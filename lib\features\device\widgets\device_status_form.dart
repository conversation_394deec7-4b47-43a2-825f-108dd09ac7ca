import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/utils/window_util.dart';
import '../../../core/widgets/custom_button.dart';
import '../models/device_model.dart';
import '../view_models/device_view_model.dart';
import 'auto_save_text_field.dart';

class DeviceStatusForm extends StatefulWidget {
  const DeviceStatusForm({Key? key}) : super(key: key);

  @override
  State<DeviceStatusForm> createState() => _DeviceStatusFormState();
}

class _DeviceStatusFormState extends State<DeviceStatusForm> {
  final _formKey = GlobalKey<FormState>();
  final _deviceMacController = TextEditingController();
  final _statusDescriptionController = TextEditingController();
  final _heartbeatIntervalController = TextEditingController();
  final _lastMaintenanceController = TextEditingController();
  DeviceStatus _selectedStatus = DeviceStatus.online;

  @override
  void initState() {
    super.initState();
    // 监听ViewModel变化，同步数据到控制器
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final viewModel = context.read<DeviceViewModel>();
      _syncFromViewModel(viewModel);
    });
  }

  @override
  void dispose() {
    _deviceMacController.dispose();
    _statusDescriptionController.dispose();
    _heartbeatIntervalController.dispose();
    _lastMaintenanceController.dispose();
    super.dispose();
  }

  void _syncFromViewModel(DeviceViewModel viewModel) {
    final data = viewModel.statusData;
    _deviceMacController.text = data.deviceMac;
    _selectedStatus = DeviceStatus.fromValue(data.status);
  }

  void _syncToViewModel() {
    final viewModel = context.read<DeviceViewModel>();
    viewModel.updateStatusData(
      deviceMac: _deviceMacController.text.trim(),
      status: _selectedStatus.value,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<DeviceViewModel>(
      builder: (context, viewModel, child) {
        return Card(
          margin: EdgeInsets.all(16.p),
          child: Padding(
            padding: EdgeInsets.all(24.p),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 标题
                  Text(
                    '设备状态上传',
                    style: TextStyle(
                      fontSize: 24.p,
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF333333),
                    ),
                  ),
                  SizedBox(height: 24.p),
                  
                  // 设备MAC输入
                  _buildDeviceMacField(),
                  
                  SizedBox(height: 24.p),
                  
                  // 状态选择
                  _buildStatusSelector(),

                  SizedBox(height: 24.p),

                  // 状态描述
                  AutoSaveTextField(
                    fieldKey: 'status_description',
                    formType: 'device_status',
                    controller: _statusDescriptionController,
                    labelText: '状态描述',
                    hintText: '请输入设备状态描述',
                    maxLines: 2,
                  ),

                  SizedBox(height: 16.p),

                  // 心跳间隔和最后维护时间
                  Row(
                    children: [
                      Expanded(
                        child: AutoSaveTextField(
                          fieldKey: 'heartbeat_interval',
                          formType: 'device_status',
                          controller: _heartbeatIntervalController,
                          labelText: '心跳间隔(秒)',
                          hintText: '请输入心跳间隔',
                          keyboardType: TextInputType.number,
                        ),
                      ),
                      SizedBox(width: 16.p),
                      Expanded(
                        child: AutoSaveTextField(
                          fieldKey: 'last_maintenance',
                          formType: 'device_status',
                          controller: _lastMaintenanceController,
                          labelText: '最后维护时间',
                          hintText: '请输入最后维护时间',
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: 32.p),
                  
                  // 上传按钮
                  SizedBox(
                    width: double.infinity,
                    child: CustomButton.filled(
                      text: viewModel.isStatusLoading ? '上传中...' : '上传状态',
                      onTap: viewModel.isStatusLoading ? null : () {
                        if (_formKey.currentState!.validate()) {
                          _syncToViewModel();
                          viewModel.uploadDeviceStatus();
                        }
                      },
                      disabled: viewModel.isStatusLoading,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildDeviceMacField() {
    return AutoSaveTextField(
      fieldKey: 'device_mac',
      formType: 'device_status',
      controller: _deviceMacController,
      labelText: '设备MAC地址 *',
      hintText: '请输入设备MAC地址',
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return '设备MAC地址不能为空';
        }
        return null;
      },
    );
  }

  Widget _buildStatusSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '设备状态 *',
          style: TextStyle(
            fontSize: 16.p,
            fontWeight: FontWeight.w500,
            color: const Color(0xFF333333),
          ),
        ),
        SizedBox(height: 12.p),
        
        // 状态选项
        Row(
          children: DeviceStatus.values.map((status) {
            return Expanded(
              child: Padding(
                padding: EdgeInsets.only(right: status != DeviceStatus.values.last ? 12.p : 0),
                child: _buildStatusOption(status),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildStatusOption(DeviceStatus status) {
    final isSelected = _selectedStatus == status;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedStatus = status;
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 16.p, horizontal: 12.p),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFF54A0FF).withOpacity(0.1) : Colors.white,
          border: Border.all(
            color: isSelected ? const Color(0xFF54A0FF) : const Color(0xFFE0E0E0),
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(8.p),
        ),
        child: Column(
          children: [
            // 状态图标
            Container(
              width: 40.p,
              height: 40.p,
              decoration: BoxDecoration(
                color: _getStatusColor(status),
                shape: BoxShape.circle,
              ),
              child: Icon(
                _getStatusIcon(status),
                color: Colors.white,
                size: 20.p,
              ),
            ),
            SizedBox(height: 8.p),
            
            // 状态文本
            Text(
              status.label,
              style: TextStyle(
                fontSize: 14.p,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                color: isSelected ? const Color(0xFF54A0FF) : const Color(0xFF333333),
              ),
            ),
            
            // 状态值
            Text(
              '(${status.value})',
              style: TextStyle(
                fontSize: 12.p,
                color: const Color(0xFF999999),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(DeviceStatus status) {
    switch (status) {
      case DeviceStatus.online:
        return const Color(0xFF52C41A); // 绿色
      case DeviceStatus.fault:
        return const Color(0xFFFA8C16); // 橙色
      case DeviceStatus.offline:
        return const Color(0xFFFF4D4F); // 红色
    }
  }

  IconData _getStatusIcon(DeviceStatus status) {
    switch (status) {
      case DeviceStatus.online:
        return Icons.check_circle;
      case DeviceStatus.fault:
        return Icons.warning;
      case DeviceStatus.offline:
        return Icons.cancel;
    }
  }
}
