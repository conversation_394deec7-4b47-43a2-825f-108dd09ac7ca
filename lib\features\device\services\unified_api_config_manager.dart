import 'dart:io';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

/// 统一API配置管理器
/// 负责管理所有API接口的配置信息，采用一层结构，存储在用户数据目录
class UnifiedApiConfigManager {
  static UnifiedApiConfigManager? _instance;
  static UnifiedApiConfigManager get instance => _instance ??= UnifiedApiConfigManager._();
  UnifiedApiConfigManager._();
  
  // 配置文件信息
  static const String _configFileName = 'api_config.json';
  static const String _configDirName = 'config';
  
  // 默认配置值
  static const String _defaultAuthBaseUrl = 'http://172.16.0.118:9000';  // 认证相关接口baseurl
  static const String _defaultBookBaseUrl = 'http://166.111.120.166:9000';  // 图书相关接口baseurl
  static const String _defaultLibraryCode = 'CN-518000-HHLIB';
  static const String _defaultDeviceMac = 'FF-FF-FF-FF-FF-FF';
  static const int _defaultTimeoutSeconds = 10;
  
  String? _configFilePath;
  Map<String, dynamic>? _cachedConfig;
  
  /// 获取配置文件路径（存储在用户数据目录）
  Future<String> _getConfigFilePath() async {
    if (_configFilePath != null) return _configFilePath!;
    
    try {
      // 获取应用数据目录
      final appDataDir = await getApplicationSupportDirectory();
      final configDir = Directory(path.join(appDataDir.path, _configDirName));
      
      // 确保配置目录存在
      if (!configDir.existsSync()) {
        configDir.createSync(recursive: true);
        debugPrint('📁 创建配置目录: ${configDir.path}');
      }
      
      _configFilePath = path.join(configDir.path, _configFileName);
      debugPrint('📄 API配置文件路径: $_configFilePath');
      return _configFilePath!;
    } catch (e) {
      debugPrint('❌ 获取配置文件路径失败: $e');
      rethrow;
    }
  }
  
  /// 获取默认配置（一层结构）
  Map<String, dynamic> _getDefaultConfig() {
    return {
      // 基础配置 - 两个不同的baseurl
      'auth_base_url': _defaultAuthBaseUrl,      // 认证相关接口baseurl
      'book_base_url': _defaultBookBaseUrl,      // 图书相关接口baseurl
      'library_code': _defaultLibraryCode,
      'device_mac': _defaultDeviceMac,
      'timeout_seconds': _defaultTimeoutSeconds,
      
      // 设备管理接口
      'device_register_endpoint': '/tunano/ldc/entrance/v1/api/door/register',
      'device_status_endpoint': '/tunano/ldc/entrance/v1/api/door/status',
      'device_auth_endpoint': '/tunano/ldc/entrance/v1/api/door/verify',
      
      // 书籍管理接口
      'book_whitelist_endpoint': '/tunano/ldc/white/tag/whitelist/contain',
      'book_uid_to_barcode_endpoint': '/tunano/rfdc/tag/v1/rfdc/tag/booktag/LibraryCode/{library_code}/Uid/{uid}',
      'book_info_query_endpoint': '/tunano/cdc/acs/v1/cdc/acs/queryBookInfo',

      // 表单数据存储
      'form_data': {
        'device_register': {
          'device_mac': _defaultDeviceMac,
          'device_name': '',
          'door_code': '',
          'door_name': '',
          'area_code': '',
          'area_name': '',
          'lib_id': '',
          'lib_name': '',
          'location': '',
          'description': '',
          'ip_address': '',
          'port': '',
        },
        'device_status': {
          'device_mac': _defaultDeviceMac,
          'status': '1',
          'status_description': '',
          'heartbeat_interval': '60',
          'last_maintenance': '',
        },
        'reader_auth': {
          'device_mac': _defaultDeviceMac,
          'auth_type': '1',
          'timeout_seconds': '30',
          'retry_count': '3',
          'reader_id': '',
          'auth_mode': '',
        },
      },

      // 元数据
      'version': '1.0.0',
      'last_updated': DateTime.now().toIso8601String(),
    };
  }
  
  /// 加载配置
  Future<Map<String, dynamic>> _loadConfig() async {
    if (_cachedConfig != null) return _cachedConfig!;
    
    try {
      final configPath = await _getConfigFilePath();
      final configFile = File(configPath);
      
      if (!configFile.existsSync()) {
        debugPrint('📄 API配置文件不存在，创建默认配置: $configPath');
        final defaultConfig = _getDefaultConfig();
        await _saveConfig(defaultConfig);
        _cachedConfig = defaultConfig;
        return defaultConfig;
      }
      
      final configContent = await configFile.readAsString();
      final config = jsonDecode(configContent) as Map<String, dynamic>;
      
      // 验证配置完整性，补充缺失的配置项
      final defaultConfig = _getDefaultConfig();
      bool needUpdate = false;
      
      for (final key in defaultConfig.keys) {
        if (!config.containsKey(key)) {
          config[key] = defaultConfig[key];
          needUpdate = true;
          debugPrint('📝 补充缺失配置项: $key = ${defaultConfig[key]}');
        }
      }
      
      if (needUpdate) {
        config['last_updated'] = DateTime.now().toIso8601String();
        await _saveConfig(config);
      }
      
      _cachedConfig = config;
      debugPrint('✅ API配置加载成功');
      return config;
    } catch (e) {
      debugPrint('❌ 加载API配置失败: $e');
      final defaultConfig = _getDefaultConfig();
      _cachedConfig = defaultConfig;
      return defaultConfig;
    }
  }
  
  /// 保存配置
  Future<void> _saveConfig(Map<String, dynamic> config) async {
    try {
      final configPath = await _getConfigFilePath();
      final configFile = File(configPath);
      
      config['last_updated'] = DateTime.now().toIso8601String();
      
      final configJson = const JsonEncoder.withIndent('  ').convert(config);
      await configFile.writeAsString(configJson);
      
      _cachedConfig = config;
      debugPrint('✅ API配置保存成功: $configPath');
    } catch (e) {
      debugPrint('❌ 保存API配置失败: $e');
      rethrow;
    }
  }
  
  /// 清除缓存，强制重新加载配置
  void clearCache() {
    _cachedConfig = null;
    debugPrint('🔄 API配置缓存已清除');
  }
  
  // ==================== 基础配置获取方法 ====================

  /// 获取认证相关接口的基础URL（读者认证、上传状态、设备注册）
  Future<String> getAuthBaseUrl() async {
    final config = await _loadConfig();
    return config['auth_base_url'] as String? ?? _defaultAuthBaseUrl;
  }

  /// 获取图书相关接口的基础URL（白名单检查、UID转条码、获取图书信息）
  Future<String> getBookBaseUrl() async {
    final config = await _loadConfig();
    return config['book_base_url'] as String? ?? _defaultBookBaseUrl;
  }

  /// 获取基础URL（保持向后兼容，默认返回认证baseurl）
  @Deprecated('请使用 getAuthBaseUrl() 或 getBookBaseUrl()')
  Future<String> getBaseUrl() async {
    return await getAuthBaseUrl();
  }
  
  /// 获取图书馆代码
  Future<String> getLibraryCode() async {
    final config = await _loadConfig();
    return config['library_code'] as String? ?? _defaultLibraryCode;
  }
  
  /// 获取设备MAC地址
  Future<String> getDeviceMac() async {
    final config = await _loadConfig();
    return config['device_mac'] as String? ?? _defaultDeviceMac;
  }
  
  /// 获取超时时间
  Future<int> getTimeoutSeconds() async {
    final config = await _loadConfig();
    return config['timeout_seconds'] as int? ?? _defaultTimeoutSeconds;
  }
  
  // ==================== 设备管理接口配置 ====================
  
  /// 获取设备注册接口端点
  Future<String> getDeviceRegisterEndpoint() async {
    final config = await _loadConfig();
    return config['device_register_endpoint'] as String? ?? '/tunano/ldc/entrance/v1/api/door/register';
  }
  
  /// 获取设备状态上传接口端点
  Future<String> getDeviceStatusEndpoint() async {
    final config = await _loadConfig();
    return config['device_status_endpoint'] as String? ?? '/tunano/ldc/entrance/v1/api/door/status';
  }
  
  /// 获取设备认证接口端点
  Future<String> getDeviceAuthEndpoint() async {
    final config = await _loadConfig();
    return config['device_auth_endpoint'] as String? ?? '/tunano/ldc/entrance/v1/api/door/verify';
  }
  
  // ==================== 书籍管理接口配置 ====================
  
  /// 获取书籍白名单检查接口端点
  Future<String> getBookWhitelistEndpoint() async {
    final config = await _loadConfig();
    return config['book_whitelist_endpoint'] as String? ?? '/tunano/ldc/white/tag/whitelist/contain';
  }
  
  /// 获取UID转条码接口端点（带参数占位符）
  Future<String> getBookUidToBarcodeEndpoint() async {
    final config = await _loadConfig();
    return config['book_uid_to_barcode_endpoint'] as String? ?? '/tunano/rfdc/tag/v1/rfdc/tag/booktag/LibraryCode/{library_code}/Uid/{uid}';
  }
  
  /// 获取图书信息查询接口端点
  Future<String> getBookInfoQueryEndpoint() async {
    final config = await _loadConfig();
    return config['book_info_query_endpoint'] as String? ?? '/tunano/cdc/acs/v1/cdc/acs/queryBookInfo';
  }
  
  // ==================== 完整URL构建方法 ====================

  /// 构建设备注册完整URL（使用认证baseurl）
  Future<String> getDeviceRegisterUrl() async {
    final baseUrl = await getAuthBaseUrl();
    final endpoint = await getDeviceRegisterEndpoint();
    return '$baseUrl$endpoint';
  }

  /// 构建设备状态上传完整URL（使用认证baseurl）
  Future<String> getDeviceStatusUrl() async {
    final baseUrl = await getAuthBaseUrl();
    final endpoint = await getDeviceStatusEndpoint();
    return '$baseUrl$endpoint';
  }

  /// 构建设备认证完整URL（使用认证baseurl）
  Future<String> getDeviceAuthUrl() async {
    final baseUrl = await getAuthBaseUrl();
    final endpoint = await getDeviceAuthEndpoint();
    return '$baseUrl$endpoint';
  }

  /// 构建书籍白名单检查完整URL（使用图书baseurl）
  Future<String> getBookWhitelistUrl() async {
    final baseUrl = await getBookBaseUrl();
    final endpoint = await getBookWhitelistEndpoint();
    return '$baseUrl$endpoint';
  }

  /// 构建UID转条码完整URL（使用图书baseurl）
  Future<String> getBookUidToBarcodeUrl(String uid) async {
    final baseUrl = await getBookBaseUrl();
    final endpoint = await getBookUidToBarcodeEndpoint();
    final libraryCode = await getLibraryCode();

    return '$baseUrl${endpoint.replaceAll('{library_code}', libraryCode).replaceAll('{uid}', uid)}';
  }

  /// 构建图书信息查询完整URL（使用图书baseurl）
  Future<String> getBookInfoQueryUrl(String barcode) async {
    final baseUrl = await getBookBaseUrl();
    final endpoint = await getBookInfoQueryEndpoint();
    final libraryCode = await getLibraryCode();
    final deviceMac = await getDeviceMac();

    return '$baseUrl$endpoint?LibraryCode=$libraryCode&Mac=$deviceMac&BookSn=$barcode';
  }
  
  // ==================== 配置更新方法 ====================

  /// 更新认证相关接口的基础URL
  Future<void> updateAuthBaseUrl(String baseUrl) async {
    final config = await _loadConfig();
    config['auth_base_url'] = baseUrl;
    await _saveConfig(config);
    debugPrint('✅ 认证baseurl已更新: $baseUrl');
  }

  /// 更新图书相关接口的基础URL
  Future<void> updateBookBaseUrl(String baseUrl) async {
    final config = await _loadConfig();
    config['book_base_url'] = baseUrl;
    await _saveConfig(config);
    debugPrint('✅ 图书baseurl已更新: $baseUrl');
  }

  /// 更新基础URL（保持向后兼容，更新认证baseurl）
  @Deprecated('请使用 updateAuthBaseUrl() 或 updateBookBaseUrl()')
  Future<void> updateBaseUrl(String baseUrl) async {
    await updateAuthBaseUrl(baseUrl);
  }
  
  /// 更新图书馆代码
  Future<void> updateLibraryCode(String libraryCode) async {
    final config = await _loadConfig();
    config['library_code'] = libraryCode;
    await _saveConfig(config);
  }
  
  /// 更新设备MAC地址
  Future<void> updateDeviceMac(String deviceMac) async {
    final config = await _loadConfig();
    config['device_mac'] = deviceMac;
    await _saveConfig(config);
  }
  
  /// 获取所有配置信息
  Future<Map<String, dynamic>> getAllConfig() async {
    return await _loadConfig();
  }
  
  /// 重置为默认配置
  Future<void> resetToDefault() async {
    final defaultConfig = _getDefaultConfig();
    await _saveConfig(defaultConfig);
    debugPrint('🔄 API配置已重置为默认值');
  }

  // ==================== 表单数据管理方法 ====================

  /// 获取表单字段值
  Future<String?> getFormFieldValue(String formType, String fieldKey) async {
    try {
      final config = await _loadConfig();
      final formData = config['form_data'] as Map<String, dynamic>?;
      if (formData == null) return null;

      final typeData = formData[formType] as Map<String, dynamic>?;
      if (typeData == null) return null;

      final value = typeData[fieldKey] as String?;
      debugPrint('📖 读取表单字段: $formType.$fieldKey = $value');
      return value;
    } catch (e) {
      debugPrint('❌ 读取表单字段失败: $formType.$fieldKey, 错误: $e');
      return null;
    }
  }

  /// 保存表单字段值（自动保存核心方法）
  Future<void> saveFormFieldValue(String formType, String fieldKey, String value) async {
    try {
      final config = await _loadConfig();

      // 确保form_data结构存在
      if (!config.containsKey('form_data')) {
        config['form_data'] = <String, dynamic>{};
      }

      final formData = config['form_data'] as Map<String, dynamic>;
      if (!formData.containsKey(formType)) {
        formData[formType] = <String, dynamic>{};
      }

      final typeData = formData[formType] as Map<String, dynamic>;
      final oldValue = typeData[fieldKey];

      // 只有值发生变化时才保存
      if (oldValue != value) {
        typeData[fieldKey] = value;

        // 特殊处理：如果是device_mac字段，同步到其他表单
        if (fieldKey == 'device_mac') {
          await _syncDeviceMacToAllForms(formData, value);
        }

        // 保存到文件
        await _saveConfig(config);
        debugPrint('💾 自动保存表单字段: $formType.$fieldKey = $value');
      }
    } catch (e) {
      debugPrint('❌ 保存表单字段失败: $formType.$fieldKey, 错误: $e');
    }
  }

  /// 同步设备MAC地址到所有表单（保证数据一致性）
  Future<void> _syncDeviceMacToAllForms(Map<String, dynamic> formData, String deviceMac) async {
    final formTypes = ['device_register', 'device_status', 'reader_auth'];

    for (final formType in formTypes) {
      if (!formData.containsKey(formType)) {
        formData[formType] = <String, dynamic>{};
      }

      final typeData = formData[formType] as Map<String, dynamic>;
      typeData['device_mac'] = deviceMac;
    }

    debugPrint('🔄 设备MAC地址已同步到所有表单: $deviceMac');
  }

  /// 获取整个表单数据
  Future<Map<String, dynamic>> getFormData(String formType) async {
    try {
      final config = await _loadConfig();
      final formData = config['form_data'] as Map<String, dynamic>?;
      if (formData == null) return {};

      final result = formData[formType] as Map<String, dynamic>? ?? {};
      debugPrint('📋 读取表单数据: $formType, 字段数量: ${result.length}');
      return result;
    } catch (e) {
      debugPrint('❌ 读取表单数据失败: $formType, 错误: $e');
      return {};
    }
  }

  /// 批量保存表单数据
  Future<void> saveFormData(String formType, Map<String, dynamic> data) async {
    try {
      final config = await _loadConfig();

      if (!config.containsKey('form_data')) {
        config['form_data'] = <String, dynamic>{};
      }

      final formData = config['form_data'] as Map<String, dynamic>;
      formData[formType] = data;

      // 如果包含device_mac，同步到其他表单
      if (data.containsKey('device_mac')) {
        await _syncDeviceMacToAllForms(formData, data['device_mac'] as String);
      }

      await _saveConfig(config);
      debugPrint('💾 批量保存表单数据: $formType, 字段数量: ${data.length}');
    } catch (e) {
      debugPrint('❌ 批量保存表单数据失败: $formType, 错误: $e');
    }
  }

  /// 清空表单数据
  Future<void> clearFormData(String formType) async {
    try {
      final config = await _loadConfig();
      final formData = config['form_data'] as Map<String, dynamic>?;

      if (formData != null && formData.containsKey(formType)) {
        formData[formType] = <String, dynamic>{};
        await _saveConfig(config);
        debugPrint('🗑️ 已清空表单数据: $formType');
      }
    } catch (e) {
      debugPrint('❌ 清空表单数据失败: $formType, 错误: $e');
    }
  }

  /// 获取所有表单数据（用于调试和导出）
  Future<Map<String, dynamic>> getAllFormData() async {
    try {
      final config = await _loadConfig();
      final formData = config['form_data'] as Map<String, dynamic>? ?? {};
      debugPrint('📊 获取所有表单数据，表单数量: ${formData.length}');
      return formData;
    } catch (e) {
      debugPrint('❌ 获取所有表单数据失败: $e');
      return {};
    }
  }
}
