import 'dart:async';
import 'package:flutter/foundation.dart';

import '../models/gate_state.dart';
import '../models/gate_command.dart';
import '../models/silence_page_state.dart';
import '../services/gate_serial_service.dart';
import '../services/rfid_service.dart';

import '../services/gate_auth_service.dart';
import '../services/enhanced_rfid_service.dart';
import '../services/sip2_book_service.dart';

import '../services/master_slave_extension.dart';
import '../services/gate_coordinator.dart';
import '../models/book_info.dart';
import '../models/gate_event.dart';
import '../../auth/models/auth_result.dart';
import '../../auth/services/multi_auth_manager.dart';

/// 静默页面ViewModel
/// 负责管理页面状态、业务逻辑和数据流
class SilencePageViewModel extends ChangeNotifier {
  // 当前状态
  GateState _currentGateState = GateState.idle;
  SilencePageState _currentPageState = SilencePageState.welcome;
  UIContentData _contentData = const UIContentData();
  
  // 服务依赖
  late GateSerialService _serialService;
  late GateAuthService _gateAuthService;
  late RFIDService _rfidService;

  late Sip2BookService _sip2BookService;

  // 事件订阅
  StreamSubscription? _commandSubscription;
  StreamSubscription? _authSubscription;
  StreamSubscription? _rfidBarcodeSubscription;
  StreamSubscription? _rfidCountSubscription;
  StreamSubscription? _serialErrorSubscription;
  StreamSubscription? _rfidErrorSubscription;

  // 🔥 新增：主从机相关订阅和定时器
  StreamSubscription? _masterSlaveDataSubscription;
  Timer? _masterSlaveDataTimer;
  
  // 状态超时控制
  Timer? _stateTimeoutTimer;
  Timer? _autoRecoverTimer;
  static const int _stateTimeoutSeconds = 30;
  
  // RFID扫描数据
  List<String> _scannedBarcodes = [];

  // 🔥 新增：主从机模式标识
  bool _isMasterSlaveMode = false;
  bool _isMasterMode = true;

  // 🔥 新增：防重复处理标志
  bool _isProcessingExit = false;

  // 当前认证用户信息
  String? _currentUserName;
  
  // Getters
  GateState get currentGateState => _currentGateState;
  SilencePageState get currentPageState => _currentPageState;
  UIContentData get contentData => _contentData;
  bool get showDefaultContent => _currentPageState.showDefaultContent;
  List<String> get scannedBarcodes => List.from(_scannedBarcodes);
  
  /// 初始化ViewModel
  Future<void> initialize() async {
    try {
      debugPrint('开始初始化SilencePageViewModel...');
      
      // 1. 初始化服务
      await _initializeServices();
      
      // 2. 设置事件监听
      _setupEventListeners();
      
      // 3. 启动串口监听
      await _serialService.startListening();
      
      debugPrint('SilencePageViewModel初始化完成');
    } catch (e) {
      debugPrint('SilencePageViewModel初始化失败: $e');
      _updatePageState(SilencePageState.error, 
          UIContentData.error(message: '系统初始化失败: $e'));
      rethrow;
    }
  }
  
  /// 初始化服务
  Future<void> _initializeServices() async {
    // 初始化串口服务
    _serialService = GateSerialService.instance;
    await _serialService.initialize();

    // 初始化闸机认证服务
    _gateAuthService = GateAuthService.instance;
    await _gateAuthService.initialize();

    // 初始化RFID服务
    _rfidService = RFIDService.instance;
    await _rfidService.initialize();



    // 🔥 新增：初始化SIP2书籍服务
    _sip2BookService = Sip2BookService.instance;
    _sip2BookService.initialize();

    // 🔥 新增：主从机扩展已准备就绪
    // 不再自动启用，改为手动配置
    debugPrint('💡 主从机扩展已准备就绪，请通过配置页面手动启用');
    debugPrint('💡 可以通过MasterSlaveConfigPage进行配置');

    // 🔥 修改：延迟检测主从机模式，确保GateConfigService已完成初始化
    Timer(const Duration(milliseconds: 500), () {
      _detectMasterSlaveMode();
    });
  }

  /// 🔥 新增：检测主从机模式
  void _detectMasterSlaveMode() {
    try {
      final extension = MasterSlaveExtension.instance;
      _isMasterSlaveMode = extension.isEnabled;
      _isMasterMode = extension.isMaster;

      debugPrint('🔍 主从机模式检测: 启用=$_isMasterSlaveMode, 主机模式=$_isMasterMode');
      debugPrint('🔍 扩展详细状态: ${extension.getDetailedStatus()}');

      // 🔥 修改：如果是从机模式，立即设置数据监听
      if (_isMasterSlaveMode && !_isMasterMode) {
        debugPrint('🎯 检测到从机模式，设置数据监听');
        _setupMasterSlaveDataListener();
      } else if (_isMasterSlaveMode && _isMasterMode) {
        debugPrint('🎯 检测到主机模式，无需设置额外监听');
      } else {
        debugPrint('🎯 主从机扩展未启用或配置异常');
      }
    } catch (e) {
      debugPrint('⚠️ 主从机模式检测失败: $e');
      _isMasterSlaveMode = false;
      _isMasterMode = true;
    }
  }

  /// 🔥 重构：设置从机持续数据监听
  void _setupMasterSlaveDataListener() {
    if (!_isMasterSlaveMode || _isMasterMode) return;

    try {
      final extension = MasterSlaveExtension.instance;

      debugPrint('🔧 开始设置从机持续数据监听...');

      // 🔥 重新启用数据流监听（用于持续数据获取）
      _masterSlaveDataSubscription = extension.dataChangeStream.listen(
        (barcodes) {
          debugPrint('🎯 持续数据监听触发: ${barcodes.length}个条码');
          _handleMasterSlaveData(barcodes);
        },
        onError: (error) {
          debugPrint('❌ 持续数据监听错误: $error');
        },
        onDone: () {
          debugPrint('⚠️ 持续数据监听结束');
        },
      );

      debugPrint('✅ 从机持续数据监听已设置');
    } catch (e) {
      debugPrint('⚠️ 设置从机持续数据监听失败: $e');
    }
  }

  /// 🔥 移除备用检查：只依赖数据流单一路径

  /// 🔥 新增：处理主从机数据（从机模式）
  void _handleMasterSlaveData(List<String> data) {
    debugPrint('🎯 _handleMasterSlaveData 被调用: ${data.length}个条码');
    debugPrint('📊 当前模式: 主从机=$_isMasterSlaveMode, 主机=$_isMasterMode');
    debugPrint('📊 当前页面状态: $_currentPageState');

    if (!_isMasterSlaveMode || _isMasterMode) {
      debugPrint('⚠️ 跳过处理: 不是从机模式');
      return;
    }

    debugPrint('📥 从机接收到数据: ${data.length}个条码 - $data');

    // 更新扫描条码列表
    final oldCount = _scannedBarcodes.length;
    _scannedBarcodes = List.from(data);
    debugPrint('📊 扫描列表更新: $oldCount -> ${_scannedBarcodes.length}');

    // 🔥 修复：更新页面显示（扩展状态检查范围）
    if (_currentPageState == SilencePageState.rfidScanning ||
        _currentPageState == SilencePageState.waitingExit ||
        _currentPageState == SilencePageState.authenticating) {
      debugPrint('📱 更新页面显示: ${_scannedBarcodes.length}个条码');
      _updatePageState(SilencePageState.rfidScanning,
          UIContentData.rfidScanning(scannedCount: _scannedBarcodes.length));
    } else {
      debugPrint('⚠️ 页面状态不是扫描相关状态，跳过页面更新: $_currentPageState');
    }

    notifyListeners();
    debugPrint('✅ 从机数据处理完成');
  }

  /// 🔥 重构：设置事件监听（统一架构：只监听GateCoordinator事件）
  void _setupEventListeners() {
    // 🔥 新架构：只监听GateCoordinator的事件流，实现统一的处理管道
    final coordinator = GateCoordinator.instance;
    _commandSubscription = coordinator.eventStream.listen(
      _handleGateEvent,
      onError: (error) {
        debugPrint('GateCoordinator事件流错误: $error');
        _handleError('闸机事件处理错误: $error');
      },
    );

    // 🔥 保留：监听串口错误（用于错误处理）
    _serialErrorSubscription = _serialService.errorStream.listen(
      (error) {
        debugPrint('串口错误: $error');
        _handleSerialError(error);
      },
    );

    debugPrint('✅ 统一事件监听已设置：SilencePageViewModel → GateCoordinator.eventStream');
    
    // 监听RFID扫描结果
    _rfidBarcodeSubscription = _rfidService.barcodeStream.listen(
      _handleRFIDBarcode,
    );

    _rfidCountSubscription = _rfidService.countStream.listen(
      _handleRFIDCount,
    );

    // 🔥 新增：监听书籍扫描结果
    _rfidService.bookResultStream.listen(
      _handleBookScanResult,
      onError: (error) {
        debugPrint('书籍扫描结果流错误: $error');
      },
    );

    _rfidErrorSubscription = _rfidService.errorStream.listen(
      (error) {
        debugPrint('RFID错误: $error');
        _handleError('RFID扫描错误: $error');
      },
    );
  }
  
  /// 🔥 新增：处理GateCoordinator事件（统一架构的核心）
  void _handleGateEvent(GateEvent event) {
    debugPrint('📨 收到GateCoordinator事件: ${event.type}');

    // 取消状态超时（保持原有的超时管理）
    _cancelStateTimeout();
    _cancelAutoRecover();

    // 🔥 修复：根据事件类型更新UI状态（正确的流程逻辑）
    switch (event.type) {
      case GateEvent.enterStart:
        // ✅ 正确：进馆开始时显示等待到位状态，不是认证状态
        _updatePageState(SilencePageState.waitingEnter, UIContentData.waitingEnter());
        break;
      case GateEvent.exitStart:
        // ✅ 正确：出馆开始时显示等待到位状态，不是认证状态
        _updatePageState(SilencePageState.waitingExit, UIContentData.waitingExit());
        break;
      case GateEvent.authSuccess:
        final userName = event.data['userName'] ?? '未知用户';
        _updatePageState(SilencePageState.authSuccess,
            UIContentData.authSuccess(userName: userName, message: '$userName，同学 欢迎光临'));
        break;
      case GateEvent.authFailed:
        final message = event.data['message'] ?? '认证失败';
        _updatePageState(SilencePageState.authFailed,
            UIContentData.authFailed(message: message));
        break;
      case GateEvent.bookScanned:
        final barcode = event.data['barcode'] ?? '';
        final totalCount = event.data['total_count'] ?? 0;
        _handleBookScannedEvent(barcode, totalCount);
        break;
      case GateEvent.exitAllowed:
        final message = event.data['message'] ?? '检查通过，请通过';
        final books = event.data['books'] as List<Map<String, dynamic>>? ?? [];
        final bookInfos = books.map((bookData) => BookInfo.fromJson(bookData)).toList();
        _updatePageState(SilencePageState.exitAllowed,
            UIContentData.bookCheck(books: bookInfos, hasUnborrowedBooks: false));
        break;
      case GateEvent.exitBlocked:
        final message = event.data['message'] ?? '检查未通过';
        final books = event.data['books'] as List<Map<String, dynamic>>? ?? [];
        final bookInfos = books.map((bookData) => BookInfo.fromJson(bookData)).toList();
        _updatePageState(SilencePageState.exitBlocked,
            UIContentData.bookCheck(books: bookInfos, hasUnborrowedBooks: true));
        break;
      case GateEvent.stateChanged:
        final newStateName = event.data['new_state'] ?? '';
        _updateGateStateFromName(newStateName);
        // 🔥 新增：根据状态变更显示对应的UI
        _handleStateChangedUI(newStateName);
        break;
      case GateEvent.enterEnd:
        _updatePageState(SilencePageState.welcome, UIContentData.authenticating());
        break;
      case GateEvent.exitEnd:
        _handleExitEndEvent();
        break;
      case GateEvent.error:
        final message = event.data['message'] ?? '系统错误';
        _handleError(message);
        break;
      case GateEvent.pageClear:
        _handlePageClearEvent();
        break;
      default:
        debugPrint('未处理的GateCoordinator事件: ${event.type}');
    }
  }

  /// 🔥 新增：处理书籍扫描事件
  void _handleBookScannedEvent(String barcode, int totalCount) {
    if (!_scannedBarcodes.contains(barcode)) {
      _scannedBarcodes.add(barcode);
    }

    // 更新UI显示扫描状态
    _updatePageState(SilencePageState.rfidScanning,
        UIContentData.rfidScanning(
          scannedCount: totalCount,
          booksInfo: {}, // 简化处理
        ));
  }

  /// 🔥 新增：根据状态名称更新闸机状态
  void _updateGateStateFromName(String stateName) {
    try {
      final newState = GateState.values.firstWhere((state) => state.name == stateName);
      _updateGateState(newState);
    } catch (e) {
      debugPrint('无法解析状态名称: $stateName');
    }
  }

  /// 🔥 新增：根据状态变更处理UI显示
  void _handleStateChangedUI(String stateName) {
    debugPrint('🎨 处理状态变更UI: $stateName');

    switch (stateName) {
      case 'enterScanning':
        // 进馆扫描状态 → 显示认证组件
        _updatePageState(SilencePageState.authenticating, UIContentData.authenticating());
        break;
      case 'exitScanning':
        // 出馆扫描状态 → 显示RFID扫描组件（使用当前扫描数量）
        _updatePageState(SilencePageState.rfidScanning,
            UIContentData.rfidScanning(scannedCount: _scannedBarcodes.length));
        break;
      case 'idle':
        // 空闲状态 → 显示欢迎界面
        _updatePageState(SilencePageState.welcome, UIContentData.authenticating());
        break;
      default:
        debugPrint('未处理的状态变更UI: $stateName');
    }
  }

  /// 🔥 新增：处理页面清空事件
  void _handlePageClearEvent() {
    debugPrint('📱 处理页面清空事件');

    // 清空扫描条码列表
    _scannedBarcodes.clear();

    // 更新页面显示为0个条码
    if (_currentPageState == SilencePageState.rfidScanning ||
        _currentPageState == SilencePageState.waitingExit ||
        _currentPageState == SilencePageState.authenticating) {
      _updatePageState(SilencePageState.rfidScanning,
          UIContentData.rfidScanning(scannedCount: 0));
      debugPrint('📱 页面已清空并更新显示为0个条码');
    }

    notifyListeners();
  }

  /// 🔥 新增：处理出馆结束事件
  void _handleExitEndEvent() {
    debugPrint('📱 处理出馆结束事件');

    try {
      // 1. 清空扫描条码列表
      debugPrint('🧹 清空扫描条码列表...');
      _scannedBarcodes.clear();

      // 2. 停止主从机数据监听（如果是从机）
      if (_isMasterSlaveMode && !_isMasterMode) {
        debugPrint('⏹️ 停止从机数据监听...');
        _stopMasterSlaveDataListener();
      }

      // 3. 更新页面状态为欢迎界面
      debugPrint('🏠 更新页面状态为欢迎界面...');
      _updatePageState(SilencePageState.welcome, UIContentData.authenticating());

      debugPrint('✅ 出馆结束事件处理完成');

    } catch (e) {
      debugPrint('❌ 处理出馆结束事件失败: $e');
      // 即使出错也要回到欢迎界面
      _updatePageState(SilencePageState.welcome, UIContentData.authenticating());
    }
  }
  
  /// 进馆开始处理
  Future<void> _handleEnterStart() async {
    debugPrint('处理进馆开始');

    _updateGateState(GateState.enterStarted);
    _updatePageState(SilencePageState.authenticating, UIContentData.authenticating());

    // 启动闸机认证服务（无UI模式）
    try {
      // 检查MultiAuthManager状态
      final multiAuthManager = MultiAuthManager.instance;
      debugPrint('MultiAuthManager当前状态: ${multiAuthManager.state}');

      // idle状态表示已初始化且准备就绪，可以直接启动认证服务
      if (multiAuthManager.state == MultiAuthState.error) {
        debugPrint('MultiAuthManager处于错误状态，无法启动认证');
        throw Exception('认证系统处于错误状态，请重启应用');
      }

      // 现在启动闸机认证服务
      // 注意：使用与MultiAuthManager初始化时相同的认证方式
      await _gateAuthService.startGateAuth(
        onResult: _handleGateAuthResult,
        onError: (error) {
          debugPrint('认证服务错误: $error');
          _handleError('认证系统错误: $error');
        },
        onStateChanged: (state) {
          debugPrint('认证状态变化: $state');
        },
        enabledMethods: [
          AuthMethod.face,                    // 人脸识别
          AuthMethod.readerCard,              // 读者证
          AuthMethod.wechatScanQRCode,        // 微信扫码（替代qrCode）
        ],
        timeoutSeconds: 30,
      );

      _updateGateState(GateState.enterScanning);
      _setStateTimeout();

      debugPrint('闸机认证服务启动成功');
    } catch (e) {
      debugPrint('启动认证系统失败: $e');
      _handleError('启动认证系统失败: $e');
    }
  }
  
  /// 🔥 修改：处理闸机认证结果（区分进馆和出馆）
  void _handleGateAuthResult(AuthResult result) {
    if (_currentGateState == GateState.enterScanning) {
      // 进馆认证处理（保持现有逻辑）
      _handleEnterAuthResult(result);
    } else if (_currentGateState == GateState.exitStarted) {
      // 🔥 新增：出馆认证处理
      _handleExitAuthResult(result);
    } else {
      debugPrint('非认证状态收到认证结果，忽略。当前状态: $_currentGateState');
      return;
    }
  }

  /// 🔥 新增：处理进馆认证结果
  void _handleEnterAuthResult(AuthResult result) {
    debugPrint('收到进馆认证结果: ${result.status}, 用户: ${result.userName}');

    // 取消状态超时
    _cancelStateTimeout();

    // 🔥 新增：无论认证成功还是失败，立即停止所有认证服务
    debugPrint('进馆认证结果已产生，立即停止所有认证服务');
    _gateAuthService.stopGateAuth();

    if (result.status == AuthStatus.success) {
      // 存储当前用户信息
      _currentUserName = result.userName;

      // 认证成功
      _updateGateState(GateState.enterOpening);
      _updatePageState(SilencePageState.authSuccess,
          UIContentData.authSuccess(
            userName: result.userName ?? '未知用户',
            message: '${result.userName ?? '未知用户'}，同学 欢迎光临'
          ));

      // 发送开门命令
      _serialService.sendCommand('enter_open');

      // 设置自动恢复
      _setAutoRecover();

    } else {
      // 认证失败
      String errorMessage = result.errorMessage ?? '认证失败';

      // 根据失败类型显示不同消息
      if (result.status == AuthStatus.failureTimeout) {
        errorMessage = '认证超时，请重试';
      } else if (result.status == AuthStatus.failureNoMatch) {
        errorMessage = '未找到读者信息，请检查证件';
      } else if (result.status == AuthStatus.failureError) {
        errorMessage = '认证系统错误，请重试';
      }

      _updatePageState(SilencePageState.authFailed,
          UIContentData.authFailed(message: errorMessage));

      // 设置自动恢复，允许重新认证
      _setAutoRecover();
    }
  }

  /// 🔥 新增：处理出馆认证结果
  void _handleExitAuthResult(AuthResult result) {
    debugPrint('收到出馆认证结果: ${result.status}, 用户: ${result.userName}');

    // 取消状态超时
    _cancelStateTimeout();

    // 停止认证服务
    _gateAuthService.stopGateAuth();

    // 🔥 根据新需求：出馆认证不关注具体结果，请求成功就继续RFID扫描流程
    debugPrint('出馆认证完成，开始RFID扫描流程');

    // 继续RFID扫描流程
    _handleExitAuthComplete(result);
  }

  /// 进馆结束处理
  Future<void> _handleEnterEnd() async {
    debugPrint('处理进馆结束');

    // 停止认证服务
    await _gateAuthService.stopGateAuth();

    _updateGateState(GateState.enterOver);

    // 短暂延迟后回到欢迎界面
    Timer(const Duration(seconds: 1), () {
      _resetToWelcome();
    });
  }
  
  /// 🔥 修改：出馆开始处理 - 先进行身份认证
  Future<void> _handleExitStart() async {
    debugPrint('处理出馆开始');

    _updateGateState(GateState.exitStarted);
    // 🔥 修改：显示认证界面，而不是直接显示RFID扫描界面
    _updatePageState(SilencePageState.authenticating, UIContentData.authenticating());

    // 🔥 修复：清空扫描结果并立即更新页面显示为0
    _scannedBarcodes.clear();

    // 🔥 关键修复：立即更新页面显示为0个条码
    if (_currentPageState == SilencePageState.rfidScanning ||
        _currentPageState == SilencePageState.waitingExit ||
        _currentPageState == SilencePageState.authenticating) {
      _updatePageState(SilencePageState.rfidScanning,
          UIContentData.rfidScanning(scannedCount: 0));
      debugPrint('📱 主机已清空扫描结果并更新页面显示为0个条码');
    }

    notifyListeners();

    // 🔥 修改：启动出馆认证系统，而不是直接开始RFID扫描
    try {
      await _gateAuthService.startGateAuth(
        onResult: _handleGateAuthResult,
        onError: (error) {
          debugPrint('出馆认证服务错误: $error');
          // 🔥 根据新需求：出馆认证失败也继续RFID扫描流程
          _handleExitAuthComplete(null);
        },
        onStateChanged: (state) {
          debugPrint('出馆认证状态变化: $state');
        },
        enabledMethods: [
          AuthMethod.face,
          AuthMethod.readerCard,
          AuthMethod.wechatScanQRCode,
        ],
        timeoutSeconds: 15, // 出馆认证超时时间可以更短
      );

      debugPrint('出馆认证服务启动成功');
    } catch (e) {
      debugPrint('启动出馆认证系统失败: $e');
      // 🔥 出馆认证启动失败也继续RFID扫描流程
      _handleExitAuthComplete(null);
    }
  }

  /// 🔥 新增：处理出馆认证完成
  Future<void> _handleExitAuthComplete(AuthResult? result) async {
    debugPrint('出馆认证完成，开始RFID扫描流程');

    // 更新状态为出馆扫描
    _updateGateState(GateState.exitScanning);
    _updatePageState(SilencePageState.rfidScanning,
        UIContentData.rfidScanning(scannedCount: _scannedBarcodes.length));

    // 🔥 优化：防重复处理检查
    if (_isProcessingExit) {
      debugPrint('⚠️ 出馆处理中，跳过重复调用');
      return;
    }

    _isProcessingExit = true;
    try {
      // 🔥 重构：根据主从机模式选择不同的处理方式（持续数据获取模式）
      if (_isMasterSlaveMode && !_isMasterMode) {
        // 🔥 从机模式：启动持续数据获取
        try {
          debugPrint('从机模式：启动持续数据获取...');

          final extension = MasterSlaveExtension.instance;
          // 清空之前收集的数据
          extension.clearCollectedData();
          // 启动持续数据获取（包含清空请求）
          await extension.handleExitStart();

          // 设置超时，等待到达指定位置
          _setStateTimeout();
          debugPrint('✅ 从机持续数据获取已启动，等待到达指定位置...');

        } catch (e) {
          debugPrint('从机模式出馆开始失败: $e');
          _handleError('从机模式出馆开始失败: $e');
        }
      } else {
        // 主机模式或非主从机模式：启动本地RFID扫描
        try {
          await _rfidService.startDataCollection();
          _setStateTimeout();
        } catch (e) {
          debugPrint('启动RFID扫描失败: $e');
          _handleError('启动RFID扫描失败: $e');
        }
      }
    } finally {
      _isProcessingExit = false;
    }
  }

  /// 处理RFID条码扫描
  void _handleRFIDBarcode(String barcode) {
    // 🔥 修改：从机模式下忽略本地RFID扫描
    if (_isMasterSlaveMode && !_isMasterMode) {
      debugPrint('⚠️ 从机模式：忽略本地RFID扫描 $barcode');
      return;
    }

    if (!_scannedBarcodes.contains(barcode)) {
      _scannedBarcodes.add(barcode);
      debugPrint('扫描到新条码: $barcode (总计: ${_scannedBarcodes.length})');
    }
  }

  /// 处理RFID扫描数量变化
  void _handleRFIDCount(int count) {
    // 🔥 修改：从机模式下忽略本地RFID计数
    if (_isMasterSlaveMode && !_isMasterMode) {
      debugPrint('⚠️ 从机模式：忽略本地RFID计数 $count');
      return;
    }

    if (_currentPageState == SilencePageState.rfidScanning) {
      _updatePageState(SilencePageState.rfidScanning,
          UIContentData.rfidScanning(scannedCount: count));
    }
  }

  /// 🔥 新增：处理书籍扫描结果
  void _handleBookScanResult(BookScanResult result) {
    // 根据状态处理不同的情况
    switch (result.status) {
      case BookScanStatus.processing:
        debugPrint('正在获取书籍信息: ${result.barcode}');
        break;
      case BookScanStatus.success:
        if (result.bookInfo != null) {
          debugPrint('获取书籍信息: ${result.barcode} - ${result.bookInfo!.bookName} - ${result.bookInfo!.borrowStatusText}');
        }
        break;
      case BookScanStatus.failed:
        debugPrint('获取书籍信息失败: ${result.barcode} - ${result.error ?? "未知错误"}');
        break;
      case BookScanStatus.notFound:
        debugPrint('未找到书籍信息: ${result.barcode} - 可能是其他馆的条码，将被忽略');
        break;
    }

    // 如果当前在扫描状态，更新UI显示
    if (_currentPageState == SilencePageState.rfidScanning) {
      _updatePageState(SilencePageState.rfidScanning,
          UIContentData.rfidScanning(
            scannedCount: _scannedBarcodes.length,
            booksInfo: {}, // 不再使用缓存
          ));
    }

    // 通知监听者更新
    notifyListeners();
  }
  
  /// 到达指定位置处理
  Future<void> _handlePositionReached() async {
    if (_currentGateState == GateState.exitScanning) {
      debugPrint('用户到达指定位置，准备停止RFID扫描');
      
      _updateGateState(GateState.exitChecking);

      // 直接停止扫描并检查书籍，无延迟
      await _stopRFIDAndCheckBooks();
    }
  }
  
  /// 停止RFID扫描并检查书籍
  Future<void> _stopRFIDAndCheckBooks() async {
    try {
      // 🔥 修改：根据主从机模式选择不同的停止方式
      if (_isMasterSlaveMode && !_isMasterMode) {
        // 从机模式：延迟2秒后停止持续数据获取
        debugPrint('从机模式：到达指定位置，延迟2秒后停止数据获取...');

        // 延迟2秒
        await Future.delayed(const Duration(seconds: 2));

        // 停止持续数据获取
        final extension = MasterSlaveExtension.instance;
        extension.stopContinuousDataCollection();

        // 获取最终收集的数据
        _scannedBarcodes = extension.getCurrentCollectedData();
        debugPrint('从机模式：数据获取已停止，共收集到${_scannedBarcodes.length}本书 - $_scannedBarcodes');
      } else {
        // 主机模式或非主从机模式：停止本地RFID扫描
        final finalBarcodes = await _rfidService.stopDataCollection();
        _scannedBarcodes = finalBarcodes;
        debugPrint('RFID扫描结束，共扫描到${_scannedBarcodes.length}本书');
      }

      if (_scannedBarcodes.isEmpty) {
        // 没有扫描到书籍，允许通过
        _updatePageState(SilencePageState.exitAllowed,
            UIContentData.authSuccess(
              userName: _currentUserName ?? '用户',
              message: '${_currentUserName ?? '用户'}，同学 欢迎再次光临'
            ));
        _serialService.sendCommand('exit_open');
        _setAutoRecover();
      } else {
        // 检查书籍状态
        await _checkBooksStatus();
      }
    } catch (e) {
      debugPrint('停止RFID扫描失败: $e');
      _handleError('书籍检查失败: $e');
    }
  }

  /// 🔥 新增：停止主从机数据监听
  void _stopMasterSlaveDataListener() {
    _masterSlaveDataTimer?.cancel();
    _masterSlaveDataTimer = null;
    debugPrint('✅ 从机数据监听已停止');
  }
  
  /// 🔥 修改：检查书籍状态（使用新的SIP2逻辑）
  Future<void> _checkBooksStatus() async {
    try {
      debugPrint('开始检查书籍状态，共${_scannedBarcodes.length}本书');

      if (_scannedBarcodes.isEmpty) {
        // 没有扫描到书籍，允许通过
        _updatePageState(SilencePageState.exitAllowed,
            UIContentData.authSuccess(
              userName: _currentUserName ?? '用户',
              message: '${_currentUserName ?? '用户'}，同学 欢迎再次光临'
            ));
        _serialService.sendCommand('exit_open');
        _setAutoRecover();
        return;
      }

      // 🔥 使用新的SIP2实时检查逻辑
      List<BookInfo> validBooks = [];
      List<String> ignoredBarcodes = [];

      for (String barcode in _scannedBarcodes) {
        debugPrint('实时检查条码: $barcode');

        // 使用SIP2实时获取书籍信息
        final bookInfo = await _sip2BookService.getBookInfoRealTime(barcode);

        if (bookInfo != null) {
          // 找到书籍信息，加入检查列表
          validBooks.add(bookInfo);
          debugPrint('条码 $barcode 找到书籍信息: ${bookInfo.bookName}');
        } else {
          // 没有找到书籍信息，忽略（可能是其他馆的条码）
          ignoredBarcodes.add(barcode);
          debugPrint('条码 $barcode 未找到书籍信息，忽略（可能是其他馆的条码）');
        }
      }

      debugPrint('检查结果: 有效书籍${validBooks.length}本，忽略条码${ignoredBarcodes.length}个');

      // 🔥 新的判断逻辑：只对有效书籍进行检查
      if (validBooks.isEmpty) {
        // 所有条码都被忽略，允许通过
        debugPrint('所有条码都是其他馆的，允许通过');
        _updatePageState(SilencePageState.exitAllowed,
            UIContentData.authSuccess(
              userName: _currentUserName ?? '用户',
              message: '${_currentUserName ?? '用户'}，同学 欢迎再次光临'
            ));
        _serialService.sendCommand('exit_open');
      } else {
        // 检查有效书籍的借阅状态
        final result = BookCheckResult(books: validBooks);

        if (result.allowPass) {
          // 所有有效书籍都已借阅，允许通过
          _updatePageState(SilencePageState.exitAllowed,
              UIContentData.authSuccess(
                userName: _currentUserName ?? '用户',
                message: '${_currentUserName ?? '用户'}，同学 欢迎再次光临'
              ));
          _serialService.sendCommand('exit_open');
        } else {
          // 有未借书籍，禁止通过
          _updatePageState(SilencePageState.exitBlocked,
              UIContentData.bookCheck(books: validBooks, hasUnborrowedBooks: true));
        }
      }

      _setAutoRecover();
    } catch (e) {
      debugPrint('检查书籍状态失败: $e');
      // 出错时默认允许通过，避免阻挡用户
      _updatePageState(SilencePageState.exitAllowed,
          UIContentData.authSuccess(
            userName: _currentUserName ?? '用户',
            message: '系统异常，默认允许通过'
          ));
      _serialService.sendCommand('exit_open');
      _setAutoRecover();
    }
  }
  
  /// 出馆结束处理
  Future<void> _handleExitEnd() async {
    debugPrint('处理出馆结束');
    
    _updateGateState(GateState.exitOver);
    
    // 确保停止RFID扫描
    if (_rfidService.isScanning) {
      await _rfidService.stopScanning();
    }
    
    // 短暂延迟后回到欢迎界面
    Timer(const Duration(seconds: 1), () {
      _resetToWelcome();
    });
  }
  
  /// 处理尾随检测
  void _handleTailgating() {
    debugPrint('检测到尾随');
    // 可以显示警告信息，但不改变主要状态
  }
  
  /// 处理通道阻挡
  void _handleDoorBlocked() {
    debugPrint('检测到开门有人');
    // 可以显示警告信息，但不改变主要状态
  }
  
  /// 处理串口错误（不影响系统状态）
  void _handleSerialError(String errorMessage) {
    debugPrint('串口错误（不影响系统功能）: $errorMessage');

    // 检查是否为硬件发送失败相关的错误
    if (errorMessage.contains('SEND_INCOMPLETE') ||
        errorMessage.contains('发送数据失败') ||
        errorMessage.contains('发送闸机命令失败') ||
        errorMessage.contains('硬件可能未连接')) {
      // 这些错误在没有真实硬件时是正常的，不影响系统功能
      debugPrint('检测到硬件相关错误，在模拟环境中忽略: $errorMessage');
      return;
    }

    // 对于其他类型的串口错误，记录但不设置系统为错误状态
    debugPrint('其他串口错误，记录但不影响系统状态: $errorMessage');
  }

  /// 处理系统错误
  void _handleError(String errorMessage) {
    debugPrint('系统错误: $errorMessage');

    _updateGateState(GateState.error);
    _updatePageState(SilencePageState.error,
        UIContentData.error(message: errorMessage));

    // 停止所有活动
    _stopAllActivities();

    // 5秒后自动恢复
    Timer(const Duration(seconds: 5), () {
      _resetToWelcome();
    });
  }
  
  /// 停止所有活动
  void _stopAllActivities() {
    // 停止认证服务
    _gateAuthService.stopGateAuth();

    // 停止RFID扫描
    if (_rfidService.isScanning) {
      _rfidService.stopScanning();
    }

    _cancelStateTimeout();
    _cancelAutoRecover();
  }
  
  /// 更新闸机状态
  void _updateGateState(GateState newState) {
    if (_currentGateState != newState) {
      _currentGateState = newState;
      debugPrint('闸机状态变更: $newState');
      notifyListeners();
    }
  }
  
  /// 更新页面状态
  void _updatePageState(SilencePageState newState, [UIContentData? data]) {
    _currentPageState = newState;
    if (data != null) {
      _contentData = data;
    }
    debugPrint('页面状态变更: $newState');
    notifyListeners();
  }
  
  /// 🔥 增强：重置到欢迎界面
  void _resetToWelcome() {
    _stopAllActivities();
    _scannedBarcodes.clear();
    _updateGateState(GateState.idle);
    _updatePageState(SilencePageState.welcome, const UIContentData());
  }
  
  /// 设置状态超时
  void _setStateTimeout() {
    _cancelStateTimeout();
    _stateTimeoutTimer = Timer(const Duration(seconds: _stateTimeoutSeconds), () {
      debugPrint('状态超时，自动重置');
      _resetToWelcome();
    });
  }
  
  /// 取消状态超时
  void _cancelStateTimeout() {
    _stateTimeoutTimer?.cancel();
    _stateTimeoutTimer = null;
  }
  
  /// 设置自动恢复
  void _setAutoRecover() {
    if (_currentPageState.isTemporaryState) {
      _cancelAutoRecover();
      final seconds = _currentPageState.autoRecoverSeconds;
      if (seconds > 0) {
        _autoRecoverTimer = Timer(Duration(seconds: seconds), () {
          _resetToWelcome();
        });
      }
    }
  }
  
  /// 取消自动恢复
  void _cancelAutoRecover() {
    _autoRecoverTimer?.cancel();
    _autoRecoverTimer = null;
  }
  
  /// 手动重置系统
  void resetSystem() {
    debugPrint('手动重置系统');
    _resetToWelcome();
  }
  
  /// 🔥 重构：模拟串口命令（委托给GateCoordinator处理）
  void simulateSerialCommand(String commandType) {
    try {
      debugPrint('🎮 SilencePageViewModel: 委托命令给GateCoordinator: $commandType');

      // 🔥 新架构：直接调用GateCoordinator处理命令，避免重复逻辑
      final coordinator = GateCoordinator.instance;
      coordinator.simulateSerialCommand(commandType);

      debugPrint('✅ 命令已委托给GateCoordinator处理');
    } catch (e) {
      debugPrint('❌ 委托命令处理失败: $e');
    }
  }

  // 🔥 移除：不再需要的主从机相关方法，由GateCoordinator统一处理
  
  /// 获取系统状态
  Map<String, dynamic> getSystemStatus() {
    return {
      'gate_state': _currentGateState.name,
      'page_state': _currentPageState.name,
      'scanned_count': _scannedBarcodes.length,
      'serial_status': _serialService.getStatus(),
      'rfid_status': _rfidService.getStatus(),
      'sip2_book_status': 'initialized',
      'timestamp': DateTime.now().toIso8601String(),
    };
  }
  
  @override
  void dispose() {
    debugPrint('释放SilencePageViewModel资源');

    _commandSubscription?.cancel();
    _authSubscription?.cancel();
    _rfidBarcodeSubscription?.cancel();
    _rfidCountSubscription?.cancel();
    _serialErrorSubscription?.cancel();
    _rfidErrorSubscription?.cancel();

    // 🔥 新增：清理主从机相关资源
    _masterSlaveDataSubscription?.cancel();
    _stopMasterSlaveDataListener();

    _cancelStateTimeout();
    _cancelAutoRecover();

    _stopAllActivities();

    // 🔥 新增：清理主从机扩展
    try {
      MasterSlaveExtension.instance.disable();
      debugPrint('✅ 主从机扩展已清理');
    } catch (e) {
      debugPrint('⚠️ 清理主从机扩展失败: $e');
    }

    super.dispose();

    debugPrint('SilencePageViewModel已释放');
  }

  /// 🔥 新增：发送闸机命令（用于测试按钮）
  void sendGateCommand(String commandType) {
    try {
      debugPrint('🎮 测试发送闸机命令: $commandType');

      // 直接使用串口服务发送命令
      _serialService.sendCommand(commandType);

      debugPrint('✅ 闸机命令 $commandType 已发送');
    } catch (e) {
      debugPrint('❌ 发送闸机命令失败: $e');
    }
  }
}
