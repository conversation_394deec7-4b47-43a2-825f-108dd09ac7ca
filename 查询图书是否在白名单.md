# 查询图书是否在白名单

## OpenAPI Specification

```yaml
openapi: 3.0.1
info:
  title: ''
  description: ''
  version: 1.0.0
paths:
  /tunano/ldc/white/tag/whitelist/contain:
    post:
      summary: 查询图书是否在白名单
      deprecated: false
      description: ''
      tags:
        - 设备接口
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                Taglist:
                  type: array
                  items:
                    type: object
                    properties:
                      Tid:
                        type: string
                        description: 图书标签UID/TID
                    x-apifox-orders:
                      - Tid
                    required:
                      - Tid
                  description: 标签列表
              required:
                - Taglist
              x-apifox-orders:
                - Taglist
            example:
              Taglist:
                - Tid: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  errorcode:
                    type: integer
                    description: 0 在白名单内, 其他不在白名单内
                  message:
                    type: string
                    description: 提示信息
                  result:
                    type: 'null'
                required:
                  - errorcode
                  - message
                  - result
                x-apifox-orders:
                  - errorcode
                  - message
                  - result
              example:
                errorcode: -1
                message: 白名单不包含标签string, 耗时0ms
                result: null
          headers: {}
          x-apifox-name: 成功
      security: []
      x-apifox-folder: 设备接口
      x-apifox-status: developing
      x-run-in-apifox: https://app.apifox.com/web/project/2805692/apis/api-340031853-run
components:
  schemas: {}
  securitySchemes: {}
servers:
  - url: http://************:9000
    description: 开发环境
security: []

```