import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:hardware/hardware.dart';
import 'package:seasetting/seasetting.dart';

import '../../../core/services/hardware_service.dart';
import '../../../features/auth/services/reader_service.dart';
import '../models/book_info.dart';
import '../models/book_scan_result.dart';
import 'gate_config_service.dart';

import 'sip2_book_service.dart';

/// 增强的RFID扫描服务
/// 借鉴 sea_mini_smart_library_client 的硬件管理技术
/// 集成书籍信息获取功能
class EnhancedRFIDService {
  static EnhancedRFIDService? _instance;
  static EnhancedRFIDService get instance => _instance ??= EnhancedRFIDService._();
  EnhancedRFIDService._();
  
  // 🔥 借鉴：连接缓存机制
  static final Map<String, dynamic> _connectionCache = {};
  static final Map<String, DateTime> _connectionLastUsed = {};
  static final Map<String, bool> _connectionHealth = {};
  
  // 🔥 借鉴：硬件服务
  final HardwareService _hardwareService = HardwareService();
  
  // 🔥 修改：仅使用SIP2服务
  final Sip2BookService _sip2BookService = Sip2BookService.instance;
  
  // 阅读器配置
  List<HWReaderSettingData> _readerConfigs = [];
  
  // 扫描状态
  bool _isScanning = false;
  bool _isInitialized = false;
  bool _isContinuousScanning = false; // 🔥 新增：持续扫描状态
  Timer? _tagPollingTimer; // 🔥 新增：标签轮询定时器
  String? _errorMessage;
  
  // 扫描结果
  final List<String> _scannedBarcodes = [];
  final Map<String, BookInfo> _bookInfoCache = {};
  final Set<String> _processedBarcodes = {};
  
  // 事件流
  final StreamController<String> _barcodeController = 
      StreamController<String>.broadcast();
  Stream<String> get barcodeStream => _barcodeController.stream;
  
  final StreamController<BookScanResult> _bookResultController = 
      StreamController<BookScanResult>.broadcast();
  Stream<BookScanResult> get bookResultStream => _bookResultController.stream;
  
  final StreamController<int> _countController = 
      StreamController<int>.broadcast();
  Stream<int> get countStream => _countController.stream;
  
  final StreamController<String> _errorController = 
      StreamController<String>.broadcast();
  Stream<String> get errorStream => _errorController.stream;
  
  // 模拟扫描定时器（实际项目中删除）
  Timer? _mockScanTimer;
  
  // Getters
  bool get isScanning => _isScanning;
  bool get isInitialized => _isInitialized;
  String? get errorMessage => _errorMessage;
  int get scannedCount => _scannedBarcodes.length;
  List<String> get scannedBarcodes => List.unmodifiable(_scannedBarcodes);
  
  /// 🔥 借鉴：从SettingProvider加载配置
  Future<void> initialize() async {
    if (_isInitialized) {
      debugPrint('增强RFID服务已经初始化');
      return;
    }

    try {
      debugPrint('开始初始化增强RFID服务...');

      // 🔥 借鉴：获取阅读器配置
      _readerConfigs = Get.context?.read<SettingProvider>()
          .readerConfigData?.bookReaders ?? [];

      if (_readerConfigs.isEmpty) {
        debugPrint('警告: 未找到书籍阅读器配置，使用默认配置');
        _readerConfigs = _generateDefaultReaderConfig();
      }

      // 🔥 借鉴：初始化硬件服务
      await _hardwareService.initializeAllHardware(_readerConfigs);

      // 🔥 修改：仅初始化SIP2图书信息服务
      _sip2BookService.initialize();

      _isInitialized = true;
      _clearError();
      debugPrint('增强RFID服务初始化完成，配置了${_readerConfigs.length}个阅读器');

      // 🔥 修复：检查主从机模式，只有主机才启动持续扫描
      await _startContinuousScanningIfMaster();
    } catch (e) {
      final errorMsg = '增强RFID服务初始化失败: $e';
      debugPrint(errorMsg);
      _setError(errorMsg);
      rethrow;
    }
  }

  /// 🔥 修复：检查主从机模式后启动持续扫描
  Future<void> _startContinuousScanningIfMaster() async {
    try {
      // 🔥 检查主从机配置
      final config = await _loadMasterSlaveConfigFromDatabase();
      if (config != null && !config.isMaster) {
        debugPrint('⚠️ 从机模式：跳过RFID硬件扫描启动，等待主机数据同步');
        debugPrint('📋 从机配置: 通道=${config.channelId}, 主机地址=${config.masterAddress}');
        return;
      }

      // 主机模式：启动持续扫描
      debugPrint('🚀 主机模式：启动RFID硬件持续扫描');
      await startContinuousScanning();
    } catch (e) {
      debugPrint('❌ 检查主从机模式并启动扫描失败: $e');
      // 出错时默认启动扫描（兼容非主从机模式）
      await startContinuousScanning();
    }
  }

  /// 🔥 新增：启动持续扫描（只启动硬件扫描，不控制数据收集）
  Future<void> startContinuousScanning() async {
    if (!_isInitialized) {
      throw Exception('增强RFID服务未初始化');
    }

    if (_isContinuousScanning) {
      debugPrint('RFID持续扫描已在运行中');
      return;
    }

    try {
      debugPrint('启动RFID持续扫描...');

      if (_readerConfigs.isNotEmpty) {
        // 🔥 借鉴：配置阅读器
        await ReaderManager.instance.changeReaders(
          jsonEncode(_readerConfigs.map((e) => e.toJson()).toList())
        );

        // 🔥 借鉴：智能连接（网口优先，串口备用）
        await _smartConnect();

        // 🔥 关键：只启动硬件扫描，不启动数据收集
        ReaderManager.instance.startInventory();

        debugPrint('RFID硬件扫描已启动，阅读器开始持续工作');
      } else {
        debugPrint('无阅读器配置，跳过硬件扫描启动');
      }

      _isContinuousScanning = true;
      _clearError();
      debugPrint('RFID持续扫描启动完成');
    } catch (e) {
      final errorMsg = '启动RFID持续扫描失败: $e';
      debugPrint(errorMsg);
      _setError(errorMsg);
      rethrow;
    }
  }

  /// 🔥 借鉴：生成默认阅读器配置
  List<HWReaderSettingData> _generateDefaultReaderConfig() {
    // 这里可以根据实际需要配置默认的阅读器
    // 暂时返回空列表，使用模拟扫描
    return [];
  }

  /// 🔥 新增：从数据库加载主从机配置
  Future<MasterSlaveConfig?> _loadMasterSlaveConfigFromDatabase() async {
    try {
      // 🔥 使用与GateConfigService相同的逻辑读取配置
      final settingProvider = Get.context?.read<SettingProvider>();

      if (settingProvider != null) {
        final configs = await settingProvider.getMasterSlaveConfigs();

        if (configs.isNotEmpty) {
          // 使用最新的配置（最后一个）
          final seasettingConfig = configs.last;

          // 转换为本地配置格式
          final config = MasterSlaveConfig(
            channelId: seasettingConfig.channelId,
            isMaster: seasettingConfig.isMaster,
            slaveAddress: seasettingConfig.slaveAddress,
            masterAddress: seasettingConfig.masterAddress,
            port: seasettingConfig.port,
          );

          debugPrint('📋 从数据库读取主从机配置: ${config.channelId}');
          debugPrint('📋 配置详情: ${config.isMaster ? "主机" : "从机"}模式');
          return config;
        } else {
          debugPrint('📋 数据库中没有找到主从机配置，按主机模式运行');
        }
      } else {
        debugPrint('📋 无法获取SettingProvider，按主机模式运行');
      }

      return null;
    } catch (e) {
      debugPrint('⚠️ 读取主从机配置失败: $e，按主机模式运行');
      return null;
    }
  }

  /// 🔥 强化：开始数据收集（强化轮询机制）
  Future<void> startDataCollection() async {
    if (!_isInitialized) {
      await initialize();
    }

    if (_isScanning) {
      debugPrint('🔄 RFID数据收集已在进行中，重置防重复机制');
      // 🔥 重要：重置防重复机制，让轮询重新发现标签
      _processedBarcodes.clear();
      debugPrint('✅ 已处理条码列表已清空，轮询将重新发现标签');
      return;
    }

    try {
      debugPrint('🚀 开始RFID数据收集...');

      // 清空之前的扫描结果
      _scannedBarcodes.clear();
      _bookInfoCache.clear();
      _processedBarcodes.clear();
      debugPrint('📋 扫描结果和缓存已清空');

      // 🔥 关键：清空阅读器缓冲区，但保持tagList
      await clearScanBuffer();

      if (_readerConfigs.isNotEmpty) {
        // 🔥 重要：不再调用startInventory()，因为硬件扫描已经在持续运行
        // 启动数据监听和轮询机制
        debugPrint('🎯 启动数据监听和轮询机制...');
        _startTagDataListening();
      } else {
        // 使用模拟扫描进行测试
        debugPrint('🧪 启动模拟扫描...');
        _startMockScanning();
      }

      _isScanning = true;
      _clearError();
      debugPrint('✅ RFID数据收集已启动，轮询机制运行中');

      // 检查当前tagList状态
      final tagProvider = Get.context?.read<HWTagProvider>();
      if (tagProvider != null) {
        debugPrint('📊 当前tagList状态: ${tagProvider.tagList.length}个标签');
      }
    } catch (e) {
      final errorMsg = '❌ 启动RFID数据收集失败: $e';
      debugPrint(errorMsg);
      _setError(errorMsg);
      rethrow;
    }
  }

  /// 🔥 保留原方法以兼容现有代码
  @Deprecated('请使用startDataCollection()方法')
  Future<void> startEnhancedScanning() async {
    await startDataCollection();
  }

  /// 🔥 修改：清空扫描缓冲区（不清空HWTagProvider）
  Future<void> clearScanBuffer() async {
    try {
      debugPrint('清空RFID扫描缓冲区...');

      if (_readerConfigs.isNotEmpty) {
        // 🔥 修复：清空HWTagProvider，确保出馆流程间的数据隔离
        final tagProvider = Get.context?.read<HWTagProvider>();
        if (tagProvider != null) {
          final beforeCount = tagProvider.tagList.length;
          // tagProvider.clearTagList();
          debugPrint('🧹 已清空HWTagProvider: $beforeCount -> ${tagProvider.tagList.length}个标签');
        } else {
          debugPrint('⚠️ 无法获取HWTagProvider，跳过清空');
        }

        // 🔥 修复：主机模式下不暂停RFID硬件扫描
        // 只清空软件层的缓冲区数据，保持硬件持续扫描
        try {
          debugPrint('🔧 主机模式：清空软件缓冲区，保持硬件持续扫描');

          // ✅ 不调用pauseInventory()和resumeInventory()
          // ✅ 不调用stopInventory()和startInventory()
          // ✅ 只清空软件层的数据，让硬件持续扫描

          // 清空已扫描条码集合
          _scannedBarcodes.clear();

          // 重置已处理条码集合（让现有标签能重新被识别）
          resetProcessedBarcodes();

          debugPrint('✅ 软件缓冲区已清空，硬件扫描保持运行');
        } catch (e) {
          debugPrint('清空软件缓冲区失败: $e');
        }
      }
    } catch (e) {
      debugPrint('清空RFID缓冲区失败: $e');
      // 不抛出异常，允许继续执行
    }
  }
  
  /// 🔥 借鉴：智能连接逻辑
  Future<void> _smartConnect() async {
    // 检查是否有网口配置
    final networkConfig = _findNetworkConfig();
    
    if (networkConfig != null) {
      debugPrint('使用网口连接: ${networkConfig['ipAddress']}:${networkConfig['port']}');
      await _connectWithNetwork(networkConfig);
    } else {
      debugPrint('使用串口连接');
      await _connectWithSerial();
    }
  }
  
  /// 🔥 借鉴：网口配置检测（支持LSGate和UHF阅读器）
  Map<String, String>? _findNetworkConfig() {
    for (HWReaderSettingData config in _readerConfigs) {
      String? ipAddress;
      String? netPort;
      String readerTypeName = '';
      
      // 检查UHF阅读器（荣瑞2881等）
      if (config.info is HWUHFInfoData) {
        HWUHFInfoData info = config.info as HWUHFInfoData;
        ipAddress = info.valueForKey('ipAddress');
        netPort = info.valueForKey('netPort');
        readerTypeName = 'UHF阅读器';
      }
      // 🔥 新增：检查LSGate图书馆安全门RFID阅读器
      else if (config.info is HWLSGateInfoData) {
        HWLSGateInfoData info = config.info as HWLSGateInfoData;
        ipAddress = info.valueForKey('ipAddress');
        netPort = info.valueForKey('netPort');
        readerTypeName = 'LSGate图书馆安全门RFID阅读器';
      }
      
      if (ipAddress?.isNotEmpty == true && netPort?.isNotEmpty == true) {
        debugPrint('找到网口配置: $readerTypeName - $ipAddress:$netPort');
        return {
          'ipAddress': ipAddress!,
          'port': netPort!,
          'readerId': config.id.toString(),
          'readerType': readerTypeName,
        };
      }
    }
    return null;
  }
  
  /// 🔥 借鉴：网口连接（带缓存）
  Future<void> _connectWithNetwork(Map<String, String> config) async {
    final cacheKey = '${config['ipAddress']}:${config['port']}';
    
    // 检查缓存
    if (_connectionCache.containsKey(cacheKey) && 
        _isConnectionHealthy(cacheKey)) {
      debugPrint('复用网口连接缓存: $cacheKey');
      return;
    }
    
    // 创建新连接
    try {
      ReaderManager.instance.open();
      ReaderManager.instance.untilDeteted();
      
      // 更新缓存
      _connectionCache[cacheKey] = true;
      _connectionLastUsed[cacheKey] = DateTime.now();
      _connectionHealth[cacheKey] = true;
      
      debugPrint('网口连接成功: $cacheKey');
    } catch (e) {
      _connectionHealth[cacheKey] = false;
      throw Exception('网口连接失败: $e');
    }
  }
  
  /// 🔥 借鉴：串口连接（带缓存）
  Future<void> _connectWithSerial() async {
    try {
      await ReaderManager.instance.open();
      await ReaderManager.instance.untilDeteted();
      debugPrint('串口连接成功');
    } catch (e) {
      throw Exception('串口连接失败: $e');
    }
  }
  
  /// 🔥 强化：标签数据监听（统一改为仅使用轮询机制，保证主/从一致性）
  void _startTagDataListening() {
    final tagProvider = Get.context?.read<HWTagProvider>();
    if (tagProvider != null) {
      // 🔥 统一：不再依赖 changeAddedItem 事件，避免主/从不一致
      // 确保移除旧的监听器（如果曾经注册过）
      tagProvider.removeListener(_onTagDataChanged);

      // 仅使用轮询机制从 tagList 持续产出条码
      _startTagPolling();
      debugPrint('✅ 标签监听改为仅轮询机制（每500ms轮询tagList）');

      // 🔍 输出当前 tagList 状态（调试）
      final currentTags = tagProvider.tagList;
      debugPrint('📊 当前HWTagProvider状态:');
      debugPrint('  - tagList: ${currentTags.length}个标签');
      debugPrint('  - type: ${tagProvider.type}');

      if (currentTags.isNotEmpty) {
        debugPrint('📋 现有标签列表:');
        for (int i = 0; i < currentTags.length && i < 5; i++) {
          final tag = currentTags[i];
          debugPrint('  [$i] barcode=${tag.barCode}, uid=${tag.uid}');
        }
      }

      debugPrint('🎯 标签数据获取已统一为轮询机制');
    } else {
      debugPrint('❌ 警告: 无法获取HWTagProvider，数据监听失败');
    }
  }

  /// 🔥 优化：标签轮询机制（解决changeAddedItem事件不触发的问题）
  void _startTagPolling() {
    // 取消之前的轮询
    _tagPollingTimer?.cancel();

    // 启动新的轮询
    _tagPollingTimer = Timer.periodic(const Duration(milliseconds: 500), (timer) {
      if (!_isScanning) {
        debugPrint('⏹️ 轮询停止: _isScanning = false');
        timer.cancel();
        return;
      }

      _pollTagData();
    });

    debugPrint('🚀 标签轮询机制已启动 (每500ms轮询一次)');

    // 立即执行一次轮询，不等待第一个定时器
    Future.delayed(const Duration(milliseconds: 100), () {
      if (_isScanning) {
        debugPrint('🔄 执行首次轮询...');
        _pollTagData();
      }
    });
  }



  /// 🔥 优化：轮询标签数据（强化日志和错误处理）
  void _pollTagData() {
    if (!_isScanning) {
      debugPrint('🔄 轮询跳过: RFID未在扫描状态');
      return;
    }

    try {
      debugPrint('🔄 开始RFID轮询检查...');
      final tagProvider = Get.context?.read<HWTagProvider>();
      if (tagProvider == null) {
        debugPrint('⚠️ 轮询失败: 无法获取HWTagProvider');
        return;
      }

      final currentTags = tagProvider.tagList;
      debugPrint('📊 轮询状态: 扫描中=$_isScanning, tagList=${currentTags.length}个标签, 已处理=${_processedBarcodes.length}个');

      if (currentTags.isEmpty) {
        debugPrint('⚠️ tagList为空，场上无标签');
        return;
      }

      int newTagCount = 0;
      // 检查是否有新的标签
      for (final tag in currentTags) {
        final barcode = tag.barCode;
        final uid = tag.uid;

        debugPrint('🏷️ 检查标签: barcode=$barcode, uid=$uid');

        if (barcode != null && barcode.isNotEmpty) {
          if (!_processedBarcodes.contains(barcode)) {
            debugPrint('🆕 轮询发现新标签: $barcode (uid: $uid)');
            debugPrint('📋 添加到已处理列表: ${_processedBarcodes.length} -> ${_processedBarcodes.length + 1}');
            _processedBarcodes.add(barcode);
            _onBarcodeScanned(barcode);
            newTagCount++;
          } else {
            debugPrint('🔄 标签已处理: $barcode');
          }
        } else if (uid != null && uid.isNotEmpty) {
          // 🔥 兼容处理：如果没有条码但有UID，将UID作为条码使用
          if (!_processedBarcodes.contains(uid)) {
            debugPrint('🆕 轮询发现新标签(UID作为条码): $uid');
            debugPrint('📋 添加到已处理列表: ${_processedBarcodes.length} -> ${_processedBarcodes.length + 1}');
            _processedBarcodes.add(uid);
            _onBarcodeScanned(uid);
            newTagCount++;
          } else {
            debugPrint('🔄 标签已处理(UID): $uid');
          }
        } else {
          debugPrint('⚠️ 标签无有效标识符: barcode=$barcode, uid=$uid');
        }
      }

      if (newTagCount > 0) {
        debugPrint('✅ 轮询完成: 发现${newTagCount}个新标签，总计已处理${_processedBarcodes.length}个标签');
        debugPrint('📋 当前已处理标签列表: ${_processedBarcodes.toList()}');
      } else {
        debugPrint('🔄 轮询完成: 无新标签，已处理${_processedBarcodes.length}个标签');
      }
    } catch (e) {
      debugPrint('❌ 轮询标签数据失败: $e');
    }
  }
  
  /// 🔥 借鉴：标签数据处理
  void _onTagDataChanged() {
    final tagProvider = Get.context?.read<HWTagProvider>();
    if (tagProvider != null && tagProvider.tagList.isNotEmpty && tagProvider.type == HWTagType.addedItem) {
      // 简化日志：只输出关键信息
      if (tagProvider.tagList.length > 0) {
        debugPrint('RFID扫描: 发现 ${tagProvider.tagList.length} 个标签');
      }

      for (HWTagData tag in tagProvider.tagList) {
        // 🔥 借鉴：条码提取逻辑
        List<String> barcodes = _extractBarcodes(tag);
        for (String barcode in barcodes) {
          if (barcode.isNotEmpty && !_processedBarcodes.contains(barcode)) {
            _processedBarcodes.add(barcode);
            _onBarcodeScanned(barcode);
          }
        }
      }
    }
  }
  
  /// 🔥 借鉴：多字段条码提取（增强解码器支持）
  List<String> _extractBarcodes(HWTagData tag) {
    List<String> barcodes = [];

    // 1. 优先从barCode字段提取（已解码的条码）
    if (tag.barCode?.isNotEmpty ?? false) {
      barcodes.add(tag.barCode!);
      debugPrint('从barCode字段提取条码: ${tag.barCode}');
    } else if (tag.uid?.isNotEmpty ?? false) {
      // 🔥 兼容处理：如果没有条码但有UID，将UID作为条码使用
      barcodes.add(tag.uid!);
      debugPrint('从uid字段提取UID作为条码: ${tag.uid}');
    }

    // 2. 从oidList提取（解码后的数据）
    if (tag.oidList?.isNotEmpty ?? false) {
      for (var oid in tag.oidList!) {
        if (oid.oid == 1 && oid.data.isNotEmpty) {
          barcodes.add(oid.data);
          debugPrint('从oidList提取条码: ${oid.data}');
        }
      }
    }

    // 3. 从info字段提取（解码后的条码）
    if (tag.info?.containsKey('barCode') ?? false) {
      String? infoBarcode = tag.info!['barCode'];
      if (infoBarcode?.isNotEmpty ?? false) {
        barcodes.add(infoBarcode!);
        debugPrint('从info字段提取条码: $infoBarcode');
      }
    }

    // 4. 🔥 新增：尝试使用解码器解码UID
    if (barcodes.isEmpty && tag.uid?.isNotEmpty == true) {
      String? decodedBarcode = _decodeUidToBarcode(tag);
      if (decodedBarcode?.isNotEmpty == true) {
        barcodes.add(decodedBarcode!);
        debugPrint('通过解码器解码UID得到条码: $decodedBarcode');
      } else {
        // 如果解码失败，记录原始UID但不作为有效条码
        debugPrint('解码器解码失败，原始UID: ${tag.uid}');
        // 暂时不添加原始UID，避免无效的API调用
        // barcodes.add(tag.uid!);
      }
    }

    return barcodes.where((barcode) => barcode.isNotEmpty).toList();
  }

  /// 🔥 新增：使用解码器将UID解码为图书条码
  String? _decodeUidToBarcode(HWTagData tag) {
    try {
      // 获取当前阅读器配置
      if (_readerConfigs.isEmpty) {
        debugPrint('没有阅读器配置，无法解码');
        return null;
      }

      // 获取第一个阅读器配置（通常只有一个）
      final readerConfig = _readerConfigs.first;

      // 获取解码器类型
      final decoderType = readerConfig.info?.valueForKey('decoderType');
      if (decoderType == null) {
        debugPrint('阅读器未配置解码器类型');
        return null;
      }

      debugPrint('使用解码器: $decoderType 解码UID: ${tag.uid}');

      // 🔥 借鉴：使用ReaderService获取解码器
      final readerService = ReaderService.instance;
      final coder = readerService.getCoder(tag.tagFrequency, readerConfig);

      if (coder == null) {
        debugPrint('无法获取解码器: $decoderType');
        return null;
      }

      // 🔥 借鉴：执行解码 - 使用正确的方法名
      String? decodeResult;
      try {
        // 尝试使用不同的解码方法
        if (coder.runtimeType.toString().contains('UHF')) {
          // 超高频解码器可能有特殊的解码方法
          decodeResult = _tryUHFDecode(coder, tag, readerConfig);
        } else {
          // 通用解码器处理
          decodeResult = _tryGeneralDecode(coder, tag, readerConfig);
        }
      } catch (e) {
        debugPrint('解码器调用失败: $e');
        return null;
      }

      if (decodeResult?.isNotEmpty == true) {
        debugPrint('解码成功: ${tag.uid} -> $decodeResult');
        return decodeResult;
      } else {
        debugPrint('解码失败，返回空结果');
        return null;
      }

    } catch (e) {
      debugPrint('解码过程出错: $e');
      return null;
    }
  }

  /// 尝试超高频解码
  String? _tryUHFDecode(dynamic coder, HWTagData tag, HWReaderSettingData config) {
    try {
      // 对于超高频标签，通常需要从UID中提取条码
      // 根据配置的解码规则进行处理
      final coderConfig = config.coderConfig;
      if (coderConfig != null) {
        String uid = tag.uid ?? '';

        // 应用前缀和后缀规则
        String result = uid;
        final prefixStr = coderConfig.prefixStr;
        if (prefixStr != null && prefixStr.isNotEmpty) {
          result = prefixStr + result;
        }
        final suffixStr = coderConfig.suffixStr;
        if (suffixStr != null && suffixStr.isNotEmpty) {
          result = result + suffixStr;
        }

        // 应用替换规则
        final replaceRuleStr = coderConfig.replaceRuleStr;
        if (replaceRuleStr != null && replaceRuleStr.isNotEmpty) {
          final rules = replaceRuleStr.split('|');
          if (rules.length >= 2) {
            result = result.replaceAll(rules[0], rules[1]);
          }
        }

        return result.isNotEmpty ? result : null;
      }

      return tag.uid;
    } catch (e) {
      debugPrint('超高频解码失败: $e');
      return null;
    }
  }

  /// 尝试通用解码
  String? _tryGeneralDecode(dynamic coder, HWTagData tag, HWReaderSettingData config) {
    try {
      // 对于通用解码器，直接返回UID或应用基本规则
      final coderConfig = config.coderConfig;
      if (coderConfig != null) {
        String uid = tag.uid ?? '';

        // 应用书籍代码规则
        final bookCode = coderConfig.bookCode;
        if (bookCode != null && bookCode.isNotEmpty) {
          // 如果UID以书籍代码开头，则提取后面的部分
          if (uid.startsWith(bookCode)) {
            uid = uid.substring(bookCode.length);
          }
        }

        return uid.isNotEmpty ? uid : null;
      }

      return tag.uid;
    } catch (e) {
      debugPrint('通用解码失败: $e');
      return null;
    }
  }
  
  /// 处理扫描到的条码
  void _onBarcodeScanned(String barcode) {
    if (!_isScanning) {
      debugPrint('⚠️ RFID未在扫描状态，忽略条码: $barcode');
      return;
    }

    // 避免重复扫描
    if (_scannedBarcodes.contains(barcode)) {
      debugPrint('🔄 条码已扫描过，跳过: $barcode');
      return;
    }

    _scannedBarcodes.add(barcode);

    // 🔥 详细日志：标签检测过程
    debugPrint('🏷️ 检测到标签: $barcode');
    debugPrint('📊 当前扫描状态: 扫描中=${_isScanning}, 已扫描=${_scannedBarcodes.length}个');
    debugPrint('📋 已扫描列表: ${_scannedBarcodes.toList()}');

    // 🔥 关键修复：立即发送条码事件，不依赖书籍信息查询
    _barcodeController.add(barcode);
    _countController.add(_scannedBarcodes.length);

    // 🔥 移除书籍信息查询逻辑，让条码立即进入共享池
    // 书籍信息查询将在后续的集合A监听中处理
    debugPrint('✅ 条码已发送到barcodeStream，将进入共享池: $barcode');
    debugPrint('📡 barcodeStream监听器数量: ${_barcodeController.hasListener ? "有监听器" : "无监听器"}');
  }
  
  /// 🔥 已移除：书籍信息查询逻辑已迁移到独立的查询服务
  /// 此方法保留用于向后兼容，但不再在标签检测时调用
  @Deprecated('书籍信息查询已迁移到独立服务，请使用BookInfoQueryService')
  void _fetchBookInfoAsync(String barcode) async {
    debugPrint('⚠️ _fetchBookInfoAsync已废弃，书籍信息查询请使用BookInfoQueryService');
    // 方法体已移除，避免在标签检测时进行书籍信息查询
  }
  
  /// 开始模拟扫描（实际项目中删除）
  void _startMockScanning() {
    _mockScanTimer = Timer.periodic(const Duration(milliseconds: 1500), (timer) {
      if (!_isScanning) {
        timer.cancel();
        return;
      }
      
      // 随机生成条码
      if (Random().nextDouble() > 0.4) { // 60%概率扫描到书籍
        final barcode = 'BOOK${Random().nextInt(1000).toString().padLeft(3, '0')}';
        _onBarcodeScanned(barcode);
      }
    });
  }
  
  /// 🔥 修改：停止数据收集（不停止硬件扫描）
  Future<List<String>> stopDataCollection() async {
    if (!_isScanning) {
      debugPrint('RFID数据收集未在进行中');
      return List.from(_scannedBarcodes);
    }

    try {
      debugPrint('停止RFID数据收集...');

      if (_readerConfigs.isNotEmpty) {
        // 🔥 重要：不再调用stopInventory()，保持硬件扫描运行
        // 只移除数据监听器
        final tagProvider = Get.context?.read<HWTagProvider>();
        if (tagProvider != null) {
          tagProvider.removeListener(_onTagDataChanged);
        }

        // 🔥 新增：停止轮询机制
        _tagPollingTimer?.cancel();
        _tagPollingTimer = null;
      } else {
        // 停止模拟扫描
        _stopMockScanning();
      }

      _isScanning = false;

      final result = List<String>.from(_scannedBarcodes);
      debugPrint('RFID数据收集已停止，共收集到${result.length}个条码');

      return result;
    } catch (e) {
      final errorMsg = '停止RFID数据收集失败: $e';
      debugPrint(errorMsg);
      _setError(errorMsg);
      return List<String>.from(_scannedBarcodes);
    }
  }

  /// 🔥 保留原方法以兼容现有代码
  @Deprecated('请使用stopDataCollection()方法')
  Future<List<String>> stopScanning() async {
    return await stopDataCollection();
  }
  
  /// 停止模拟扫描
  void _stopMockScanning() {
    _mockScanTimer?.cancel();
    _mockScanTimer = null;
  }
  
  /// 获取所有已扫描书籍的信息（实时获取）
  Future<List<BookInfo>> getAllScannedBooksInfo() async {
    if (_scannedBarcodes.isEmpty) return [];

    // 实时获取所有书籍信息
    final books = <BookInfo>[];
    for (String barcode in _scannedBarcodes) {
      final bookInfo = await _sip2BookService.getBookInfoRealTime(barcode);
      if (bookInfo != null) {
        books.add(bookInfo);
      }
    }
    return books;
  }

  /// 🔥 新增：获取单个条码的书籍信息（实时获取，不缓存）
  Future<BookInfo?> getSingleBookInfoRealTime(String barcode) async {
    if (barcode.isEmpty) return null;

    debugPrint('实时获取单个书籍信息: $barcode');

    // 直接使用SIP2服务实时获取
    return await _sip2BookService.getBookInfoRealTime(barcode);
  }
  
  /// 获取当前扫描结果
  List<String> getCurrentScanResult() {
    return List<String>.from(_scannedBarcodes);
  }
  
  /// 清空扫描结果
  void clearScanResult() {
    _scannedBarcodes.clear();
    _processedBarcodes.clear();
    _countController.add(0);
    debugPrint('扫描结果已清空');
  }

  /// 🔥 强化：重置已处理条码集合（供主从机扩展调用）
  /// 用于在清空共享池后，让当前场上的标签能够重新被识别为新标签
  void resetProcessedBarcodes() {
    final beforeCount = _processedBarcodes.length;
    final contentBefore = _processedBarcodes.toList();

    debugPrint('🔄 开始重置已处理条码集合...');
    debugPrint('📊 重置前状态: 大小=$beforeCount, 内容=$contentBefore');

    _processedBarcodes.clear();

    debugPrint('✅ 已处理条码集合已重置: $beforeCount -> ${_processedBarcodes.length}');
    debugPrint('🔄 当前场上标签将被重新识别为新标签');

    // 🔥 检查当前tagList状态
    final tagProvider = Get.context?.read<HWTagProvider>();
    if (tagProvider != null) {
      final currentTags = tagProvider.tagList;
      debugPrint('📊 当前tagList状态: ${currentTags.length}个标签');
      if (currentTags.isNotEmpty) {
        debugPrint('📋 现有标签将被重新处理:');
        for (int i = 0; i < currentTags.length && i < 3; i++) {
          final tag = currentTags[i];
          debugPrint('  [$i] barcode=${tag.barCode}, uid=${tag.uid}');
        }
      }
    }

    // 🔥 立即触发一次轮询，加速标签重新发现
    if (_isScanning) {
      debugPrint('🚀 立即触发轮询，加速标签重新发现...');
      _pollTagData();
    } else {
      debugPrint('⚠️ 当前未在扫描状态，跳过立即轮询');
    }
  }
  
  /// 连接健康检查
  bool _isConnectionHealthy(String cacheKey) {
    final lastUsed = _connectionLastUsed[cacheKey];
    final isHealthy = _connectionHealth[cacheKey] ?? false;
    
    if (!isHealthy || lastUsed == null) return false;
    
    // 连接超过5分钟未使用，认为可能不健康
    return DateTime.now().difference(lastUsed).inMinutes < 5;
  }
  
  /// 设置错误信息
  void _setError(String error) {
    _errorMessage = error;
    _errorController.add(error);
  }
  
  /// 清除错误信息
  void _clearError() {
    _errorMessage = null;
  }
  
  /// 获取服务状态
  Map<String, dynamic> getStatus() {
    return {
      'initialized': _isInitialized,
      'scanning': _isScanning,
      'scanned_count': _scannedBarcodes.length,
      'book_info_count': _bookInfoCache.length,
      'reader_configs': _readerConfigs.length,
      'error': _errorMessage,
      'service_name': 'EnhancedRFIDService',
      'version': '1.0.0',
    };
  }
  
  /// 测试连接
  Future<bool> testConnection() async {
    try {
      if (_readerConfigs.isNotEmpty) {
        // 测试真实硬件连接
        ReaderManager.instance.open();
        ReaderManager.instance.close();
        return true;
      } else {
        // 模拟连接测试
        await Future.delayed(const Duration(milliseconds: 100));
        return _isInitialized;
      }
    } catch (e) {
      debugPrint('连接测试失败: $e');
      return false;
    }
  }
  
  /// 重置服务
  Future<void> reset() async {
    debugPrint('重置增强RFID服务');
    
    if (_isScanning) {
      await stopScanning();
    }
    
    clearScanResult();
    _clearError();
  }
  
  /// 释放资源
  void dispose() {
    debugPrint('释放增强RFID服务资源');
    
    _stopMockScanning();
    _isScanning = false;
    _isInitialized = false;
    
    _barcodeController.close();
    _bookResultController.close();
    _countController.close();
    _errorController.close();
    
    debugPrint('增强RFID服务已释放');
  }
}

/// 书籍扫描结果
class BookScanResult {
  final String barcode;
  final BookInfo? bookInfo;
  final DateTime scanTime;
  final BookScanStatus status;
  final String? error;
  
  BookScanResult({
    required this.barcode,
    this.bookInfo,
    required this.scanTime,
    required this.status,
    this.error,
  });
  
  Map<String, dynamic> toJson() {
    return {
      'barcode': barcode,
      'book_info': bookInfo?.toJson(),
      'scan_time': scanTime.toIso8601String(),
      'status': status.toString(),
      'error': error,
    };
  }
}

/// 书籍扫描状态
enum BookScanStatus {
  processing, // 处理中
  success,    // 成功
  failed,     // 失败
  notFound,   // 未找到（其他馆的条码）
}
