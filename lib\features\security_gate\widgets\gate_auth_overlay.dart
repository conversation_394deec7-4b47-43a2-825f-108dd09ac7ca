import 'package:a3g/core/utils/window_util.dart';
import 'package:flutter/material.dart';
import 'package:base_package/base_package.dart';

/// 闸机认证覆盖层
class GateAuthOverlay extends StatefulWidget {
  final VoidCallback? onClose;
  
  const GateAuthOverlay({
    Key? key,
    this.onClose,
  }) : super(key: key);

  @override
  State<GateAuthOverlay> createState() => _GateAuthOverlayState();
}

class _GateAuthOverlayState extends State<GateAuthOverlay>
    with TickerProviderStateMixin {
  
  late AnimationController _fadeController;
  late AnimationController _scaleController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  
  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimations();
  }
  
  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: Duration(milliseconds: 300),
      vsync: this,
    );
    
    _scaleController = AnimationController(
      duration: Duration(milliseconds: 400),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));
    
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));
  }
  
  void _startAnimations() {
    _fadeController.forward();
    _scaleController.forward();
  }
  
  void _closeOverlay() async {
    await _fadeController.reverse();
    await _scaleController.reverse();
    widget.onClose?.call();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: AnimatedBuilder(
        animation: Listenable.merge([_fadeAnimation, _scaleAnimation]),
        builder: (context, child) {
          return Opacity(
            opacity: _fadeAnimation.value,
            child: Container(
              width: double.infinity,
              height: double.infinity,
              color: Colors.black.withOpacity(0.7),
              child: Center(
                child: Transform.scale(
                  scale: _scaleAnimation.value,
                  child: Container(
                    width: 600.p,
                    height: 400.p,
                    margin: EdgeInsets.all(40.p),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(20.p),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.3),
                          blurRadius: 20,
                          offset: Offset(0, 10),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        // 标题栏
                        _buildHeader(),
                        
                        // 认证内容区域
                        Expanded(
                          child: _buildAuthContent(),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
  
  /// 构建标题栏
  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(20.p),
      decoration: BoxDecoration(
        color: Color(0xFF2A5298),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.p),
          topRight: Radius.circular(20.p),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.security,
            color: Colors.white,
            size: 32.p,
          ),
          SizedBox(width: 15.p),
          Expanded(
            child: Text(
              '身份认证',
              style: TextStyle(
                color: Colors.white,
                fontSize: 24.p,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          IconButton(
            onPressed: _closeOverlay,
            icon: Icon(
              Icons.close,
              color: Colors.white,
              size: 28.p,
            ),
          ),
        ],
      ),
    );
  }
  
  /// 构建认证内容
  Widget _buildAuthContent() {
    return Padding(
      padding: EdgeInsets.all(30.p),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 认证图标
          Container(
            width: 100.p,
            height: 100.p,
            decoration: BoxDecoration(
              color: Color(0xFF2A5298).withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.person_outline,
              size: 60.p,
              color: Color(0xFF2A5298),
            ),
          ),
          
          SizedBox(height: 30.p),
          
          // 提示文字
          Text(
            '请使用以下方式进行身份认证',
            style: TextStyle(
              fontSize: 20.p,
              color: Colors.grey[700],
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          
          SizedBox(height: 40.p),
          
          // 认证方式提示
          _buildAuthMethods(),
        ],
      ),
    );
  }
  
  /// 构建认证方式提示
  Widget _buildAuthMethods() {
    final methods = [
      {'icon': Icons.face, 'text': '人脸识别'},
      {'icon': Icons.credit_card, 'text': '读者证'},
      {'icon': Icons.qr_code, 'text': '二维码'},
    ];
    
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: methods.map((method) {
        return Column(
          children: [
            Container(
              width: 60.p,
              height: 60.p,
              decoration: BoxDecoration(
                color: Color(0xFF2A5298).withOpacity(0.1),
                borderRadius: BorderRadius.circular(15.p),
              ),
              child: Icon(
                method['icon'] as IconData,
                size: 30.p,
                color: Color(0xFF2A5298),
              ),
            ),
            SizedBox(height: 10.p),
            Text(
              method['text'] as String,
              style: TextStyle(
                fontSize: 14.p,
                color: Colors.grey[600],
              ),
            ),
          ],
        );
      }).toList(),
    );
  }
  
  @override
  void dispose() {
    _fadeController.dispose();
    _scaleController.dispose();
    super.dispose();
  }
}
