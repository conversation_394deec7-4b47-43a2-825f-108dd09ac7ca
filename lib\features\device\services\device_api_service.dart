import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../models/device_model.dart';
import 'unified_api_config_manager.dart';

/// 设备API服务
/// 使用统一API配置管理器
class DeviceApiService {
  static DeviceApiService? _instance;
  static DeviceApiService get instance => _instance ??= DeviceApiService._();
  DeviceApiService._();

  final Dio _dio = Dio();
  final UnifiedApiConfigManager _configManager = UnifiedApiConfigManager.instance;
  bool _isInitialized = false;

  /// 初始化服务
  Future<void> initialize() async {
    final baseUrl = await _configManager.getAuthBaseUrl();  // 使用认证baseurl
    final timeoutSeconds = await _configManager.getTimeoutSeconds();

    _dio.options = BaseOptions(
      baseUrl: baseUrl, // 修正：Dio 5.x 使用 baseUrl 而不是 baseURL
      connectTimeout: Duration(seconds: timeoutSeconds),
      receiveTimeout: Duration(seconds: timeoutSeconds),
      contentType: Headers.jsonContentType,
      responseType: ResponseType.json,
    );

    // 只在第一次初始化时添加拦截器，避免重复添加
    if (!_isInitialized && kDebugMode) {
      _dio.interceptors.add(LogInterceptor(
        requestBody: true,
        responseBody: true,
        logPrint: (obj) => debugPrint(obj.toString()),
      ));
      _isInitialized = true;
    }

    debugPrint('设备API服务初始化完成: $baseUrl');
  }
  
  /// 设备注册
  Future<ApiResponseModel> registerDevice(DeviceRegisterModel device) async {
    try {
      await initialize(); // 确保使用最新配置
      
      final endpoint = await _configManager.getDeviceRegisterEndpoint();
      debugPrint('发送设备注册请求: $endpoint');
      debugPrint('请求数据: ${jsonEncode(device.toJson())}');
      
      final response = await _dio.post(
        endpoint,
        data: device.toJson(),
      );
      
      debugPrint('设备注册响应: ${response.data}');
      
      if (response.statusCode == 200 && response.data != null) {
        return ApiResponseModel.fromJson(response.data);
      } else {
        return ApiResponseModel(
          errorCode: response.statusCode ?? -1,
          message: '请求失败: ${response.statusCode}',
        );
      }
    } on DioException catch (e) {
      debugPrint('设备注册请求异常: ${e.message}');
      return ApiResponseModel(
        errorCode: e.response?.statusCode ?? -1,
        message: _handleDioError(e),
      );
    } catch (e) {
      debugPrint('设备注册未知错误: $e');
      return ApiResponseModel(
        errorCode: -1,
        message: '注册失败: $e',
      );
    }
  }
  
  /// 上传设备状态
  Future<ApiResponseModel> uploadDeviceStatus(DeviceStatusModel status) async {
    try {
      await initialize(); // 确保使用最新配置

      final endpoint = await _configManager.getDeviceStatusEndpoint();
      debugPrint('发送设备状态上传请求: $endpoint');
      debugPrint('请求数据: ${jsonEncode(status.toJson())}');

      final response = await _dio.post(
        endpoint,
        data: status.toJson(),
      );

      debugPrint('设备状态上传响应: ${response.data}');

      if (response.statusCode == 200 && response.data != null) {
        return ApiResponseModel.fromJson(response.data);
      } else {
        return ApiResponseModel(
          errorCode: response.statusCode ?? -1,
          message: '请求失败: ${response.statusCode}',
        );
      }
    } on DioException catch (e) {
      debugPrint('设备状态上传请求异常: ${e.message}');
      return ApiResponseModel(
        errorCode: e.response?.statusCode ?? -1,
        message: _handleDioError(e),
      );
    } catch (e) {
      debugPrint('设备状态上传未知错误: $e');
      return ApiResponseModel(
        errorCode: -1,
        message: '上传失败: $e',
      );
    }
  }

  /// 读者认证
  Future<ApiResponseModel> authenticateReader(ReaderAuthModel authData) async {
    try {
      await initialize(); // 确保使用最新配置

      final endpoint = await _configManager.getDeviceAuthEndpoint();
      debugPrint('发送读者认证请求: $endpoint');
      debugPrint('请求数据: ${jsonEncode(authData.toJson())}');

      final response = await _dio.post(
        endpoint,
        data: authData.toJson(),
      );

      debugPrint('读者认证响应: ${response.data}');

      if (response.statusCode == 200 && response.data != null) {
        return ApiResponseModel.fromJson(response.data);
      } else {
        return ApiResponseModel(
          errorCode: response.statusCode ?? -1,
          message: '请求失败: ${response.statusCode}',
        );
      }
    } on DioException catch (e) {
      debugPrint('读者认证请求异常: ${e.message}');
      return ApiResponseModel(
        errorCode: e.response?.statusCode ?? -1,
        message: _handleDioError(e),
      );
    } catch (e) {
      debugPrint('读者认证未知错误: $e');
      return ApiResponseModel(
        errorCode: -1,
        message: '认证失败: $e',
      );
    }
  }
  
  /// 处理Dio错误
  String _handleDioError(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
        return '连接超时，请检查网络';
      case DioExceptionType.sendTimeout:
        return '发送超时，请检查网络';
      case DioExceptionType.receiveTimeout:
        return '接收超时，请检查网络';
      case DioExceptionType.badResponse:
        return '服务器响应错误: ${e.response?.statusCode}';
      case DioExceptionType.cancel:
        return '请求已取消';
      case DioExceptionType.connectionError:
        return '网络连接错误，请检查网络设置';
      case DioExceptionType.unknown:
        return '未知错误: ${e.message}';
      default:
        return '请求失败: ${e.message}';
    }
  }
  
  /// 测试API连接
  Future<bool> testConnection() async {
    try {
      await initialize();
      
      // 尝试发送一个简单的请求来测试连接
      final response = await _dio.get('/').timeout(const Duration(seconds: 5));
      return response.statusCode != null && response.statusCode! < 500;
    } catch (e) {
      debugPrint('API连接测试失败: $e');
      return false;
    }
  }
  
  /// 获取服务状态
  Map<String, dynamic> getStatus() {
    return {
      'base_url': _dio.options.baseUrl, // 修正：使用 baseUrl 而不是 baseURL
      'timeout_seconds': _dio.options.connectTimeout?.inSeconds,
      'service_name': 'DeviceApiService',
      'version': '1.0.0',
    };
  }
}
