enum AuthMethod {
  face,                     // 人脸识别
  idCard,                   // 身份证
  readerCard,               // 读者证
  qrCode,                   // 二维码
  socialSecurityCard,       // 社保卡
  citizenCard,              // 市民卡
  eletricSocialSecurityCard, // 电子社保卡
  wechatQRCode,             // 微信二维码
  wechatScanQRCode,         // 微信扫码
  alipayQRCode,             // 支付宝二维码
  aliCreditQRCode,          // 芝麻信用码
  huiwenQRCode,             // 汇文二维码
  shangHaiQRCode,           // 上海随申码
  keyboardInput,            // 手动输入
  tencentTCard,             // 腾讯E证通
  imiAuth,                  // IMI身份认证
  takePhoto,                // 拍照认证
  wechatOrAlipay,           // 微信/支付宝
  alipayQRCodeCredit,       // 支付宝信用认证
  jieYueBao,                // 借阅宝
}

enum AuthStatus {
  success,
  failureNoMatch,
  failureError,
  failureTimeout,  // 添加超时状态
}

class AuthResult {
  final String? userId;
  final String? userName;
  final AuthMethod method;
  final AuthStatus status;
  final DateTime timestamp;
  final String? errorMessage; // 新增：错误信息字段
  final dynamic readerInfo; // 新增：读者信息字段，用于认证后处理

  AuthResult({
    this.userId,
    this.userName,
    required this.method,
    required this.status,
    DateTime? timestamp,
    this.errorMessage, // 新增：错误信息参数
    this.readerInfo, // 新增：读者信息参数
  }) : timestamp = timestamp ?? DateTime.now();
}