import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:dio/dio.dart';

import '../models/book_info.dart';

/// 书籍信息API服务
/// 负责从后端API获取书籍详细信息和借阅状态
class BookInfoApiService {
  static BookInfoApiService? _instance;
  static BookInfoApiService get instance => _instance ??= BookInfoApiService._();
  BookInfoApiService._();
  
  // HTTP客户端
  final Dio _dio = Dio();
  
  // 🚫 移除缓存：书籍信息需要实时获取，确保借阅状态准确性
  
  // 请求配置
  String _baseUrl = 'http://your-api-server.com'; // 替换为实际API地址
  final Duration _timeout = Duration(seconds: 5);
  final int _maxRetries = 3;
  final Duration _retryDelay = Duration(milliseconds: 500);
  
  // 并发控制
  final Map<String, Future<BookInfo?>> _pendingRequests = {};
  final int _maxConcurrentRequests = 10;
  int _currentRequests = 0;
  
  /// 初始化服务
  void initialize({
    String? baseUrl,
    Duration? timeout,
  }) {
    _baseUrl = baseUrl ?? _baseUrl;
    
    // 配置Dio
    _dio.options = BaseOptions(
      baseUrl: _baseUrl,
      connectTimeout: timeout ?? _timeout,
      receiveTimeout: timeout ?? _timeout,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    );
    
    debugPrint('书籍API服务初始化完成: $_baseUrl');
  }
  
  /// 获取单本书籍信息（实时获取，不缓存）
  Future<BookInfo?> getBookInfo(String barcode) async {
    if (barcode.isEmpty) return null;

    // 检查是否已有相同请求（防止重复请求，但不缓存结果）
    if (_pendingRequests.containsKey(barcode)) {
      debugPrint('等待现有请求完成: $barcode');
      return await _pendingRequests[barcode]!;
    }

    // 并发控制
    if (_currentRequests >= _maxConcurrentRequests) {
      await _waitForAvailableSlot();
    }

    // 创建新请求
    final future = _requestBookInfoWithRetry(barcode);
    _pendingRequests[barcode] = future;
    _currentRequests++;

    try {
      final result = await future;
      debugPrint('实时获取图书信息: $barcode - ${result?.bookName ?? "未找到"}');
      return result;
    } finally {
      _pendingRequests.remove(barcode);
      _currentRequests--;
    }
  }
  
  /// 批量获取书籍信息（实时获取，不缓存）
  Future<List<BookInfo>> getBooksInfo(List<String> barcodes) async {
    if (barcodes.isEmpty) return [];

    try {
      debugPrint('批量实时获取书籍信息，数量: ${barcodes.length}');

      // 直接批量请求所有书籍信息
      final books = await _batchRequestBooksInfo(barcodes);

      debugPrint('批量获取完成，总数: ${books.length}');
      return books;
    } catch (e) {
      debugPrint('批量获取书籍信息失败: $e');
      return [];
    }
  }
  
  /// 带重试的单个书籍信息请求
  Future<BookInfo?> _requestBookInfoWithRetry(String barcode) async {
    Exception? lastException;
    
    for (int attempt = 1; attempt <= _maxRetries; attempt++) {
      try {
        debugPrint('请求书籍信息: $barcode (第${attempt}次尝试)');
        return await _requestSingleBookInfo(barcode);
      } catch (e) {
        lastException = e as Exception;
        debugPrint('请求失败: $barcode, 错误: $e');
        
        if (attempt < _maxRetries) {
          // 指数退避重试
          final delay = Duration(
            milliseconds: _retryDelay.inMilliseconds * attempt
          );
          await Future.delayed(delay);
        }
      }
    }
    
    debugPrint('请求书籍信息最终失败: $barcode, 错误: $lastException');
    return null;
  }
  
  /// 单个书籍信息请求
  Future<BookInfo?> _requestSingleBookInfo(String barcode) async {
    try {
      final response = await _dio.post(
        '/api/books/info',
        data: {'barcode': barcode},
      );
      
      if (response.statusCode == 200 && response.data != null) {
        return BookInfo.fromJson(response.data);
      } else {
        throw Exception('API返回错误状态: ${response.statusCode}');
      }
    } catch (e) {
      // 如果API调用失败，返回模拟数据用于测试
      debugPrint('API调用失败，返回模拟数据: $barcode, 错误: $e');
      return _generateMockBookInfo(barcode);
    }
  }
  
  /// 批量请求书籍信息
  Future<List<BookInfo>> _batchRequestBooksInfo(List<String> barcodes) async {
    try {
      final response = await _dio.post(
        '/api/books/batch-info',
        data: {'barcodes': barcodes},
      );
      
      if (response.statusCode == 200 && response.data != null) {
        final List<dynamic> data = response.data['books'] ?? [];
        return data.map((item) => BookInfo.fromJson(item)).toList();
      } else {
        throw Exception('批量API返回错误状态: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('批量请求失败，回退到单个请求: $e');
      
      // 回退到单个请求
      final books = <BookInfo>[];
      for (String barcode in barcodes) {
        try {
          final book = await _requestSingleBookInfo(barcode);
          if (book != null) {
            books.add(book);
          }
        } catch (e) {
          debugPrint('单个请求也失败: $barcode, $e');
        }
      }
      return books;
    }
  }
  
  /// 生成模拟书籍信息（用于测试）
  BookInfo _generateMockBookInfo(String barcode) {
    final random = Random();
    final bookNumber = barcode.replaceAll(RegExp(r'[^0-9]'), '');
    final isBorrowed = random.nextDouble() > 0.3; // 70%概率已借
    
    return BookInfo(
      barcode: barcode,
      bookName: '图书名称_$bookNumber',
      author: '作者${random.nextInt(100)}',
      isbn: '978-${random.nextInt(1000000000)}',
      isBorrowed: isBorrowed,
      borrowDate: isBorrowed 
          ? DateTime.now().subtract(Duration(days: random.nextInt(30)))
          : null,
      returnDate: isBorrowed
          ? DateTime.now().add(Duration(days: random.nextInt(30)))
          : null,
      borrowerName: isBorrowed ? '借阅者${random.nextInt(100)}' : null,
      borrowerId: isBorrowed ? 'USER${random.nextInt(10000)}' : null,
    );
  }
  
  /// 等待可用请求槽位
  Future<void> _waitForAvailableSlot() async {
    while (_currentRequests >= _maxConcurrentRequests) {
      await Future.delayed(Duration(milliseconds: 100));
    }
  }
  

  
  /// 获取服务状态
  Map<String, dynamic> getStatus() {
    return {
      'base_url': _baseUrl,
      'timeout_seconds': _timeout.inSeconds,
      'current_requests': _currentRequests,
      'max_concurrent_requests': _maxConcurrentRequests,
      'cache_disabled': true, // 标记缓存已禁用
      'real_time_data': true, // 标记使用实时数据
      'service_name': 'BookInfoApiService',
      'version': '2.0.0', // 版本升级，移除缓存
    };
  }
  
  /// 测试API连接
  Future<bool> testConnection() async {
    try {
      final response = await _dio.get('/api/health').timeout(Duration(seconds: 5));
      return response.statusCode == 200;
    } catch (e) {
      debugPrint('API连接测试失败: $e');
      return false;
    }
  }
}
