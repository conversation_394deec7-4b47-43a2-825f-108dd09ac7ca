# 上传状态

## OpenAPI Specification

```yaml
openapi: 3.0.1
info:
  title: ''
  description: ''
  version: 1.0.0
paths:
  /v1/api/door/status:
    post:
      summary: 上传状态
      deprecated: false
      description: ''
      tags:
        - 清华门径
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                deviceMac:
                  type: string
                  description: 设备MAC
                status:
                  type: integer
                  description: 1=在线, 2=故障, 3=离线
              required:
                - deviceMac
                - status
              x-apifox-orders:
                - deviceMac
                - status
            example:
              deviceMac: FFFFFFFF
              status: 1
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  errorCode:
                    type: integer
                    description: 0成功, 其他失败
                  message:
                    type: string
                    description: 提示信息
                  data:
                    type: 'null'
                required:
                  - errorCode
                  - message
                  - data
                x-apifox-orders:
                  - errorCode
                  - message
                  - data
              example:
                errorCode: 0
                message: 上传成功!
                data: null
          headers: {}
          x-apifox-name: 成功
      security: []
      x-apifox-folder: 清华门径
      x-apifox-status: developing
      x-run-in-apifox: https://app.apifox.com/web/project/2805692/apis/api-325271577-run
components:
  schemas: {}
  securitySchemes: {}
servers:
  - url: http://************:9000
    description: 开发环境
security: []

```