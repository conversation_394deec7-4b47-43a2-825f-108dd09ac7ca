# 安全闸机项目

## 项目背景

本项目旨在开发一款集成了人脸识别、二维码扫描、IC卡读取等多种认证方式的智能门禁系统。系统将部署在特定的硬件终端上，用于提供安全、高效的人员身份验证，并根据验证结果执行相应操作（如开启门禁）和记录事件（如考勤）。

## 项目目标

1.  实现人脸识别、二维码扫描、IC卡读取等多种认证方式
2.  根据验证结果执行相应操作（如开启门禁）
3.  记录认证事件（考勤记录）

=====================================
<!-- 这是之前的命令，即将去掉

AA 00 64 80 00 00 06 D2        进馆开始流程
AA 00 65 80 00 00 07 2E        进馆结束流程

AA 00 C8 80 00 00 27 82        出馆开始流程
AA 00 C9 80 00 00 26 7E        出馆结束流程

AA 00 0A 80 00 00              到达指定位置

AA 00 02 01 00 00 48 72        进馆开门  失败
AA 00 01 01 00 00 48 36        出馆开门  成功
<!-- AA 00 02 01 00 00 48 72        失败信号 -->

AA 00 0F 80 00 00              尾随
AA 00 0B 80 00 00              开门有人

aa 00 02 81 00 00 49 9a  这个命令待定 -->




=====================================
准确的命令如下
增加闸机主控板控制一体闸闸开关。
波特率 ： 115200
终端验证通过RS485与闸机主控板进行通信。
通信协议命令如下：


命令  
AA 00 01 01 00 00 48 36	成功信号
AA 00 02 01 00 00 48 72	失败信号
控制命令
AA 00 03 01 00 00 49 8E	门右常开   进馆
AA 00 04 01 00 00 48 FA	门左常开   出馆

AA 00 00 01 00 00 49 CA           关闭 门常开


接收到的命令
AA 00 64 80 00 00 06 D2        进馆开始流程
AA 00 0A 80 00 00 1A 3A        进馆到位
AA 00 65 80 00 00 07 2E        进馆结束流程

AA 00 C8 80 00 00 27 82        出馆开始流程
AA 00 0A 80 00 00 1A 3A        出馆馆到位
AA 00 C9 80 00 00 26 7E        出馆结束流程


<!-- AA 00 0A 80 00 00                  到位信号 -->

<!-- 异常
AA 00 0F 80 00 00                   尾随 -->

 
一体闸485协议

开始标记（一字节）    地址（一字节）   命令（两字节）    数据长度（两字节）    数据 （两字节）      检验位（两字节）

=====================================

现在流程有变化

进馆 ：
收到 进馆开始流程命令  
收到 进馆到位信号  
显示进馆认证组件，进行读者认证，认证成功后，
发送 成功信号 （此时闸机会开门，然后会自动关门）
发送成功信号有一个约束条件，只有在收到 进馆到位信号8秒内 才可以发送，否则等待下一次 的 进馆到位信号
收到 进馆结束流程命令

出馆：
收到 出馆开始流程命令  
收到 出馆到位信号  显示认证组件，进行读者认证，此时不关注结果，请求发送成功就行，读者认证的参数要发送正确
开始 从共享池 获取条码到自己维护的集合，
暂定5秒钟停止获取，
检查自己集合的书籍是否都已借出
如果都已借出，发送 成功信号 （此时闸机会出馆开门，然后会自动关门）
发送成功信号有一个约束条件，只有在收到 出馆到位信号8秒内 才可以发送，否则等待下一次 的 出馆馆到位信号
收到 出馆结束流程命令



enum GateState {
  idle,             // 空闲
  enterStarted,      // 进馆开始
  enterScanning,    // 进馆扫描中
  enterOpening,     // 进馆开门中
  enterOver,        // 进馆结束
  exitStarted,      // 出馆开始
  exitScanning,     // 出馆RFID扫描中
  exitChecking,     // 出馆书籍检查中
  exitOver,         // 出馆结束
  error             // 异常状态
}



数据同步流程

在程序中，可被设置成主机、从机、
主机会连接rfid阅读器，持续不断的获取扫描到的条码，然后维护一个共享池，是一个Set集合，天然会去重，，然后主机/从机自己维护自己的一个set 集合， 该集合的数据 会一直从主机的共享池获取，  自己的集合不是被新数据覆盖，而是追加

主机/从机 开始获取数据的时机是   各自 出馆开始到 到达指定位置 + 2秒  这个时间段内 

共享池和 各自的集合 清空时是 各自 出馆开始时

这样下来 各自的集合都是独立的，不会相互影响，随后会拿着数据做后续处理


从机的数据都来自主机共享池   从机不会启动本地的RFID去扫描


rfid阅读器会持续扫描     扫描的数据 会持续往共享池存放     你明白吗？仔细看代码 是否这样





完整的数据流架构：
RFID阅读器 → 共享池 → 主机集合A / 从机集合A → 书籍信息查询
🔧 详细流程：
第一层：硬件扫描 → 共享池
RFID阅读器持续扫描 → 检测到标签 → 解析条码
立即放入共享池（无条件，不管任何业务逻辑）
共享池：纯粹的数据存储，所有扫描到的条码都在这里
第二层：共享池 → 各自维护的集合A
主机：在特定时机（如出馆开始）从共享池拿数据 → 放入主机集合A
从机：在特定时机（如出馆开始）从共享池拿数据 → 放入从机集合A
关键：主机和从机都是从同一个共享池获取数据，但放入各自独立的集合A
第三层：监听集合A → 请求书籍信息
主机：监听主机集合A的变化 → 发现新条码 → 异步请求书籍信息
从机：监听从机集合A的变化 → 发现新条码 → 异步请求书籍信息
各自独立：主机和从机分别基于自己的集合A进行书籍信息查询
🎯 关键理解：
共享池：
只负责存储RFID扫描到的原始条码
主从机之间的数据共享通道
不涉及任何业务逻辑
集合A：
主机有自己的集合A，从机有自己的集合A
都是从共享池在特定时机获取数据
这是真正用于业务逻辑的数据集合
书籍信息查询：
完全基于各自的集合A
与共享池和RFID扫描解耦
主机和从机独立进行


收到出馆开始命令 → 清空共享池 → 清空主机集合A → 等待新标签进入共享池 → 同步到集合A
收到出馆开始命令 → 请求主机清空共享池 → 清空从机集合A → 从主机同步新数据到集合A





现在流程有点变化

进馆流程：
收到 进馆开始信号   显示认证组件进行认证  到时候会请求读者认证接口，需要关注响应结果，需要提示失败时的错误提示    收到进馆结束信号
出馆流程：
收到 出馆开始信号   显示认证组件进行认证 到时候会请求读者认证接口，不需要关注响应结果，请求成功就行   开启rfid扫描   收到 到达指定位置信号  关闭rfid扫描   收到出馆结束信号
出馆可能会收到多个 到达指定位置信号，以出馆认证后的第一个为准



出馆流程中，
首先收到  AA 00 C8 80 00 00 27 82        出馆开始流程
AA 00 0A 80 00 00 1A 3A        出馆馆到位
开始从共享池获取数据

检查书是否在白名单  ===此接口 会返回 不在白名单里面的uid 是一个字符串"uid1,uid2,uid3"
如果有不在白名单的，则拿此uid ，请求接口=》根据标签UID查询条码, 拿到条码后 请求接口=》获取图书信息Get

全部通过后，才发送成功信号，，出馆发送成功信号没有9秒的约束，只要保证，接收到的上一个从闸机发过来的命令是出馆到位的信号，才可以发送成功信号，



在收到出馆到位后，应该先清空一下共享池，，清空各自的集合，清空各自的去重条件都要重置，不能保留上一次的数据，，，，从机是通过接口请求主机清空一次 共享池，，主机是直接清空共享池，，，主机从机 其实大部分逻辑都一致，，只不过数据  操作共享池的方法不一样而已，，你全面梳理逻辑，，认真分析




阶段	主机逻辑	从机逻辑	
出馆开始	等待到位信号	 等待到位信号 	
出馆到位	直接清空共享池(自己的集合和去重都要清空)	请求主机清空共享池(自己的集合和去重都要清空)	
数据收集	从共享池收集	启动持续数据获取从主机请求数据	
出馆结束	停止收集	停止数据获取	