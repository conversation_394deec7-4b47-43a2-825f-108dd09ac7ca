import 'dart:async';
import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:hardware/hardware.dart' as hw;

import '../models/gate_command.dart';
import 'package:seasetting/seasetting.dart';
import 'package:provider/provider.dart';
import 'package:get/get.dart';

/// 闸机串口通信服务
class GateSerialService {
  static GateSerialService? _instance;
  static GateSerialService get instance => _instance ??= GateSerialService._();
  GateSerialService._();

  // 统一改用硬件库 GateSerialManager 作为串口通道
  final hw.GateSerialManager _gateManager = hw.GateSerialManager.instance;
  StreamSubscription? _gateEventSub;

  // 状态标志
  bool _isInitialized = false;
  bool _isListening = false;
  bool _simulationMode = false; // 无可用配置/串口时进入模拟模式，保证应用可启动

  // 配置参数（统一通过 SettingProvider 获取，不使用默认值）
  String? _portName;
  int? _baudRate;

  // 事件流
  final StreamController<GateCommand> _commandController =
      StreamController<GateCommand>.broadcast();
  Stream<GateCommand> get commandStream => _commandController.stream;

  // 错误流
  final StreamController<String> _errorController =
      StreamController<String>.broadcast();
  Stream<String> get errorStream => _errorController.stream;

  /// 初始化串口服务
  Future<void> initialize({
    String? portName,
    int? baudRate,
  }) async {
    if (_isInitialized) {
      debugPrint('闸机串口服务已经初始化');
      return;
    }

    // 优先使用传入的参数（仅用于测试），否则统一从SettingProvider获取安全闸机配置
    if (portName != null || baudRate != null) {
      _portName = portName;
      _baudRate = baudRate;
      debugPrint('使用传入的串口参数: $_portName @ $_baudRate（测试模式）');
    } else {
      // 统一数据来源：必须通过 SettingProvider 获取串口配置
      await _loadSerialConfigFromSettingProvider();
    }

    // 严格校验：确保配置完整
    if (_portName == null || _portName!.isEmpty || _baudRate == null) {
      // 兜底：尝试自动选择一个可用串口并使用常见波特率，保证应用可进入
      try {
        final ports = await _gateManager.getAvailablePorts();
        if (ports.isNotEmpty) {
          _portName = ports.first;
          _baudRate = 115200; // 常见默认值
          _simulationMode = true; // 标记为兜底模式
          debugPrint('⚠️ 未读取到配置，兜底使用首个串口: $_portName @ $_baudRate（模拟模式）');
        }
      } catch (_) {}

      if (_portName == null || _portName!.isEmpty || _baudRate == null) {
        // 无法兜底，进入纯模拟模式，不再抛异常，应用继续
        _simulationMode = true;
        debugPrint('⚠️ 未读取到配置且无可用串口，进入模拟模式，后续不会连接硬件');
        _isInitialized = true;
        return;
      }
    }

    try {
      if (_simulationMode) {
        // 模拟模式下不打开串口，直接视为初始化成功
        _isInitialized = true;
        debugPrint('闸机串口服务以模拟模式初始化成功');
      } else {
        await _openSerialPort();
        _isInitialized = true;
        debugPrint('闸机串口服务初始化成功: $_portName');
      }
    } catch (e) {
      final errorMsg = '闸机串口服务初始化失败: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
      // 失败兜底：进入模拟模式，避免应用无法启动
      _simulationMode = true;
      _isInitialized = true;
      debugPrint('⚠️ 串口打开失败，已切换至模拟模式');
    }
  }

  /// 开始监听串口数据
  Future<void> startListening() async {
    if (!_isInitialized) {
      throw Exception('串口服务未初始化');
    }

    if (_simulationMode) {
      // 模拟模式下不启动真实监听，但为了上层流程，直接打印并返回
      debugPrint('模拟模式：跳过真实串口监听，使用 UI/调试按钮或主从机流程触发');
      _isListening = true;
      return;
    }

    if (_isListening) {
      debugPrint('串口监听已经启动');
      return;
    }

    try {
      _startReading();
      _isListening = true;
      debugPrint('开始监听闸机串口命令');
    } catch (e) {
      final errorMsg = '启动串口监听失败: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
      rethrow;
    }
  }

  /// 停止监听串口数据
  Future<void> stopListening() async {
    if (!_isListening) return;
    try {
      await _gateEventSub?.cancel();
      _gateEventSub = null;
      _isListening = false;
      debugPrint('停止监听闸机串口命令');
    } catch (e) {
      debugPrint('停止串口监听失败: $e');
    }
  }

  /// 发送命令到闸机
  Future<bool> sendCommand(String commandType) async {
    if (!_isInitialized || !_gateManager.isConnected) {
      debugPrint('串口未初始化或未连接，模拟发送命令: $commandType');
      // 在没有真实硬件时，模拟命令发送成功，不影响其他功能
      return true;
    }

    try {
      if (_simulationMode) {
        debugPrint('模拟模式：忽略发送硬件命令 $commandType');
        return true;
      }

      bool result = false;
      switch (commandType) {
        case 'success_signal':
          result = await _sendRawCommand(GateCommand.getSendCommandData('success_signal')!);
          break;
        case 'failure_signal':
          result = await _sendRawCommand(GateCommand.getSendCommandData('failure_signal')!);
          break;
        case 'enter_door_open':
          result = await _sendRawCommand(GateCommand.getSendCommandData('enter_door_open')!);
          break;
        case 'exit_door_open':
          result = await _sendRawCommand(GateCommand.getSendCommandData('exit_door_open')!);
          break;
        case 'close_door':
          result = await _sendRawCommand(GateCommand.getSendCommandData('close_door')!);
          break;
        default:
          debugPrint('未知的发送命令类型: $commandType');
          return false;
      }

      if (result) {
        debugPrint('闸机命令发送成功: $commandType');
      } else {
        debugPrint('闸机命令发送失败: $commandType (硬件可能未连接，但不影响系统功能)');
      }

      return result;
    } catch (e) {
      final errorMsg = '发送闸机命令异常: $commandType, 错误: $e (硬件可能未连接，但不影响系统功能)';
      debugPrint(errorMsg);
      // 不再将硬件发送失败作为系统错误，避免影响其他功能
      // _errorController.add(errorMsg);
      return false;
    }
  }

  /// 打开串口
  Future<void> _openSerialPort() async {
    try {
      // 优先使用配置端口，否则尝试第一个可用端口
      final ports = await _gateManager.getAvailablePorts();
      String? target = ports.contains(_portName) ? _portName : (ports.isNotEmpty ? ports.first : null);
      if (target == null) {
        throw Exception('未发现可用串口');
      }
      final ok = await _gateManager.connectGate(target, baudRate: _baudRate!);
      if (!ok) {
        throw Exception('连接串口失败: $target');
      }

      // 建立事件订阅（接收硬件命令）
      _gateEventSub?.cancel();
      _gateEventSub = _gateManager.gateEventStream.listen((event) {
        if (event is hw.GateCommandReceivedEvent) {
          _handleGateReceived(event);
        }
      });

      debugPrint('串口 $target 连接成功 (波特率: $_baudRate)');
    } catch (e) {
      throw Exception('打开串口失败: $e');
    }
  }

  /// 开始读取串口数据
  void _startReading() {
    // 已由 _openSerialPort 中建立 gateEventStream 订阅
    debugPrint('开始监听串口数据（通过 GateSerialManager 事件流）');
  }

  /// 处理接收到的数据
  void _handleGateReceived(hw.GateCommandReceivedEvent evt) {
    if (!_isListening) return;
    try {
      final localType = _mapHwToLocalType(evt.command);
      if (localType != null) {
        final cmd = GateCommand(type: localType, data: evt.rawData);
        debugPrint('解析到闸机命令: ${cmd.type} (${cmd.displayName})');
        _commandController.add(cmd);
      } else {
        debugPrint('收到未映射的闸机命令: ${evt.command}');
      }
    } catch (e) {
      final errorMsg = '解析闸机命令失败: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
    }
  }

  /// 获取串口状态信息
  Map<String, dynamic> getStatus() {
    final info = _gateManager.connectionInfo;
    return {
      'initialized': _isInitialized,
      'listening': _isListening,
      'port_name': info['portName'],
      'baud_rate': info['baudRate'],
      'port_open': info['isConnected'] == true,
      'available_ports': 'N/A',
    };
  }

  /// 重新连接串口
  Future<bool> reconnect() async {
    try {
      debugPrint('尝试重新连接串口...');
      await stopListening();
      await _gateManager.disconnectGate();
      _isInitialized = false;
      await initialize(portName: _portName, baudRate: _baudRate);
      await startListening();
      debugPrint('串口重新连接成功');
      return true;
    } catch (e) {
      final errorMsg = '串口重新连接失败: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
      return false;
    }
  }

  /// 检查串口连接状态
  bool get isConnected {
    return _isInitialized && !_simulationMode && _gateManager.isConnected;
  }

  /// 释放资源
  void dispose() {
    debugPrint('释放闸机串口服务资源');
    stopListening();
    _gateManager.disconnectGate();
    _commandController.close();
    _errorController.close();
    _isInitialized = false;
    _isListening = false;
    debugPrint('闸机串口服务已释放');
  }
  // 硬件命令 → 本地字符串类型映射
  String? _mapHwToLocalType(hw.GateCommand cmd) {
    switch (cmd) {
      case hw.GateCommand.enterStart:
        return GateCommand.enterStart;
      case hw.GateCommand.enterEnd:
        return GateCommand.enterEnd;
      case hw.GateCommand.exitStart:
        return GateCommand.exitStart;
      case hw.GateCommand.exitEnd:
        return GateCommand.exitEnd;
      case hw.GateCommand.reachPosition:
        return GateCommand.positionReached;
      case hw.GateCommand.tailgating:
        return GateCommand.tailgating;
      case hw.GateCommand.doorHasPerson:
        return GateCommand.doorBlocked;
      case hw.GateCommand.enterOpen:
      case hw.GateCommand.exitOpen:
      case hw.GateCommand.failSignal:
        return null; // 发送类命令不作为接收处理，包括失败信号
    }
  }

  // 🔥 修复：发送原始命令数据到真实硬件
  Future<bool> _sendRawCommand(List<int> commandData) async {
    try {
      debugPrint('发送原始命令数据: ${commandData.map((e) => e.toRadixString(16).padLeft(2, '0')).join(' ')}');

      if (!_isInitialized || !_gateManager.isConnected) {
        debugPrint('⚠️ 串口未连接，无法发送真实命令');
        return false;
      }

      if (_simulationMode) {
        debugPrint('🧪 模拟模式：跳过真实硬件发送');
        debugPrint('发送数据成功: ${commandData.map((e) => e.toRadixString(16).padLeft(2, '0')).join(' ')}');
        return true;
      }

      // 🔥 使用硬件库的sendRawData方法发送原始数据
      final uint8Data = Uint8List.fromList(commandData);
      final success = await _gateManager.sendRawData(uint8Data);

      if (success) {
        debugPrint('发送数据成功: ${commandData.map((e) => e.toRadixString(16).padLeft(2, '0')).join(' ')}');
      } else {
        debugPrint('❌ 发送数据失败: ${commandData.map((e) => e.toRadixString(16).padLeft(2, '0')).join(' ')}');
      }

      return success;
    } catch (e) {
      debugPrint('发送原始命令失败: $e');
      return false;
    }
  }

  /// 统一通过 SettingProvider 加载串口配置（与主从机配置保持一致的数据来源）
  Future<void> _loadSerialConfigFromSettingProvider() async {
  try {
  // 1. 获取 SettingProvider 实例
  final settingProvider = Get.context?.read<SettingProvider>();
  if (settingProvider == null) {
    debugPrint('⚠️ SettingProvider 未就绪，跳过加载串口配置');
    return;
  }

  // 2. 清除缓存，强制从数据库重新读取
  try {
    (settingProvider as dynamic).clearSerialConfigCache();
    debugPrint('✅ 已清除 SettingProvider 串口配置缓存');
  } catch (e) {
    debugPrint('⚠️ 清除缓存失败（可能是旧版本 seasetting）: $e');
  }

  // 3. 尝试通过多种可能的接口获取 SecurityGateConfigs
  bool configLoaded = false;
      
  // 方法1：尝试专用的串口配置接口（如果 seasetting 提供）
  configLoaded = await _tryGetSerialConfigDirect(settingProvider);
  
      // 方法2：通过通用配置接口获取 SecurityGateConfigs
  if (!configLoaded) {
      configLoaded = await _tryGetSerialConfigFromSecurityGateConfigs(settingProvider);
  }

    if (!configLoaded) {
      debugPrint('⚠️ 未从 SettingProvider 获取到有效的串口配置，将在后续尝试兜底');
      return;
    }

    debugPrint('✅ 串口配置加载完成: $_portName @ $_baudRate');
  } catch (e) {
    debugPrint('⚠️ 从 SettingProvider 加载串口配置失败: $e，将在后续尝试兜底');
    return;
  }
}
  /// 方法1：尝试通过专用串口配置接口获取（如果 seasetting 提供专门的串口配置方法）
  Future<bool> _tryGetSerialConfigDirect(SettingProvider settingProvider) async {
    try {
      // 检查是否存在专用的串口配置方法
      final dynamic provider = settingProvider;

      // 尝试 getSerialConfig() 方法
      if (provider.getSerialConfig != null) {
        final result = await provider.getSerialConfig();
        if (_extractSerialConfigFromResult(result, 'getSerialConfig')) {
          return true;
        }
      }

      // 尝试 getGateSerialConfig() 方法
      if (provider.getGateSerialConfig != null) {
        final result = await provider.getGateSerialConfig();
        if (_extractSerialConfigFromResult(result, 'getGateSerialConfig')) {
          return true;
        }
      }

      return false;
    } catch (e) {
      debugPrint('尝试专用串口配置接口失败: $e');
      return false;
    }
  }

  /// 方法2：通过 SecurityGateConfigs 获取串口配置（与主从机配置类似的方式）
  Future<bool> _tryGetSerialConfigFromSecurityGateConfigs(SettingProvider settingProvider) async {
    try {
      final dynamic provider = settingProvider;

      // 尝试多种可能的方法名获取 SecurityGateConfigs
      final methods = [
        'getSecurityGateConfigs',
        'getSecurityGateConfig',
        'getSettingBy',
        'querySettingBy',
        'getConfig'
      ];

      for (final methodName in methods) {
        try {
          dynamic result;

          // 根据方法名调用对应的接口
          switch (methodName) {
            case 'getSettingBy':
            case 'querySettingBy':
            case 'getConfig':
              result = await (provider as dynamic).getSettingBy('SecurityGateConfigs');
              break;
            case 'getSecurityGateConfigs':
              result = await (provider as dynamic).getSecurityGateConfigs();
              break;
            case 'getSecurityGateConfig':
              result = await (provider as dynamic).getSecurityGateConfig();
              break;
            default:
              continue; // 跳过不支持的方法
          }

          if (_extractSerialConfigFromSecurityGateConfigs(result, methodName)) {
            return true;
          }
        } catch (e) {
          debugPrint('尝试方法 $methodName 失败: $e');
          continue;
        }
      }

      return false;
    } catch (e) {
      debugPrint('通过 SecurityGateConfigs 获取串口配置失败: $e');
      return false;
    }
  }

  /// 从专用串口配置结果中提取配置
  bool _extractSerialConfigFromResult(dynamic result, String methodName) {
    try {
      if (result == null) return false;

      Map<String, dynamic>? config;

      if (result is Map<String, dynamic>) {
        config = result;
      } else if (result is String) {
        config = jsonDecode(result) as Map<String, dynamic>?;
      } else {
        // 兼容 seasetting 返回 SerialConfig 对象
        try {
          final port = (result as dynamic).gatePort;
          final baud = (result as dynamic).baudRate;
          if (port != null && baud != null) {
            config = {
              'gatePort': port.toString(),
              'baudRate': baud is int ? baud : int.tryParse(baud.toString()),
            };
          }
        } catch (_) {}
      }

      if (config != null) {
        final port = config['gatePort'] ?? config['port'] ?? config['portName'];
        final baud = config['baudRate'] ?? config['baud'] ?? config['baudrate'];

        if (port != null && baud != null) {
          _portName = port.toString();
          _baudRate = baud is int ? baud : int.tryParse(baud.toString());

          if (_portName != null && _portName!.isNotEmpty && _baudRate != null) {
            debugPrint('✅ 通过 $methodName 获取串口配置: $_portName @ $_baudRate');
            return true;
          }
        }
      }

      return false;
    } catch (e) {
      debugPrint('解析 $methodName 结果失败: $e');
      return false;
    }
  }

  /// 从 SecurityGateConfigs 中提取串口配置
  bool _extractSerialConfigFromSecurityGateConfigs(dynamic result, String methodName) {
    try {
      if (result == null) return false;

      // 处理不同的返回格式
      Map<String, dynamic>? configsData;

      if (result is String) {
        configsData = jsonDecode(result) as Map<String, dynamic>?;
      } else if (result is List && result.isNotEmpty) {
        final first = result.first;
        if (first is String) {
          configsData = jsonDecode(first) as Map<String, dynamic>?;
        } else if (first is Map) {
          configsData = Map<String, dynamic>.from(first);
        }
      } else if (result is Map) {
        configsData = Map<String, dynamic>.from(result);
      }

      if (configsData == null) return false;

      // 查找启用的配置项
      for (final entry in configsData.entries) {
        if (entry.value is Map) {
          final config = Map<String, dynamic>.from(entry.value as Map);
          if (config['enabled'] == true) {
            final serialConfig = config['serialConfig'] as Map<String, dynamic>?;
            if (serialConfig != null) {
              final port = serialConfig['gatePort'];
              final baud = serialConfig['baudRate'];

              if (port != null && baud != null) {
                _portName = port.toString();
                _baudRate = baud is int ? baud : int.tryParse(baud.toString());

                if (_portName != null && _portName!.isNotEmpty && _baudRate != null) {
                  debugPrint('✅ 通过 $methodName 从 SecurityGateConfigs 获取串口配置: $_portName @ $_baudRate');
                  return true;
                }
              }
            }
          }
        }
      }

      return false;
    } catch (e) {
      debugPrint('从 SecurityGateConfigs 解析串口配置失败: $e');
      return false;
    }
  }



}
