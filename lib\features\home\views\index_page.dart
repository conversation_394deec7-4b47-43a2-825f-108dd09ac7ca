import 'dart:async';
import 'dart:convert';

import 'package:base_package/base_package.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:hardware/hardware.dart';
import 'package:path_provider/path_provider.dart';
import 'package:provider/provider.dart';
import 'package:sea_socket/sea_socket.dart';
import 'package:sealog/sealog.dart';
import 'package:seasetting/seasetting.dart';

import '../../../core/router/app_router.dart';
import './home_page.dart';
import '../../../core/utils/window_util.dart';
import '../../security_gate/services/gate_coordinator.dart';
import '../../security_gate/services/gate_config_service.dart';

class IndexPage extends StatefulWidget {
  const IndexPage({super.key});

  @override
  State<IndexPage> createState() => _IndexPageState();
}

class _IndexPageState extends SEPageState<IndexPage> {
  static const double _toastFontSize = 30.0;
  static const double _toastIndicatorSize = 70.0;
  static const double _toastPadding = 15.0;

  @override
  void initState() {
    super.initState();
    NetworkManager.instance;
    AppPageBuildRefresh.instance.refreshAction = _refreshAction;
    _initToast();
  }

  void _refreshAction(BuildContext ctx) {
    WindowUtil.updateScale(ctx);
  }

  void _initToast() {
    final instance = EasyLoading.instance;
    instance
      ..fontSize = _toastFontSize
      ..indicatorSize = _toastIndicatorSize
      ..contentPadding = const EdgeInsets.all(_toastPadding);
  }

  @override
  void didPopNext() {
    super.didPopNext();
    // context.go(AppRoutes.home);
    // AppNavigator.toHome();
  }

  @override
  didPush() {
    super.didPush();

    try {
      EasyLoading.show(status: '初始化中...');

      Future.wait<void>([
        _initLogPath(),
        _loadSip2Xmls(),
        HWUtils.copyBundles(),
      ]).then((_) async {
        try {
          await _initializeConfigurations();

          // 初始化安全闸机系统
          await _initializeSecurityGate();

          EasyLoading.dismiss();

          // 直接跳转到安全闸机主页
          AppNavigator.offAllToGateHome();

        } catch (e) {
          _handleError('配置初始化失败: $e');
        }
      }).catchError((error) {
        _handleError('基础服务初始化失败: $error');
      });

    } catch (e) {
      _handleError('未知错误: $e');
    }
  }

  void _handleError(String message) {
    debugPrint(message);
    EasyLoading.dismiss();

    EasyLoading.showError(
      '初始化失败，请重试',
      duration: const Duration(seconds: 3),
      dismissOnTap: true,
    );
  }

  Future<void> _initLogPath() async {
    final supportDir = await getApplicationSupportDirectory();
    SLLogCache.instance().appSupportPath = supportDir.path;
  }

  Future<void> _loadSip2Xmls() async {
    try {
      await XmlUtil.copySip2Xmls();
      await XmlUtil.loadAllSiip2Xmls();
    } catch (e) {
      debugPrint('加载XML失败: $e');
      rethrow;
    }
  }

  /// 初始化安全闸机系统
  Future<void> _initializeSecurityGate() async {
    try {
      // 初始化闸机协调器
      await GateCoordinator.instance.initialize();

      // 🔥 新增：初始化主从机扩展
      await _initializeMasterSlaveExtension();

      debugPrint('安全闸机系统初始化完成');
    } catch (e) {
      debugPrint('安全闸机系统初始化失败: $e');
      rethrow;
    }
  }

  /// 🔥 修改：使用GateConfigService初始化主从机扩展
  Future<void> _initializeMasterSlaveExtension() async {
    try {
      debugPrint('🔧 开始初始化主从机扩展（使用持久化配置）...');

      // 🔥 使用GateConfigService加载持久化配置
      await GateConfigService.instance.initialize();

      final config = GateConfigService.instance.currentConfig;
      if (config != null) {
        debugPrint('✅ 加载到持久化配置: ${config.isMaster ? "主机" : "从机"}模式, 通道: ${config.channelId}');
      } else {
        debugPrint('⚠️ 未找到持久化配置，使用默认主机模式');

        // 如果没有配置，设置默认主机模式并保存
        await GateConfigService.instance.configureAsMaster(
          channelId: 'channel_1',
          port: 8888,
        );
        debugPrint('✅ 默认主机模式配置已保存');
      }

      debugPrint('主从机扩展初始化完成');
    } catch (e) {
      debugPrint('主从机扩展初始化失败: $e');
      // 不抛出异常，允许系统继续运行（主从机功能是可选的）
    }
  }

  Future<void> _initializeConfigurations() async {
    final dbProvider = await context.read<DBProvider>().initDBType();
    final db = await DBSettingManager.getDBInstance();

    // 并行查询所有配置
    final configResults = await Future.wait([
      db.querySettingBy('SysConfig'),
      db.querySettingBy('Sip2Config'),
      db.querySettingBy('pageConfig'),
      db.querySettingBy('readerConfig'),
      db.querySettingBy('banZhengConfig'),
      db.querySettingBy('printConfig'),
      db.querySettingBy('planConfig'),
    ]);
    
    // 预先获取默认配置
    final defaultSip2ConfigData = await Sip2ConfigData.defaultValue;
    
    // 方案一：直接在主线程中执行解析配置，传入db对象
    final configs = await _parseConfigurations(configResults, defaultSip2ConfigData, db);

    // 应用配置
    await _applyConfigurations(configs, db);

    // 初始化其他服务
    await _initializeServices(configs.planConfig);
  }

  Future<void> _applyConfigurations(Configs configs, dynamic db) async {
    // 设置布局
    _setLayout(configs.sysConfigOption);

    // 设置语言
    _setLocale(configs.sysConfigOption);

    // 更新所有配置到 Provider
    if (!mounted) return;
    
    context.read<SettingProvider>()
      ..changePlanConfig(configs.planConfig)
      ..changeSysConfigOptions(configs.sysConfigOption)
      ..changeSip2ConfigData(configs.sip2configData)
      ..changePageControlConfig(configs.pageControlConfig)
      ..changeReaderConfigData(configs.readerConfigData)
      ..changeBanZhengConfigModel(configs.banZhengConfigModel)
      ..changePrintConfigData(configs.printConfigData);
  }

  void _setLayout(SysConfigOptions sysConfigOption) {
    final style = sysConfigOption.options
        .where((element) => element.name == '风格')
        .firstOrNull;
    if (style != null && mounted) {
      context.read<CurrentLayoutProvider>().setLocale(
          style.data.name == 'vertical'
              ? SeaLayout.vertical
              : SeaLayout.horizontal);
    }
  }

  void _setLocale(SysConfigOptions sysConfigOption) {
    final lanData = sysConfigOption.options
        .where((element) => element.name == '语言')
        .firstOrNull;
    if (lanData != null && mounted) {
      context.read<CurrentLocale>().setLocale(Locale(lanData.data.name));
    }
  }

  Future<void> _initializeServices(PlanConfigModel planConfig) async {
    await ShutdownManager.instance.createIsolate();
    ShutdownManager.instance.updateConfig(planConfig.shutdownConfig!);
    NewSip2Request.instance.connectSocket();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return const Scaffold(
      body: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _VerifyButton(),
            Center(
              child: CircularProgressIndicator(),
            ),
          ],
        ),
      ),
    );
  }
}

class _VerifyButton extends StatelessWidget {
  const _VerifyButton({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      // onDoubleTap: () => Get.to(VerifyAccountPage(VerifyAccountType.local)),
      child: Container(
        width: 200.p,
        height: 200.p,
        color: Colors.transparent,
      ),
    );
  }
}

// 配置数据类
class Configs {
  final SysConfigOptions sysConfigOption;
  final Sip2ConfigData sip2configData;
  final PageControlConfig pageControlConfig;
  final ReaderConfigData readerConfigData;
  final BanZhengConfigModel banZhengConfigModel;
  final List<PrintModel> printConfigData;
  final PlanConfigModel planConfig;

  Configs({
    required this.sysConfigOption,
    required this.sip2configData,
    required this.pageControlConfig,
    required this.readerConfigData,
    required this.banZhengConfigModel,
    required this.printConfigData,
    required this.planConfig,
  });
}

// 修改解析函数接收默认配置
Future<Configs> _parseConfigurations(
  List<List<String>> configResults, 
  Sip2ConfigData defaultSip2ConfigData,
  DBSettingInterface db
) async {
  final [
    sysConfigList,
    sip2ConfigList,
    pageConfigList,
    readerConfigList,
    banZhengConfigList,
    printConfigList,
    planConfigList,
  ] = configResults;
  // WidgetsFlutterBinding.ensureInitialized();

  // 使用传入的默认配置
  final sip2configData = sip2ConfigList.isEmpty
      ? defaultSip2ConfigData
      : Sip2ConfigData.fromJson(jsonDecode(sip2ConfigList.first));
      if (sip2ConfigList.isEmpty){
        db.insertSetting('Sip2Config', jsonEncode(defaultSip2ConfigData));
      }
  // 解析系统配置
  final sysConfigOption = sysConfigList.isEmpty
      ? SysConfigOptions(SysConfigOptions.defaultOptions)
      : SysConfigOptions.fromJson(jsonDecode(sysConfigList.first));
  if (sysConfigList.isEmpty){
    db.insertSetting('SysConfig', jsonEncode(SysConfigOptions(SysConfigOptions.defaultOptions)) );
  }

  // 解析计划配置
  final planConfig = planConfigList.isEmpty
      ? PlanConfigModel.fromJson(PlanConfigModel.defaultValue.toJson())
      : PlanConfigModel.fromJson(jsonDecode(planConfigList.first));
  if (planConfigList.isEmpty){
    db.insertSetting('planConfig', jsonEncode(PlanConfigModel.fromJson(PlanConfigModel.defaultValue.toJson())) );
  }

  // 解析页面控制配置
  final pageControlConfig = pageConfigList.isEmpty
      ? PageControlConfig.defaultValue
      : PageControlConfig.fromJson(jsonDecode(pageConfigList.first));
  if (pageConfigList.isEmpty){
    db.insertSetting('pageConfig', jsonEncode(PageControlConfig.defaultValue) );
  }

  // 解析读者配置
  final readerConfigData = readerConfigList.isEmpty
      ? ReaderConfigData.defaultValue
      : ReaderConfigData.fromJson(jsonDecode(readerConfigList.first));
  if (readerConfigList.isEmpty){
    db.insertSetting('readerConfig', jsonEncode(ReaderConfigData.defaultValue) );
  }

  // 解析办证配置
  final banZhengConfigModel = _parseBanZhengConfig(db,banZhengConfigList);

  // 解析打印配置
  final printConfigData = _parsePrintConfig(db,printConfigList);

  return Configs(
    sysConfigOption: sysConfigOption,
    sip2configData: sip2configData,
    pageControlConfig: pageControlConfig,
    readerConfigData: readerConfigData,
    banZhengConfigModel: banZhengConfigModel,
    printConfigData: printConfigData,
    planConfig: planConfig,
  );
}

BanZhengConfigModel _parseBanZhengConfig(DBSettingInterface db,List<String> banZhengConfigList) {
  if (banZhengConfigList.isEmpty) {
    db.insertSetting('banZhengConfig', jsonEncode(BanZhengConfigModel(
      list: [
        BanZhengConfigData.defaultXinyongValue,
        BanZhengConfigData.defaultCardValue,
        BanZhengConfigData.defaultIDCardValue,
        BanZhengConfigData.defaultVirtualValue,
      ],
      otherData: BanZhengOtherData.defaultValue, smsConfig: BanZhengSmsConfig.fromJson({}),
    )) );
    return BanZhengConfigModel(
      list: [
        BanZhengConfigData.defaultXinyongValue,
        BanZhengConfigData.defaultCardValue,
        BanZhengConfigData.defaultIDCardValue,
        BanZhengConfigData.defaultVirtualValue,
      ],
      otherData: BanZhengOtherData.defaultValue, smsConfig: BanZhengSmsConfig.fromJson({}),
    );
  }

  dynamic json = jsonDecode(banZhengConfigList.first);
  return json is List
      ? BanZhengConfigModel(
      list: json.map((e) => BanZhengConfigData.fromJson(e)).toList(),
      otherData: BanZhengOtherData.defaultValue, smsConfig: BanZhengSmsConfig.fromJson({}))
      : BanZhengConfigModel.fromJson(json);
}

List<PrintModel> _parsePrintConfig(DBSettingInterface db,List<String> printConfigList) {
  if (printConfigList.isEmpty) {
    db.insertSetting('printConfig', jsonEncode(PrintModel.defaultList) );
    return PrintModel.defaultList;
  }

  final List<PrintModel> printConfigData = [];
  jsonDecode(printConfigList.first).forEach((e) {
    printConfigData.add(PrintModel.fromJson(e));
  });
  printConfigData.sort((a, b) => a.id - b.id);
  return printConfigData;
}