import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

import '../models/book_info.dart';

/// 书籍API服务
class BookApiService {
  static BookApiService? _instance;
  static BookApiService get instance => _instance ??= BookApiService._();
  BookApiService._();
  
  // API配置
  String _baseUrl = 'http://api.library.com'; // 实际API地址
  Duration _timeout = Duration(seconds: 10);
  
  /// 初始化服务
  void initialize({
    String? baseUrl,
    Duration? timeout,
  }) {
    _baseUrl = baseUrl ?? _baseUrl;
    _timeout = timeout ?? _timeout;
    debugPrint('书籍API服务初始化完成: $_baseUrl');
  }
  
  /// 批量检查书籍借阅状态
  Future<List<BookInfo>> checkBooksStatus(List<String> barcodes) async {
    if (barcodes.isEmpty) {
      return [];
    }
    
    try {
      debugPrint('开始检查书籍状态，条码数量: ${barcodes.length}');
      
      // TODO: 替换为实际的API调用
      // final response = await _makeApiRequest('/books/check', {
      //   'barcodes': barcodes,
      // });
      // 
      // if (response.statusCode == 200) {
      //   final data = jsonDecode(response.body);
      //   return (data['books'] as List)
      //       .map((item) => BookInfo.fromJson(item))
      //       .toList();
      // } else {
      //   throw Exception('API请求失败: ${response.statusCode}');
      // }
      
      // 暂时使用模拟数据
      return await _simulateApiCall(barcodes);
      
    } catch (e) {
      debugPrint('检查书籍状态失败: $e');
      // 发生错误时返回默认状态（未借）
      return barcodes.map((barcode) => BookInfo(
        barcode: barcode,
        bookName: '未知书籍',
        isBorrowed: false, // 默认为未借，确保安全
      )).toList();
    }
  }
  
  /// 模拟API调用（实际项目中删除）
  Future<List<BookInfo>> _simulateApiCall(List<String> barcodes) async {
    // 模拟网络延迟
    await Future.delayed(Duration(milliseconds: 800 + Random().nextInt(500)));
    
    final random = Random();
    return barcodes.map((barcode) {
      // 模拟不同的借阅状态
      final isBorrowed = random.nextDouble() > 0.3; // 70%概率已借
      final bookNumber = barcode.replaceAll(RegExp(r'[^0-9]'), '');
      
      return BookInfo(
        barcode: barcode,
        bookName: '图书名称_$bookNumber',
        author: '作者${random.nextInt(100)}',
        isbn: '978-${random.nextInt(1000000000)}',
        isBorrowed: isBorrowed,
        borrowDate: isBorrowed 
            ? DateTime.now().subtract(Duration(days: random.nextInt(30)))
            : null,
        returnDate: isBorrowed
            ? DateTime.now().add(Duration(days: random.nextInt(30)))
            : null,
        borrowerName: isBorrowed ? '借阅者${random.nextInt(100)}' : null,
        borrowerId: isBorrowed ? 'USER${random.nextInt(10000)}' : null,
      );
    }).toList();
  }
  
  /// 发送API请求
  Future<http.Response> _makeApiRequest(
    String endpoint, 
    Map<String, dynamic> data,
  ) async {
    final url = Uri.parse('$_baseUrl$endpoint');
    
    debugPrint('发送API请求: $url');
    debugPrint('请求数据: ${jsonEncode(data)}');
    
    final response = await http.post(
      url,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: jsonEncode(data),
    ).timeout(_timeout);
    
    debugPrint('API响应状态: ${response.statusCode}');
    if (response.statusCode != 200) {
      debugPrint('API响应内容: ${response.body}');
    }
    
    return response;
  }
  
  /// 获取单本书籍信息
  Future<BookInfo?> getBookInfo(String barcode) async {
    try {
      final books = await checkBooksStatus([barcode]);
      return books.isNotEmpty ? books.first : null;
    } catch (e) {
      debugPrint('获取书籍信息失败: $e');
      return null;
    }
  }
  
  /// 检查是否有未借书籍
  Future<bool> hasUnborrowedBooks(List<String> barcodes) async {
    if (barcodes.isEmpty) return false;
    
    try {
      final books = await checkBooksStatus(barcodes);
      return books.any((book) => !book.isBorrowed);
    } catch (e) {
      debugPrint('检查未借书籍失败: $e');
      // 发生错误时默认认为有未借书籍，确保安全
      return true;
    }
  }
  
  /// 获取未借书籍列表
  Future<List<BookInfo>> getUnborrowedBooks(List<String> barcodes) async {
    if (barcodes.isEmpty) return [];
    
    try {
      final books = await checkBooksStatus(barcodes);
      return books.where((book) => !book.isBorrowed).toList();
    } catch (e) {
      debugPrint('获取未借书籍列表失败: $e');
      return [];
    }
  }
  
  /// 获取已借书籍列表
  Future<List<BookInfo>> getBorrowedBooks(List<String> barcodes) async {
    if (barcodes.isEmpty) return [];
    
    try {
      final books = await checkBooksStatus(barcodes);
      return books.where((book) => book.isBorrowed).toList();
    } catch (e) {
      debugPrint('获取已借书籍列表失败: $e');
      return [];
    }
  }
  
  /// 获取服务状态
  Map<String, dynamic> getStatus() {
    return {
      'base_url': _baseUrl,
      'timeout_seconds': _timeout.inSeconds,
      'service_name': 'BookApiService',
      'version': '1.0.0',
    };
  }
  
  /// 测试API连接
  Future<bool> testConnection() async {
    try {
      // TODO: 实现实际的连接测试
      // final response = await http.get(
      //   Uri.parse('$_baseUrl/health'),
      //   headers: {'Accept': 'application/json'},
      // ).timeout(Duration(seconds: 5));
      // 
      // return response.statusCode == 200;
      
      // 暂时返回true
      await Future.delayed(Duration(milliseconds: 100));
      return true;
    } catch (e) {
      debugPrint('API连接测试失败: $e');
      return false;
    }
  }
}
