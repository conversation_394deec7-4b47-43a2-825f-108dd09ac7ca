import '../../auth/models/auth_result.dart';

/// 闸机认证配置
/// 专门为安全闸机场景定制的认证配置
class GateAuthConfig {
  /// 默认启用的认证方式（与MultiAuthManager初始化的认证方式保持一致）
  static const List<AuthMethod> defaultEnabledMethods = [
    AuthMethod.face,              // 人脸识别
    AuthMethod.readerCard,        // 读者证
    AuthMethod.wechatScanQRCode,  // 微信扫码
  ];

  /// 认证超时时间（秒）
  static const int defaultTimeoutSeconds = 30;

  /// 认证重试次数
  static const int maxRetryAttempts = 3;

  /// 认证成功后的显示时间（秒）
  static const int successDisplaySeconds = 5;

  /// 认证失败后的显示时间（秒）
  static const int failureDisplaySeconds = 3;

  /// 自动恢复到欢迎界面的时间（秒）
  static const int autoRecoverSeconds = 8;

  /// 获取认证方式的显示名称
  static String getAuthMethodDisplayName(AuthMethod method) {
    switch (method) {
      case AuthMethod.face:
        return '人脸识别';
      case AuthMethod.readerCard:
        return '读者证';
      case AuthMethod.qrCode:
        return '二维码';
      case AuthMethod.idCard:
        return '身份证';
      case AuthMethod.socialSecurityCard:
        return '社保卡';
      case AuthMethod.citizenCard:
        return '市民卡';
      case AuthMethod.eletricSocialSecurityCard:
        return '电子社保卡';
      case AuthMethod.wechatQRCode:
        return '微信二维码';
      case AuthMethod.wechatScanQRCode:
        return '微信扫码';
      case AuthMethod.alipayQRCode:
        return '支付宝二维码';
      case AuthMethod.aliCreditQRCode:
        return '芝麻信用码';
      case AuthMethod.huiwenQRCode:
        return '汇文二维码';
      case AuthMethod.shangHaiQRCode:
        return '上海随申码';
      case AuthMethod.keyboardInput:
        return '手动输入';
      case AuthMethod.tencentTCard:
        return '腾讯E证通';
      case AuthMethod.imiAuth:
        return 'IMI身份认证';
      case AuthMethod.takePhoto:
        return '拍照认证';
      case AuthMethod.wechatOrAlipay:
        return '微信/支付宝';
      case AuthMethod.alipayQRCodeCredit:
        return '支付宝信用认证';
      case AuthMethod.jieYueBao:
        return '借阅宝';
    }
  }

  /// 获取认证状态的显示名称
  static String getAuthStatusDisplayName(AuthStatus status) {
    switch (status) {
      case AuthStatus.success:
        return '认证成功';
      case AuthStatus.failureNoMatch:
        return '未找到匹配信息';
      case AuthStatus.failureError:
        return '认证错误';
      case AuthStatus.failureTimeout:
        return '认证超时';
    }
  }

  /// 获取认证状态的颜色
  static String getAuthStatusColor(AuthStatus status) {
    switch (status) {
      case AuthStatus.success:
        return '#4CAF50'; // 绿色
      case AuthStatus.failureNoMatch:
        return '#FF9800'; // 橙色
      case AuthStatus.failureError:
        return '#F44336'; // 红色
      case AuthStatus.failureTimeout:
        return '#FF5722'; // 深橙色
    }
  }

  /// 检查认证方式是否适合闸机场景
  static bool isMethodSuitableForGate(AuthMethod method) {
    // 闸机场景适合的认证方式
    const suitableMethods = [
      AuthMethod.face,
      AuthMethod.readerCard,
      AuthMethod.qrCode,
      AuthMethod.idCard,
      AuthMethod.socialSecurityCard,
      AuthMethod.citizenCard,
      AuthMethod.wechatQRCode,
      AuthMethod.alipayQRCode,
      AuthMethod.huiwenQRCode,
    ];
    
    return suitableMethods.contains(method);
  }

  /// 获取闸机专用的认证方式列表
  static List<AuthMethod> getGateSuitableMethods() {
    return AuthMethod.values
        .where((method) => isMethodSuitableForGate(method))
        .toList();
  }

  /// 获取认证方式的图标名称
  static String getAuthMethodIcon(AuthMethod method) {
    switch (method) {
      case AuthMethod.face:
        return 'face_icon';
      case AuthMethod.readerCard:
        return 'card_icon';
      case AuthMethod.qrCode:
        return 'qr_code_icon';
      case AuthMethod.idCard:
        return 'id_card_icon';
      case AuthMethod.socialSecurityCard:
        return 'social_card_icon';
      case AuthMethod.citizenCard:
        return 'citizen_card_icon';
      default:
        return 'default_auth_icon';
    }
  }

  /// 获取认证失败的用户友好提示
  static String getFailureUserFriendlyMessage(AuthStatus status, String? originalMessage) {
    switch (status) {
      case AuthStatus.failureNoMatch:
        return '未找到您的信息，请检查证件是否正确或联系管理员';
      case AuthStatus.failureTimeout:
        return '认证超时，请重新尝试认证';
      case AuthStatus.failureError:
        return originalMessage?.isNotEmpty == true 
            ? originalMessage! 
            : '认证系统暂时不可用，请稍后重试或联系管理员';
      case AuthStatus.success:
        return '认证成功';
    }
  }

  /// 验证认证配置是否有效
  static bool validateConfig({
    required List<AuthMethod> enabledMethods,
    required int timeoutSeconds,
    required int maxRetryAttempts,
  }) {
    // 检查是否至少启用了一种认证方式
    if (enabledMethods.isEmpty) {
      return false;
    }

    // 检查超时时间是否合理
    if (timeoutSeconds < 10 || timeoutSeconds > 300) {
      return false;
    }

    // 检查重试次数是否合理
    if (maxRetryAttempts < 1 || maxRetryAttempts > 10) {
      return false;
    }

    // 检查启用的认证方式是否都适合闸机场景
    for (final method in enabledMethods) {
      if (!isMethodSuitableForGate(method)) {
        return false;
      }
    }

    return true;
  }

  /// 创建默认的闸机认证配置
  static Map<String, dynamic> createDefaultConfig() {
    return {
      'enabled_methods': defaultEnabledMethods.map((e) => e.toString()).toList(),
      'timeout_seconds': defaultTimeoutSeconds,
      'max_retry_attempts': maxRetryAttempts,
      'success_display_seconds': successDisplaySeconds,
      'failure_display_seconds': failureDisplaySeconds,
      'auto_recover_seconds': autoRecoverSeconds,
      'ui_mode': 'minimal', // 极简UI模式
      'enable_sound': true,  // 启用声音提示
      'enable_vibration': false, // 禁用震动（闸机通常没有震动功能）
    };
  }
}
