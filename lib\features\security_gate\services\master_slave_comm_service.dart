import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:uuid/uuid.dart';
import 'shared_scan_pool_service.dart';
import 'rfid_service.dart' show RFIDService;

/// 主从机通信服务
/// 处理主从机间的数据同步
class MasterSlaveCommService {
  static final _instance = MasterSlaveCommService._internal();
  static MasterSlaveCommService get instance => _instance;
  MasterSlaveCommService._internal();

  // 配置信息
  bool _isMaster = false;
  String? _masterAddress;
  int _port = 8888;

  // 网络组件
  ServerSocket? _server;
  Socket? _clientSocket;

  // 🔥 新增：多从机连接管理
  final List<Socket> _connectedSlaves = [];
  final Map<Socket, String> _slaveInfo = {}; // 存储从机信息
  
  // 🔥 新增：清空请求管理
  final Map<String, Completer<Map<String, dynamic>>> _pendingRequests = {};
  final Uuid _uuid = const Uuid();
  
  // 🔥 新增：并发控制
  bool _isClearingPool = false;
  
  // 连接状态
  bool _isConnected = false;
  
  // 数据同步定时器
  Timer? _syncTimer;
  
  // 事件流
  final StreamController<String> _messageController = 
      StreamController<String>.broadcast();
  final StreamController<String> _errorController = 
      StreamController<String>.broadcast();
  final StreamController<bool> _connectionController = 
      StreamController<bool>.broadcast();

  /// 获取消息流
  Stream<String> get messageStream => _messageController.stream;
  
  /// 获取错误流
  Stream<String> get errorStream => _errorController.stream;
  
  /// 获取连接状态流
  Stream<bool> get connectionStream => _connectionController.stream;
  
  /// 是否为主机
  bool get isMaster => _isMaster;
  
  /// 是否已连接
  bool get isConnected => _isConnected;

  /// 配置为主机模式
  Future<void> configureAsMaster({
    int port = 8888,
  }) async {
    try {
      debugPrint('配置为主机模式，监听端口: $port');

      _isMaster = true;
      _port = port;

      // 启动服务器
      await _startServer();

      // 🔥 移除定时同步：改为请求-响应模式
      debugPrint('主机模式配置完成（请求-响应模式）');
    } catch (e) {
      final errorMsg = '配置主机模式失败: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
      rethrow;
    }
  }

  /// 配置为从机模式
  Future<void> configureAsSlave({
    required String masterAddress,
    int port = 8888,
  }) async {
    try {
      debugPrint('配置为从机模式: $masterAddress:$port');
      
      _isMaster = false;
      _masterAddress = masterAddress;
      _port = port;
      
      // 连接到主机
      await _connectToMaster();
      
      debugPrint('从机模式配置完成');
    } catch (e) {
      final errorMsg = '配置从机模式失败: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
      rethrow;
    }
  }

  /// 启动服务器（主机模式）
  Future<void> _startServer() async {
    try {
      _server = await ServerSocket.bind(InternetAddress.anyIPv4, _port);
      debugPrint('主机服务器启动成功，监听端口: $_port');
      
      _server!.listen((Socket client) {
        final clientInfo = '${client.remoteAddress}:${client.remotePort}';
        debugPrint('新从机连接: $clientInfo');

        // 🔥 新增：添加到从机列表
        _connectedSlaves.add(client);
        _slaveInfo[client] = clientInfo;

        // 更新连接状态
        _isConnected = _connectedSlaves.isNotEmpty;
        _connectionController.add(_isConnected);

        debugPrint('当前连接的从机数量: ${_connectedSlaves.length}');

        // 监听从机消息
        client.listen(
          (data) => _handleSlaveMessage(client, utf8.decode(data)),
          onDone: () {
            debugPrint('从机断开连接: $clientInfo');
            _removeSlaveConnection(client);
          },
          onError: (error) {
            debugPrint('从机连接错误: $clientInfo - $error');
            _errorController.add('从机连接错误: $clientInfo - $error');
            _removeSlaveConnection(client);
          },
        );

        // 🔥 新增：向新连接的从机发送当前状态
        _sendCurrentStateToSlave(client);
      });
    } catch (e) {
      throw Exception('启动服务器失败: $e');
    }
  }

  /// 连接到主机（从机模式）
  Future<void> _connectToMaster() async {
    try {
      _clientSocket = await Socket.connect(_masterAddress!, _port);
      _isConnected = true;
      _connectionController.add(true);
      
      debugPrint('成功连接到主机: $_masterAddress:$_port');
      
      // 监听主机消息
      _clientSocket!.listen(
        (data) => _handleServerMessage(utf8.decode(data)),
        onDone: () {
          debugPrint('与主机断开连接');
          _isConnected = false;
          _connectionController.add(false);
          _clientSocket = null;
        },
        onError: (error) {
          debugPrint('主机连接错误: $error');
          _errorController.add('主机连接错误: $error');
        },
      );
    } catch (e) {
      throw Exception('连接主机失败: $e');
    }
  }

  /// 🔥 优化：从机请求当前数据（增强错误处理和重试机制）
  Future<List<String>> requestCurrentData({
    String? channelId,
    Duration timeout = const Duration(seconds: 5),
    int maxRetries = 1,
  }) async {
    if (_isMaster) {
      throw Exception('主机不能请求数据');
    }

    if (!_isConnected || _clientSocket == null) {
      throw Exception('未连接到主机');
    }

    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        final requestId = _uuid.v4();
        final request = {
          'type': 'get_current_data',
          'request_id': requestId,
          'channel_id': channelId ?? 'unknown',
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        };

        debugPrint('📤 [$channelId] 请求主机当前数据 (尝试$attempt/$maxRetries)...');

        // 发送请求
        final requestJson = jsonEncode(request);
        _clientSocket!.write('$requestJson\n');

        // 等待响应
        final completer = Completer<Map<String, dynamic>>();
        _pendingRequests[requestId] = completer;

        final response = await completer.future.timeout(timeout);

        if (response['success'] == true) {
          final data = List<String>.from(response['data'] ?? []);
          final count = response['count'] ?? 0;
          debugPrint('✅ [$channelId] 获取主机数据成功: $count个条码');
          if (data.isNotEmpty) {
            debugPrint('📋 [$channelId] 数据详情: $data');
          }
          return data;
        } else {
          final errorMsg = response['error'] ?? '未知错误';
          debugPrint('❌ [$channelId] 获取数据失败 (尝试$attempt/$maxRetries): $errorMsg');
          if (attempt == maxRetries) {
            throw Exception('获取数据失败: $errorMsg');
          }
        }
      } catch (e) {
        debugPrint('❌ [$channelId] 请求主机数据异常 (尝试$attempt/$maxRetries): $e');
        if (attempt == maxRetries) {
          rethrow;
        }
        // 重试前等待一小段时间
        await Future.delayed(const Duration(milliseconds: 200));
      }
    }

    return [];
  }

  /// 🔥 增强：请求主机清空共享池（带响应确认）
  Future<bool> requestMasterClearPool({
    String? channelId,
    Duration timeout = const Duration(seconds: 3),
    int maxRetries = 2,
  }) async {
    if (_isMaster || !_isConnected || _clientSocket == null) {
      debugPrint('⚠️ 无法发送清空请求：${_isMaster ? "是主机" : "未连接"} ');
      return false;
    }
    
    // 带重试的请求机制
    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        final requestId = _uuid.v4();
        final message = {
          'type': 'clear_pool_request',
          'request_id': requestId,
          'channel_id': channelId ?? 'unknown',
          'timestamp': DateTime.now().millisecondsSinceEpoch,
          'reason': 'exit_start',
          'attempt': attempt,
        };
        
        // 创建响应等待器
        final completer = Completer<Map<String, dynamic>>();
        _pendingRequests[requestId] = completer;
        
        // 发送请求
        final jsonStr = jsonEncode(message);
        _clientSocket!.write('$jsonStr\n');
        
        debugPrint('📤 [$channelId] 发送清空请求 (尝试$attempt/$maxRetries): $requestId');
        
        try {
          // 等待响应
          final response = await completer.future.timeout(timeout);
          
          if (response['success'] == true) {
            final clearedCount = response['cleared_count'] ?? 0;
            debugPrint('✅ [$channelId] 清空请求成功: 清除${clearedCount}个条码');
            return true;
          } else {
            final errorMsg = response['message'] ?? '未知错误';
            debugPrint('❌ [$channelId] 清空请求被拒绝: $errorMsg');
          }
        } on TimeoutException {
          debugPrint('⏰ [$channelId] 清空请求超时 (尝试$attempt/$maxRetries)');
        } finally {
          _pendingRequests.remove(requestId);
        }
        
      } catch (e) {
        debugPrint('💥 [$channelId] 清空请求失败 (尝试$attempt/$maxRetries): $e');
      }
      
      // 重试前等待
      if (attempt < maxRetries) {
        await Future.delayed(Duration(milliseconds: 500 * attempt));
      }
    }
    
    debugPrint('🚫 [$channelId] 清空请求最终失败，已重试$maxRetries次');
    _errorController.add('清空请求失败：网络超时或主机无响应');
    return false;
  }

  /// 🔥 新增：移除从机连接
  void _removeSlaveConnection(Socket client) {
    final clientInfo = _slaveInfo[client];
    _connectedSlaves.remove(client);
    _slaveInfo.remove(client);

    // 更新连接状态
    _isConnected = _connectedSlaves.isNotEmpty;
    _connectionController.add(_isConnected);

    debugPrint('从机已移除: $clientInfo，当前连接数: ${_connectedSlaves.length}');

    try {
      client.close();
    } catch (e) {
      debugPrint('关闭从机连接失败: $e');
    }
  }

  /// 🔥 新增：向指定从机发送当前状态
  void _sendCurrentStateToSlave(Socket client) {
    try {
      final sharedPool = SharedScanPoolService.instance;
      final currentBarcodes = sharedPool.getCurrentPool().toList();

      if (currentBarcodes.isNotEmpty) {
        final message = {
          'type': 'sync_barcodes',
          'data': {'barcodes': currentBarcodes},
          'timestamp': DateTime.now().toIso8601String(),
        };

        client.write('${jsonEncode(message)}\n');
        debugPrint('向新从机同步当前状态: ${currentBarcodes.length}个条码');
      }
    } catch (e) {
      debugPrint('向从机发送当前状态失败: $e');
    }
  }

  /// 🔥 新增：处理清空池请求（主机端）
  Future<void> _handleClearPoolRequest(Socket client, Map<String, dynamic> request) async {
    final requestId = request['request_id'] as String?;
    final channelId = request['channel_id'] as String? ?? 'unknown';
    final clientInfo = _slaveInfo[client] ?? 'unknown';

    debugPrint('📥 收到从机[$channelId]清空请求: $requestId from $clientInfo');
    debugPrint('🔍 主机开始处理从机清空请求...');

    Map<String, dynamic> response = {
      'type': 'clear_pool_response',
      'request_id': requestId,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };

    try {
      // 🔥 并发控制：防止同时清空
      if (_isClearingPool) {
        response.addAll({
          'success': false,
          'message': '正在执行清空操作，请稍后重试',
          'error_code': 'CLEARING_IN_PROGRESS',
        });
        debugPrint('⚠️ [$channelId] 清空请求被拒绝：正在执行中');
      } else {
        _isClearingPool = true;

        try {
          final sharedPool = SharedScanPoolService.instance;
          final clearedCount = sharedPool.poolSize;

          debugPrint('📊 [$channelId] 清空前状态: 共享池大小=$clearedCount');

          // 🔥 关键：执行完整的清空操作（包含缓冲区清空和RFID重置）
          debugPrint('🧹 [$channelId] 开始执行主机清空操作...');
          debugPrint('🔧 [$channelId] 调用clearPoolAndBuffer方法...');
          await sharedPool.clearPoolAndBuffer();
          debugPrint('🔧 [$channelId] clearPoolAndBuffer方法执行完成');

          debugPrint('✅ [$channelId] 主机清空操作完成: 清除$clearedCount个条码');
          debugPrint('📊 [$channelId] 清空后状态: 共享池大小=${sharedPool.poolSize}');

          // 🔁 强制对齐主机本地出馆开始：同时清空事件层去重（已扫描+已处理），并确保轮询运行
          try {
            final rfid = RFIDService.instance;
            final wasScanning = rfid.isScanning;
            debugPrint('📡 [$channelId] 清空后RFID扫描状态: scanning=$wasScanning');

            // 先清空事件层的两套去重集合，避免“条码已扫描过，跳过”
            debugPrint('🧹 [$channelId] 清空扫描结果（事件层去重：已扫描+已处理）...');
            rfid.clearScanResult();
            debugPrint('✅ [$channelId] 扫描结果已清空，计数归零');

            // 再触发数据收集：若正在扫描则仅重置processed；若未扫描则启动轮询
            await rfid.startDataCollection();
            debugPrint('✅ [$channelId] 已触发RFID数据收集以重置去重集合并恢复轮询');
          } catch (e) {
            debugPrint('⚠️ [$channelId] 触发RFID重置失败: $e');
          }

          response.addAll({
            'success': true,
            'cleared_count': clearedCount,
            'message': '共享池已清空，清除$clearedCount个条码',
          });

          // 🔥 通知所有其他从机池已被清空
          _notifyPoolClearedToOtherSlaves(client, channelId, clearedCount);

        } finally {
          _isClearingPool = false;
        }
      }
    } catch (e) {
      _isClearingPool = false;
      response.addAll({
        'success': false,
        'message': '清空操作失败: $e',
        'error_code': 'CLEAR_FAILED',
      });
      debugPrint('💥 [$channelId] 清空操作失败: $e');
    }
    
    // 发送响应
    try {
      final jsonStr = '${jsonEncode(response)}\n';
      client.write(jsonStr);
      debugPrint('📤 向从机[$channelId]发送清空响应: ${response['success']}');
    } catch (e) {
      debugPrint('💥 发送清空响应失败: $e');
      _removeSlaveConnection(client);
    }
  }

  /// 🔥 新增：通知其他从机池已被清空
  void _notifyPoolClearedToOtherSlaves(Socket requester, String requesterChannelId, int clearedCount) {
    final notification = {
      'type': 'pool_cleared_notification',
      'cleared_by': requesterChannelId,
      'cleared_count': clearedCount,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      'message': '共享池已被从机[$requesterChannelId]清空',
    };
    
    final jsonStr = '${jsonEncode(notification)}\n';
    int notifiedCount = 0;
    
    for (final slave in _connectedSlaves) {
      if (slave != requester) { // 不通知请求者自己
        try {
          slave.write(jsonStr);
          notifiedCount++;
        } catch (e) {
          final clientInfo = _slaveInfo[slave] ?? 'unknown';
          debugPrint('通知从机失败: $clientInfo - $e');
          _removeSlaveConnection(slave);
        }
      }
    }
    
    if (notifiedCount > 0) {
      debugPrint('📢 已通知${notifiedCount}个其他从机：池被[$requesterChannelId]清空');
    }
  }

  /// 🔥 新增：处理清空池响应（从机端）
  void _handleClearPoolResponse(Map<String, dynamic> response) {
    final requestId = response['request_id'] as String?;
    
    if (requestId != null && _pendingRequests.containsKey(requestId)) {
      final completer = _pendingRequests[requestId]!;
      if (!completer.isCompleted) {
        completer.complete(response);
        debugPrint('📨 收到清空响应: $requestId - ${response['success']}');
      }
    } else {
      debugPrint('⚠️ 收到未知的清空响应: $requestId');
    }
  }

  /// 🔥 新增：广播消息到所有从机
  void _broadcastToAllSlaves(Map<String, dynamic> message) {
    if (_connectedSlaves.isEmpty) return;

    final jsonStr = '${jsonEncode(message)}\n';
    final failedSlaves = <Socket>[];

    for (final slave in _connectedSlaves) {
      try {
        slave.write(jsonStr);
      } catch (e) {
        final clientInfo = _slaveInfo[slave] ?? 'unknown';
        debugPrint('向从机发送消息失败: $clientInfo - $e');
        failedSlaves.add(slave);
      }
    }

    // 移除发送失败的从机连接
    for (final failedSlave in failedSlaves) {
      _removeSlaveConnection(failedSlave);
    }
  }

  /// 🔥 新增：处理从机消息（支持多从机）
  void _handleSlaveMessage(Socket client, String message) {
    final clientInfo = _slaveInfo[client] ?? 'unknown';
    debugPrint('收到从机消息: $clientInfo - $message');

    // 转发给原有的处理方法
    _handleClientMessage(message, client);
  }

  /// 处理客户端消息（主机模式）
  Future<void> _handleClientMessage(String message, Socket client) async {
    try {
      final lines = message.trim().split('\n');
      for (String line in lines) {
        if (line.isEmpty) continue;
        
        final data = jsonDecode(line);
        final type = data['type'] as String?;
        
        switch (type) {
          case 'clear_pool_request':
            await _handleClearPoolRequest(client, data);
            break;
          case 'get_current_data':
            // 🔥 新增：处理数据请求
            await _handleDataRequest(client, data);
            break;
          default:
            debugPrint('未知的从机消息类型: $type');
        }
      }
    } catch (e) {
      debugPrint('处理从机消息失败: $e');
      _errorController.add('处理从机消息失败: $e');
    }
  }

  /// 处理服务器消息（从机模式）
  void _handleServerMessage(String message) {
    try {
      final lines = message.trim().split('\n');
      for (String line in lines) {
        if (line.isEmpty) continue;
        
        final data = jsonDecode(line);
        final type = data['type'] as String?;
        
        switch (type) {
          case 'sync_pool':
            final List<dynamic> poolData = data['data'] ?? [];
            final Set<String> newPool = poolData.cast<String>().toSet();
            SharedScanPoolService.instance.syncPool(newPool);
            break;
          case 'clear_pool_response':
            _handleClearPoolResponse(data);
            break;
          case 'current_data_response':
            // 🔥 新增：处理数据响应
            _handleDataResponse(data);
            break;
          default:
            debugPrint('未知的主机消息类型: $type');
        }
      }
    } catch (e) {
      debugPrint('处理主机消息失败: $e');
      _errorController.add('处理主机消息失败: $e');
    }
  }

  /// 发送自定义消息
  Future<void> sendMessage(Map<String, dynamic> message) async {
    if (!_isConnected || _clientSocket == null) {
      throw Exception('未连接到对端');
    }
    
    try {
      final jsonStr = jsonEncode(message);
      _clientSocket!.write('$jsonStr\n');
      debugPrint('发送消息: ${message['type']}');
    } catch (e) {
      debugPrint('发送消息失败: $e');
      _errorController.add('发送消息失败: $e');
      rethrow;
    }
  }

  /// 重新连接
  Future<void> reconnect() async {
    try {
      await disconnect();
      
      if (_isMaster) {
        await _startServer();
        // 🔥 移除定时同步：改为请求-响应模式
      } else {
        await _connectToMaster();
      }
      
      debugPrint('重新连接成功');
    } catch (e) {
      final errorMsg = '重新连接失败: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
      rethrow;
    }
  }

  /// 断开连接
  Future<void> disconnect() async {
    try {
      _isConnected = false;
      _connectionController.add(false);
      
      _syncTimer?.cancel();
      _syncTimer = null;
      
      await _clientSocket?.close();
      _clientSocket = null;
      
      await _server?.close();
      _server = null;
      
      debugPrint('连接已断开');
    } catch (e) {
      debugPrint('断开连接失败: $e');
    }
  }

  /// 获取连接状态信息
  Map<String, dynamic> getConnectionInfo() {
    return {
      'is_master': _isMaster,
      'is_connected': _isConnected,
      'connected_slaves': _connectedSlaves.length,
      'slave_list': _slaveInfo.values.toList(),
      'master_address': _masterAddress,
      'port': _port,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// 🔥 新增：处理数据响应（从机端）
  void _handleDataResponse(Map<String, dynamic> response) {
    try {
      final requestId = response['request_id'] as String?;
      if (requestId != null && _pendingRequests.containsKey(requestId)) {
        final completer = _pendingRequests.remove(requestId)!;
        completer.complete(response);
        debugPrint('📨 收到数据响应: $requestId - ${response['success']}');
      } else {
        debugPrint('⚠️ 收到未知的数据响应: $requestId');
      }
    } catch (e) {
      debugPrint('❌ 处理数据响应失败: $e');
    }
  }

  /// 🔥 优化：处理从机的数据请求（增强数据获取和日志）
  Future<void> _handleDataRequest(Socket client, Map<String, dynamic> request) async {
    try {
      final requestId = request['request_id'] as String?;
      final channelId = request['channel_id'] as String? ?? 'unknown';

      debugPrint('📥 [$channelId] 收到数据请求: $requestId');

      // 🔥 优化：确保获取最新的共享池数据
      final sharedPool = SharedScanPoolService.instance;
      final currentData = sharedPool.getCurrentPool().toList();

      // 🔥 新增：详细的数据状态日志
      debugPrint('📊 [$channelId] 共享池状态: 大小=${sharedPool.poolSize}, 数据=$currentData');

      final response = {
        'type': 'current_data_response',
        'request_id': requestId,
        'success': true,
        'data': currentData,
        'count': currentData.length,
        'pool_size': sharedPool.poolSize,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };

      // 发送响应
      final responseJson = '${jsonEncode(response)}\n';
      client.write(responseJson);

      if (currentData.isNotEmpty) {
        debugPrint('✅ [$channelId] 数据请求处理完成: 返回${currentData.length}个条码 - $currentData');
      } else {
        debugPrint('✅ [$channelId] 数据请求处理完成: 返回${currentData.length}个条码（共享池为空）');
      }

    } catch (e) {
      debugPrint('❌ 处理数据请求失败: $e');

      // 发送错误响应
      final errorResponse = {
        'type': 'current_data_response',
        'request_id': request['request_id'],
        'success': false,
        'error': e.toString(),
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };

      try {
        final errorJson = '${jsonEncode(errorResponse)}\n';
        client.write(errorJson);
      } catch (writeError) {
        debugPrint('发送错误响应失败: $writeError');
      }
    }
  }

  /// 清理资源
  void dispose() {
    disconnect();
    _messageController.close();
    _errorController.close();
    _connectionController.close();
  }
}
