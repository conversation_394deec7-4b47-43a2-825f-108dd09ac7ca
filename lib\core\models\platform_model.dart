// ignore_for_file: non_constant_identifier_names

import 'dart:convert';

import 'package:base_package/base_package.dart';
import 'package:sea_socket/sea_socket.dart';

// String State_CurShelf                        = "1")                        //在架
// String State_NotInShelf                = "2")                        //不在架
// String State_Operated                        = "3")                        //已操作 下架
// String State_WrongShelf                = "4")                        //剔旧 错架
// String State_ChangeCollection                        = "5")                        //换馆藏
// String State_NewBook                        = "6")                        //新增书
// String State_CheckBorrow                        = "7")                        //查询外借状态
// String State_CheckSettle_Not                        = "8")                        //盘点操作未扫描到本架图书
// String State_CheckSettle_On                        = "9")                        //盘点操作扫描到本架图书
//
// String State_Upload_Not                = "0")                        //图书未操作，不用更新
// String State_Upload_Shelf                = "1")
// String State_Upload_All                = "2")
//
// String  Statistics_collection                = "collection")        //采集
// String  Statistics_takedown                = "takedown")                //下架
// String  Statistics_tidy                        = "tidy")                        //剔旧
// String  Statistics_upsidedown                = "upsidedown")        //倒架
// String  Statistics_upload                        = "upload")                //上架
// String  Statistics_download                = "download")                //下架
// String  Statistics_shelftdownload                = "shelftdownload")                //层架信息下架
// String  Statistics_returnShelf                = "returnShelf")                //归架

// class BookOperateUtil {
//   static String State_CurShelf = "1"; //在架
//   static String State_NotInShelf = "2"; //不在架
//   static String State_takedown = "3"; //已操作 下架
//   static String State_WrongShelf = "4"; //剔旧 错架
//   static String State_ChangeCollection = "5"; //换馆藏
//   static String State_NewBook = "6"; //新增书
//   static String State_CheckBorrow = "7"; //查询外借状态
//   static String State_CheckSettle_Not = "8"; //盘点操作未扫描到本架图书
//   static String State_CheckSettle_On = "9"; //盘点操作扫描到本架图书
//
//   static String State_Upload_Not = "0"; //图书未操作，不用更新
//   static String State_Upload_Shelf = "1";
//   static String State_Upload_All = "2";
// }

class TokenData {
  String access_token;
  int expires_in;
  String token_type;
  String scope;
  int? date;

  bool get isAvailable {
    if (date == null) return false;

    int now = DateTime.now().millisecondsSinceEpoch ~/ 1000;

    return (date! + expires_in) > now;
  }

  TokenData({
    required this.access_token,
    required this.expires_in,
    required this.token_type,
    required this.scope,
    this.date,
  });

  Map<String, dynamic> toJson() {
    return {
      "access_token": access_token,
      "expires_in": expires_in,
      "token_type": token_type,
      "scope": scope,
      "date": date,
    };
  }

  factory TokenData.fromJson(Map<String, dynamic> json) {
    return TokenData(
      access_token: json["access_token"] ?? "",
      expires_in: json["expires_in"] ?? "",
      token_type: json["token_type"] ?? "",
      scope: json["scope"] ?? "",
      date: json["date"],
    );
  }
//
}

class DepartmentData {
  String servicePointCode;
  String name;
  int id;
  int servicePointType;
  int collectionDepartmentId;
  bool isSelected = false;

  DepartmentData(
      {required this.servicePointCode,
      required this.name,
      required this.id,
      required this.servicePointType,
      required this.collectionDepartmentId});

  Map<String, dynamic> toJson() {
    return {
      "servicePointCode": servicePointCode,
      "name": name,
      "id": id,
      "servicePointType": servicePointType,
      "collectionDepartmentId": collectionDepartmentId,
    };
  }

  factory DepartmentData.fromJson(Map<String, dynamic> json) {
    return DepartmentData(
      servicePointCode: json["servicePointCode"] ?? "",
      name: json["name"] ?? "",
      id: json["id"] ?? 0,
      servicePointType: json["servicePointType"] ?? 0,
      collectionDepartmentId: json["collectionDepartmentId"] ?? 0,
    );
  }
//
}

class BookShelfModel {
  List<BookShelfData> list;
  int totalCount;
  int pageSize;
  int currentPage;
  int pageCount;

  BookShelfModel({
    required this.list,
    required this.totalCount,
    required this.pageSize,
    required this.currentPage,
    required this.pageCount,
  });

  Map<String, dynamic> toJson() {
    return {
      "list": jsonEncode(list.map((e) => e.toJson()).toList()),
      "totalCount": totalCount,
      "pageSize": pageSize,
      "currentPage": currentPage,
      "pageCount": pageCount,
    };
  }

  factory BookShelfModel.fromJson(Map<String, dynamic> json) {
    List<BookShelfData> list = [];
    (json["list"] ?? []).forEach((e) {
      list.add(BookShelfData.fromJson(e));
    });

    return BookShelfModel(
      list: list,
      totalCount: json["totalCount"] ?? 0,
      pageSize: json["pageSize"] ?? 0,
      currentPage: json["currentPage"] ?? 0,
      pageCount: json["pageCount"] ?? 0,
    );
  }
//
}

class BookShelfData {
  String libraryCode;
  String location;
  String layerBarcode;
  String name;
  String displayName;
  int mappingType;
  String mapLayerBarcode;
  String departmentCode;

  BookShelfData({
    required this.libraryCode,
    required this.location,
    required this.layerBarcode,
    required this.name,
    required this.displayName,
    required this.mappingType,
    required this.mapLayerBarcode,
    required this.departmentCode,
  });

  Map<String, dynamic> toJson() {
    return {
      "libraryCode": libraryCode,
      "location": location,
      "layerBarcode": layerBarcode,
      "name": name,
      "displayName": displayName,
      "mappingType": mappingType,
      "mapLayerBarcode": mapLayerBarcode,
      "departmentCode": departmentCode,
    };
  }

  factory BookShelfData.fromJson(Map<String, dynamic> json) {
    return BookShelfData(
      libraryCode: json["libraryCode"] ?? "",
      location: json["location"] ?? "",
      layerBarcode: json["layerBarcode"] ?? "",
      name: json["name"] ?? "",
      displayName: json["displayName"] ?? "",
      mappingType: json["mappingType"] ?? -1,
      mapLayerBarcode: json["mapLayerBarcode"] ?? "",
      departmentCode: json['departmentCode'] ?? "",
    );
  }

  String get showName {
    if (displayName.isNotEmpty) {
      return displayName;
    } else if (name.isNotEmpty) {
      return name;
    } else if (mapLayerBarcode.isNotEmpty) {
      return mapLayerBarcode;
    } else if (layerBarcode.isNotEmpty) {
      return layerBarcode;
    }
    return '';
  }
}

class BookServerModel {
  List<BookServerData> list;
  List<BookDBData>? _dbList;
  int totalCount;
  int pageSize;
  int currentPage;
  int pageCount;

  BookServerModel({
    required this.list,
    required this.totalCount,
    required this.pageSize,
    required this.currentPage,
    required this.pageCount,
  });

  List<BookDBData> get dbList {
    if (_dbList != null) {
      return _dbList!;
    }

    _dbList = [];
    list.forEach((element) {
      _dbList?.add(BookDBData(
        barcode: element.bookSn,
        uid: '',
        title: element.bookTitle,
        callno: element.bookCallNumber,
        isbn: element.bookIsbn,
        author: element.bookAuthor,
        media_type: '',
        price: '',
        circulation_type: '',
        permanent_location: '',
        current_location: '',
        publisher: element.bookPublisher,
        pages: element.departmentCode,
        subject: '',
        extra: '',
        datetime: element.updated,
        shelfno: element.currentLayerBarcode,
        status: '',
        upload: '0', circulationStatus: '',
      ));
    });
    _dbList?.sort((a, b) => a.datetime.compareTo(b.datetime));
    return _dbList!;
  }

  Map<String, dynamic> toJson() {
    return {
      "list": jsonEncode(list.map((e) => e.toJson()).toList()),
      "totalCount": totalCount,
      "pageSize": pageSize,
      "currentPage": currentPage,
      "pageCount": pageCount,
    };
  }

  factory BookServerModel.fromJson(Map<String, dynamic> json) {
    List<BookServerData> list = [];
    (json["list"] ?? json["List"] ?? []).forEach((e) {
      list.add(BookServerData.fromJson(e));
    });

    return BookServerModel(
      list: list,
      totalCount: json["totalCount"] ?? json["TotalCount"] ?? 0,
      pageSize: json["pageSize"] ?? json["PageSize"] ?? 0,
      currentPage: json["currentPage"] ?? json["CurrentPage"] ?? 0,
      pageCount: json["pageCount"] ?? json["PageCount"] ?? 0,
    );
  }
//
}

class BookServerData {
  int id;
  String bookSn;
  String bookTitle;
  String bookAuthor;
  String bookIsbn;
  String bookPublisher;
  String bookCallNumber;
  String fixedLayerBarcode;
  String currentLayerBarcode;
  int bookOnShelfStatus;
  String updated;
  String departmentCode;

  BookServerData({
    required this.id,
    required this.bookSn,
    required this.bookTitle,
    required this.bookAuthor,
    required this.bookIsbn,
    required this.bookPublisher,
    required this.bookCallNumber,
    required this.fixedLayerBarcode,
    required this.currentLayerBarcode,
    required this.bookOnShelfStatus,
    required this.updated,
    required this.departmentCode,
  });

  Map<String, dynamic> toJson() {
    return {
      "id": id,
      "bookSn": bookSn,
      "bookTitle": bookTitle,
      "bookAuthor": bookAuthor,
      "bookIsbn": bookIsbn,
      "bookPublisher": bookPublisher,
      "bookCallNumber": bookCallNumber,
      "fixedLayerBarcode": fixedLayerBarcode,
      "currentLayerBarcode": currentLayerBarcode,
      "bookOnShelfStatus": bookOnShelfStatus,
      "updated": updated,
      "departmentCode": departmentCode,
    };
  }

  factory BookServerData.fromJson(Map<String, dynamic> json) {
    return BookServerData(
      id: json["id"] ??json["Id"] ?? -1,
      bookSn: json["bookSn"] ?? json["BookSn"] ?? "",
      bookTitle: json["bookTitle"] ?? json["BookTitle"] ?? "",
      bookAuthor: json["bookAuthor"] ?? json["BookAuthor"] ?? "",
      bookIsbn: json["bookIsbn"] ?? json["BookIsbn"] ?? "",
      bookPublisher: json["bookPublisher"] ?? json["BookPublisher"] ?? "",
      bookCallNumber: json["bookCallNumber"] ?? json["BookCallNumber"] ?? "",
      fixedLayerBarcode: json["fixedLayerBarcode"] ?? json["FixedLayerBarcode"] ?? "",
      currentLayerBarcode: json["currentLayerBarcode"] ?? json["CurrentLayerBarcode"] ?? "",
      bookOnShelfStatus: json["bookOnShelfStatus"] ?? json["BookOnShelfStatus"] ?? -1,
      updated: json["updated"] ?? json["Updated"] ?? "",
      departmentCode: json['departmentCode'] ?? json['DepartmentCode'] ?? "",
    );
  }
}


const String BookOrigin = '0';
const String BookAdd = '1';
const String BookExists = '2';
const String BookRemove = '3';



class DepartmentDBData {
  String department;
  String total;
  String extra;
  String datetime;

  DepartmentDBData(
      {required this.department,
      required this.total,
      required this.extra,
      required this.datetime});

  Map<String, dynamic> toJson() {
    return {
      "department": department,
      "total": total,
      "extra": extra,
      "datetime": datetime,
    };
  }

  factory DepartmentDBData.fromJson(Map<String, dynamic> json) {
    return DepartmentDBData(
      department: json["department"],
      total: json["total"],
      extra: json["extra"],
      datetime: json["datetime"],
    );
  }

}

enum StatictisDBType {
  collection, // 采集
  takedown, // 下架
  tidy, // 剔旧
  upsidedown, // 倒架
  upload, // 上传
  download, // 下载
  shelftdownload, // 层架信息下架
  returnShelf, // 归架
}

class StatictisDBData {
  String type_name;
  String cur_number;
  String quantity;
  String statistics_date;
  String operate_Datetime;
  String extra;

  StatictisDBData({
    required this.type_name,
    required this.cur_number,
    required this.quantity,
    required this.statistics_date,
    required this.operate_Datetime,
    required this.extra,
  });

  Map<String, dynamic> toJson() {
    return {
      "type_name": type_name,
      "cur_number": cur_number,
      "quantity": quantity,
      "statistics_date": statistics_date,
      "operate_Datetime": operate_Datetime,
      "extra": extra,
    };
  }

  factory StatictisDBData.fromJson(Map<String, dynamic> json) {
    return StatictisDBData(
      type_name: json["type_name"],
      cur_number: json["cur_number"],
      quantity: json["quantity"],
      statistics_date: json["statistics_date"],
      operate_Datetime: json["operate_Datetime"],
      extra: json["extra"],
    );
  }

  static String getName(StatictisDBType type) {
    // collection, // 采集
    // takedown,   // 下架
    // tidy,       // 剔旧
    // upsidedown, // 倒架
    // upload,     // 上架
    // download,   // 下载
    // shelftdownload, // 层架信息下架
    // returnShelf,    // 归架
    Map<StatictisDBType, String> map = {
      StatictisDBType.collection: 'collection',
      StatictisDBType.takedown: 'takedown',
      StatictisDBType.tidy: 'tidy',
      StatictisDBType.upsidedown: 'upsidedown',
      StatictisDBType.upload: 'upload',
      StatictisDBType.download: 'download',
      StatictisDBType.shelftdownload: 'shelftdownload',
      StatictisDBType.returnShelf: 'returnShelf',
    };

    return map[type] ?? "";
  }
}

class CollectionDBData {
  String collectionCode;
  String collectionName;
  String departmentCode;
  String departmentName;
  String extra;

  CollectionDBData({
    required this.collectionCode,
    required this.collectionName,
    required this.departmentCode,
    required this.departmentName,
    required this.extra,
  });

  Map<String, dynamic> toJson() {
    return {
      "collectionCode": collectionCode,
      "collectionName": collectionName,
      "departmentCode": departmentCode,
      "departmentName": departmentName,
      "extra": extra,
    };
  }

  factory CollectionDBData.fromJson(Map<String, dynamic> json) {
    return CollectionDBData(
      collectionCode: json["collectionCode"],
      collectionName: json["collectionName"],
      departmentCode: json["departmentCode"],
      departmentName: json["departmentName"],
      extra: json["extra"],
    );
  }
//
}
