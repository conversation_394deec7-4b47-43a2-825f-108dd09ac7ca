# 读者认证

## OpenAPI Specification

```yaml
openapi: 3.0.1
info:
  title: ''
  description: ''
  version: 1.0.0
paths:
  /v1/api/door/verify:
    post:
      summary: 读者认证
      deprecated: false
      description: ''
      tags:
        - 清华门径
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                deviceMac:
                  type: string
                  description: 设备MAC
                patronSn:
                  type: string
                  description: 读者证号 (patronSn,cardSn二选一)
                cardSn:
                  type: string
                  description: 物理卡号 (patronSn,cardSn二选一)
                type:
                  type: integer
                  description: 1=进, 2=出
              required:
                - deviceMac
                - type
              x-apifox-orders:
                - deviceMac
                - patronSn
                - cardSn
                - type
            example:
              deviceMac: FFFFFFFF
              patronSn: ''
              cardSn: ''
              type: 1
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  errorCode:
                    type: integer
                    description: 0成功, 其他失败
                  message:
                    type: string
                    description: 提示信息
                  data:
                    type: 'null'
                required:
                  - errorCode
                  - message
                  - data
                x-apifox-orders:
                  - errorCode
                  - message
                  - data
              example:
                errorCode: 0
                message: 验证通过
                data: null
          headers: {}
          x-apifox-name: 成功
      security: []
      x-apifox-folder: 清华门径
      x-apifox-status: developing
      x-run-in-apifox: https://app.apifox.com/web/project/2805692/apis/api-324815727-run
components:
  schemas: {}
  securitySchemes: {}
servers:
  - url: http://************:9000
    description: 开发环境
security: []

```