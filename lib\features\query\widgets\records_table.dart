import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

import '../../../core/utils/window_util.dart';
import '../models/record_model.dart';
import '../view_models/query_view_model.dart';

class RecordsTable extends StatelessWidget {
  const RecordsTable({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final viewModel = Provider.of<QueryViewModel>(context);
    final dateFormat = DateFormat('yyyy-MM-dd HH:mm:ss');
    
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 40.p, vertical: 20.p),
      decoration: BoxDecoration(
        // color: Colors.white,
        borderRadius: BorderRadius.circular(8.p),
        // boxShadow: [
        //   BoxShadow(
        //     color: Colors.black.withOpacity(0.1),
        //     blurRadius: 4.p,
        //     offset: Offset(0, 2.p),
        //   ),
        // ],
      ),
      child: Column(
        children: [
          // 表头
          Container(
            padding: EdgeInsets.symmetric(vertical: 15.p),
            decoration: BoxDecoration(
              color: const Color(0xFFFFFFFF),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(8.p),
                topRight: Radius.circular(8.p),
              ),
            ),
            child: Row(
              children: [
                _buildHeaderCell('序号', flex: 1),
                _buildHeaderCell('姓名', flex: 2),
                _buildHeaderCell('读者证号', flex: 2),
                _buildHeaderCell('开门时间', flex: 3),
                _buildHeaderCell('认证方式', flex: 2),
                _buildHeaderCell('认证状态', flex: 2),
                _buildHeaderCell('备注', flex: 3),
              ],
            ),
          ),
          
          // 表格内容
          Expanded(
            child: viewModel.isLoading
                ? const Center(child: CircularProgressIndicator())
                : viewModel.records.isEmpty
                    ? _buildEmptyState()
                    : ListView.builder(
                        itemCount: viewModel.records.length,
                        itemBuilder: (context, index) {
                          final record = viewModel.records[index];

                          return Container(
                            color:const Color(0xFFF6F7FA ),
                            margin: EdgeInsets.only(top: 16.p),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                 LeftBorderIndicator(width: 6.p,height: 100.p,),
                                _buildCell('${record.id}', flex: 1),
                                _buildCell(record.name, flex: 2),
                                _buildThreeLineCell(record.readerCardNo, flex: 2),
                                _buildTwoLineCell(
                                  dateFormat.format(record.openTime),
                                  flex: 3,
                                ),
                                _buildCell(_getAuthMethodText(record.authMethod), flex: 2),
                                _buildStatusCell(record.status, flex: 2),
                                _buildMultiLineCell(record.remark, flex: 3),
                              ],
                            ),
                          );
                        },
                      ),
          ),
          
          // 底部提示
          // Container(
          //   padding: EdgeInsets.symmetric(vertical: 10.p),
          //   alignment: Alignment.center,
          //   decoration: BoxDecoration(
          //     color: Colors.white,
          //     borderRadius: BorderRadius.only(
          //       bottomLeft: Radius.circular(8.p),
          //       bottomRight: Radius.circular(8.p),
          //     ),
          //   ),
          //   child: Text(
          //     '本地仅保存近3个月记录，更多记录请在平台查询',
          //     style: TextStyle(
          //       fontSize: 12.p,
          //       color: const Color(0xFF999999),
          //     ),
          //   ),
          // ),
        ],
      ),
    );
  }
  
  // 表头单元格
  Widget _buildHeaderCell(String text, {required int flex}) {
    return Expanded(
      flex: flex,
      child: Center(
        child: Text(
          text,
          style: TextStyle(
            fontSize: 24.p,
            fontWeight: FontWeight.w400,
            color: const Color(0xFF7A8499 ),
          ),
        ),
      ),
    );
  }
  
  // 表格内容单元格
  Widget _buildCell(String text, {required int flex}) {
    return Expanded(
      flex: flex,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 15.p, horizontal: 8.p),
        child: Center(
          child: Text(
            text,
            style: TextStyle(
              fontSize: 18.p,  // 减小字体大小
              color: const Color(0xFF12215F),
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
        ),
      ),
    );
  }

  // 两行文本单元格（用于开门时间）
  Widget _buildTwoLineCell(String text, {required int flex}) {
    return Expanded(
      flex: flex,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 15.p, horizontal: 8.p),
        child: Center(
          child: Text(
            text,
            style: TextStyle(
              fontSize: 18.p,
              color: const Color(0xFF12215F),
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,  // 最多2行
            overflow: TextOverflow.visible,  // 允许换行显示
          ),
        ),
      ),
    );
  }

  // 三行文本单元格（用于读者证号）
  Widget _buildThreeLineCell(String text, {required int flex}) {
    return Expanded(
      flex: flex,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 15.p, horizontal: 8.p),
        child: Center(
          child: Text(
            text,
            style: TextStyle(
              fontSize: 18.p,
              color: const Color(0xFF12215F),
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
            maxLines: 3,  // 最多3行
            overflow: TextOverflow.visible,  // 允许换行显示
          ),
        ),
      ),
    );
  }

  // 状态单元格（带颜色显示）
  Widget _buildStatusCell(String status, {required int flex}) {
    String statusText = _getStatusText(status);
    Color textColor;

    // 根据状态设置颜色
    switch (status.toLowerCase()) {
      case 'success':
        textColor = const Color(0xFF12215F); // 默认蓝色
        break;
      case 'failure':
      case 'failure_no_match':
      case 'failure_error':
        textColor = const Color(0xFFE53E3E); // 红色
        break;
      default:
        textColor = const Color(0xFF12215F); // 默认蓝色
        break;
    }

    return Expanded(
      flex: flex,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 15.p, horizontal: 8.p),
        child: Center(
          child: Text(
            statusText,
            style: TextStyle(
              fontSize: 18.p,
              color: textColor,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
        ),
      ),
    );
  }

  // 多行文本单元格（用于备注）
  Widget _buildMultiLineCell(String text, {required int flex}) {
    return Expanded(
      flex: flex,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 15.p, horizontal: 8.p),
        child: Text(
          text.isEmpty ? '-' : text,
          style: TextStyle(
            fontSize: 16.p,  // 备注字体更小
            color: const Color(0xFF12215F),
            fontWeight: FontWeight.w400,
          ),
          textAlign: TextAlign.left,
          overflow: TextOverflow.ellipsis,
          maxLines: 5,  // 最多5行
        ),
      ),
    );
  }

  // 获取认证方式显示文本
  String _getAuthMethodText(String authMethod) {
    switch (authMethod.toLowerCase()) {
      case 'face':
        return '人脸识别';
      case 'id_card':
        return '身份证';
      case 'qr_code':
        return '二维码';
      case 'card':
        return '读者证';
      default:
        return authMethod.isEmpty ? '-' : authMethod;
    }
  }

  // 获取认证状态显示文本
  String _getStatusText(String status) {
    switch (status.toLowerCase()) {
      case 'success':
        return '成功';
      case 'failure_no_match':
        return '无匹配';
      case 'failure_error':
        return '错误';
      case 'failure':
        return '失败';
      default:
        return status.isEmpty ? '-' : status;
    }
  }
  
  // 空状态
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 60.p,
            color: const Color(0xFFCCCCCC),
          ),
          SizedBox(height: 20.p),
          Text(
            '没有找到符合条件的记录',
            style: TextStyle(
              fontSize: 16.p,
              color: const Color(0xFF999999),
            ),
          ),
        ],
      ),
    );
  }
}


class LeftBorderIndicator extends StatelessWidget {
  final double width;
  final double height;
  final Color color;
  final BorderRadius borderRadius;
  
  const LeftBorderIndicator({
    Key? key, 
    this.width = 6,
    this.height = 80,
    this.color = const Color(0xFF3568FC),
    this.borderRadius = const BorderRadius.only(
      topLeft: Radius.circular(6),
      bottomLeft: Radius.circular(6),
    ),
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: color,
        borderRadius: borderRadius,
      ),
    );
  }
}