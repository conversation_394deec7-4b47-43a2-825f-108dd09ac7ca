import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:a3g/features/adminLogin/views/admin_login_page.dart';
import 'package:a3g/features/changePassword/views/change_password_view.dart';
import 'package:a3g/features/auth/views/auth_page.dart';
import 'package:a3g/features/auth/views/face_detection_page.dart';
import 'package:a3g/features/security_gate/views/gate_home_page.dart';

import '../../features/admin/views/admin_page.dart';
import '../../features/admin/views/admin_page2.dart';
import '../../features/admin/views/error_records_page.dart';
import '../../features/auth/views/auth_page.dart';
import '../../features/face_detection/face_detector_app.dart';
import '../../features/home/<USER>/home_page.dart';
import '../../features/home/<USER>/index_page.dart';
import '../../features/login/models/operation_type.dart';
import '../../features/login/views/login_type_page.dart';
import '../../features/query/views/query_page.dart';
import '../providers/countdown_provider.dart';
import '../providers/user_provider.dart';
import 'package:seasetting/seasetting.dart';
import '../../features/device/views/device_page.dart';

import '../utils/window_util.dart';

abstract class AppRoutes {
  static const index = '/';
  static const home = '/Homepage';
  static const loginType = '/loginType';
  static const auth = '/auth';
  static const auth2 = '/auth2';
  static const faceDetection = '/faceDetection';
  static const borrow = '/borrow';
  static const renew = '/renew';
  static const test = '/test';
  static const setting = '/setting';
  static const returnBook = '/returnBook';
  static const preview = '/preview';
  static const admin = '/admin';
  static const slotManage = '/slotManage';
  static const errorRecord = '/errorRecord';
  static const addBook = '/addBook';
  static const removeBook = '/removeBook';
  static const adminLogin = '/adminLogin';
  static const changePassword = '/changePassword';
  static const admin2 = '/admin2';
  static const query = '/query';
  static const faceDetectorApp = '/faceDetectorApp';
  static const gateHome = '/gateHome';
  // 🔥 新增：增强书籍扫描测试页面
  static const enhancedBookScanTest = '/enhancedBookScanTest';
  static const device = '/device';
}

// 路由页面配置
class AppPages {
  static final pages = [
    GetPage(
      name: AppRoutes.index,
      page: () => const IndexPage(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 350),
    ),
    GetPage(
      name: AppRoutes.home,
      page: () => const HomePage(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 350),
    ),
    GetPage(
      name: AppRoutes.loginType,
      page: () => LoginTypePage(
        operationType: Get.arguments?['operationType'] ?? OperationType.borrow,
      ),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 350),
    ),
    GetPage(
      name: AppRoutes.auth,
      page: () => AuthPage(
        authLoginType: Get.arguments?['authLoginType'] ?? AuthLoginType.unknow,
        operationType: Get.arguments?['operationType'] ?? OperationType.borrow,
      ),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 350),
    ),
    GetPage(
      name: AppRoutes.auth2,
      page: () => AuthPage(
        authLoginType: Get.arguments?['authLoginType'] ?? AuthLoginType.faceAuth, operationType: null,
      ),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 350),
    ),
    GetPage(
      name: AppRoutes.faceDetection,
      page: () => const FaceDetectionPage(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 350),
    ),

    GetPage(
      name: AppRoutes.setting,
      page: () =>  SettingMainPage(2),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 350),
    ),
    GetPage(
      name: AppRoutes.admin,
      page: () => const AdminPage(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 350),
    ),

    GetPage(
      name: AppRoutes.errorRecord,
      page: () => const ErrorRecordsPage(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 350),
    ),


    GetPage(
      name: AppRoutes.adminLogin,
      page: () => const AdminLoginPage(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 350),
    ),
    GetPage(
      name: AppRoutes.changePassword,
      page: () => const ChangePasswordView(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 350),
    ),
        GetPage(
      name: AppRoutes.admin2,
      page: () => const AdminPage2(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 350),
    ),
    GetPage(
      name: AppRoutes.query,
      page: () => const QueryPage(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 350),
    ),
    GetPage(
      name: AppRoutes.faceDetectorApp,
      page: () => const FaceDetectorApp(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 350),
    ),
    GetPage(
      name: AppRoutes.gateHome,
      page: () => const GateHomePage(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 350),
    ),
    GetPage(
      name: AppRoutes.device,
      page: () => const DevicePage(),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 350),
    ),
  ];
}

// 导航助手
class AppNavigator {
  static void toLoginType(OperationType type) {
    final routes = Get.currentRoute.split('/');
    List<Page<dynamic>>? pages = Get.key.currentState?.widget.pages;
    Get.toNamed(AppRoutes.loginType, arguments: {'operationType': type});
  }

  static void toAuth({
    required AuthLoginType authLoginType,
    required OperationType operationType,
  }) {
    Get.toNamed(
      AppRoutes.auth,
      arguments: {
        'authLoginType': authLoginType,
        'operationType': operationType,
      },
    );
  }
  static void toHome() => Get.toNamed(AppRoutes.home);
  static void toBorrow() => Get.toNamed(AppRoutes.borrow);
  static void toReturnBook() => Get.toNamed(AppRoutes.returnBook);
  static void toRenew() => Get.toNamed(AppRoutes.renew);
  static void toPreview() => Get.toNamed(AppRoutes.preview);
  static void toSetting() => Get.toNamed(AppRoutes.setting);
  static void toAdmin() => Get.toNamed(AppRoutes.admin);
  static void toSlotManage() => Get.toNamed(AppRoutes.slotManage);
  static void toErrorRecord() => Get.toNamed(AppRoutes.errorRecord);
  static void toAddBook() => Get.toNamed(AppRoutes.addBook);
  static void toRemoveBook() => Get.toNamed(AppRoutes.removeBook);
  static void toAdminLogin() => Get.toNamed(AppRoutes.adminLogin);
  static void toChangePassword() => Get.toNamed(AppRoutes.changePassword);
  static void toFaceDetectorApp() => Get.toNamed(AppRoutes.faceDetectorApp);
  static void toDevice() => Get.toNamed(AppRoutes.device);
  static void toAuth2({
    AuthLoginType authLoginType = AuthLoginType.faceAuth,
    OperationType operationType = OperationType.borrow,
  }) => Get.toNamed(
    AppRoutes.auth2,
    arguments: {
      'authLoginType': authLoginType,
      'operationType': operationType,
    },
  );
  static void toFaceDetection() => Get.toNamed(AppRoutes.faceDetection);

  static void back<T>({T? result}) => Get.back(result: result);
  static void untilHome() => Get.until((route) => route.settings.name == AppRoutes.home);
  static void offAllToHome() => Get.offAllNamed(AppRoutes.home);
  static void offAllToGateHome() => Get.offAllNamed(AppRoutes.gateHome);
  static void toAdmin2() => Get.toNamed(AppRoutes.admin2);
  static void toQuery() => Get.toNamed(AppRoutes.query);
  // 🔥 新增：导航到增强书籍扫描测试页面
  static void toEnhancedBookScanTest() => Get.toNamed(AppRoutes.enhancedBookScanTest);
}

// 路由中间件（权限验证）
class AuthMiddleware extends GetMiddleware {
  // @override
  // RouteSettings? redirect(String? route) {
  //   final needLogin = [
  //     AppRoutes.borrow,
  //     AppRoutes.renew,
  //     AppRoutes.returnBook,
  //     AppRoutes.query,
  //   ];
  //
  //   if (needLogin.contains(route) && !Get.find<UserProvider>().isLoggedIn) {
  //     return const RouteSettings(
  //       name: AppRoutes.loginType,
  //       arguments: {'operationType': OperationType.borrow},
  //     );
  //   }
  //   return null;
  // }
}

// 路由观察者
// class RouterObserver extends GetObserver {
//   @override
//   void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
//     _updateWindowScale(route);
//   }
//
//   @override
//   void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) {
//     if (previousRoute != null) {
//       _updateWindowScale(previousRoute);
//       if (Get.currentRoute == AppRoutes.home || Get.currentRoute == AppRoutes.index) {
//         navigator?.context.read<UserProvider>().clearUserData();
//       }
//     }
//   }
//
//   void _updateWindowScale(Route<dynamic> route) {
//     final context = route.navigator?.context;
//     if (context != null) {
//       WidgetsBinding.instance.addPostFrameCallback((_) {
//         WindowUtil.updateScale(context);
//       });
//     }
//   }
// }

// 添加页面倒计时设置
class PageCountdownSettings {
  static const Map<String, int> settings = {
    // AppRoutes.index: 120,
    // AppRoutes.home: 120,
    // AppRoutes.loginType: 60,
    // AppRoutes.auth: 60,
    // AppRoutes.borrow: 180,
    // AppRoutes.renew: 120,
    // AppRoutes.returnBook: 180,
    // AppRoutes.preview: 60,
    // AppRoutes.admin: 120,
    // AppRoutes.slotManage: 120,
    // AppRoutes.errorRecord: 120,
    // AppRoutes.addBook: 60,
  };

  static int getCountdownSeconds(String? routeName) {
    return settings[routeName] ?? 60;  // 默认60秒
  }
}

// 修改路由观察者
class RouterObserver extends GetObserver {
  String? _currentRouteName;

  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPush(route, previousRoute);
    _updateWindowScale(route);

    // 只处理命名路由的页面切换
    if (_shouldHandleRoute(route)) {
      _currentRouteName = route.settings.name;
      _resetCountdown(route);
    }
  }

  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPop(route, previousRoute);
    if (previousRoute != null) {
      _updateWindowScale(previousRoute);

      // 只在返回到主页面时重置倒计时
      if (_shouldHandleRoute(previousRoute) &&
          previousRoute.settings.name != _currentRouteName) {
        _currentRouteName = previousRoute.settings.name;
        _resetCountdown(previousRoute);
      }

      if (Get.currentRoute == AppRoutes.home || Get.currentRoute == AppRoutes.index) {
        navigator?.context.read<UserProvider>().clearUserData();
      }
    }
  }

  bool _shouldHandleRoute(Route<dynamic> route) {
    return route.settings.name != null &&
        route.settings.name!.startsWith('/') &&
        route is GetPageRoute;  // 只处理 GetPage 路由
  }

  void _resetCountdown(Route<dynamic> route) {
    final context = route.navigator?.context;
    if (context != null && context.mounted) {
      final seconds = PageCountdownSettings.getCountdownSeconds(route.settings.name);
      context.read<CountdownProvider>().reset(seconds);
    }
  }

  void _updateWindowScale(Route<dynamic> route) {
    final context = route.navigator?.context;
    if (context != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        WindowUtil.updateScale(context);
      });
    }
  }
}