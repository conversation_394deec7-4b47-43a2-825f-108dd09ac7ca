import 'package:flutter/foundation.dart';
import 'rfid_service.dart';
import 'shared_scan_pool_service.dart';
import 'gate_coordinator.dart';

/// RFID持续扫描设计方案测试类
/// 验证新设计的各种场景
class RFIDContinuousScanTest {
  static final RFIDContinuousScanTest _instance = RFIDContinuousScanTest._internal();
  static RFIDContinuousScanTest get instance => _instance;
  RFIDContinuousScanTest._internal();

  /// 测试应用启动时的初始化流程
  static Future<void> testApplicationStartup() async {
    debugPrint('🧪 测试：应用启动时的初始化流程');
    
    try {
      // 1. 测试RFID服务初始化（应该启动持续扫描）
      debugPrint('1. 初始化RFID服务...');
      await RFIDService.instance.initialize();
      
      if (RFIDService.instance.isInitialized) {
        debugPrint('✅ RFID服务初始化成功');
      } else {
        debugPrint('❌ RFID服务初始化失败');
      }
      
      // 2. 测试共享池服务初始化
      debugPrint('2. 初始化共享池服务...');
      await SharedScanPoolService.initializeGlobal();
      
      final poolSize = SharedScanPoolService.instance.poolSize;
      debugPrint('✅ 共享池服务初始化完成，当前大小: $poolSize');
      
      // 3. 测试闸机协调器初始化
      debugPrint('3. 初始化闸机协调器...');
      await GateCoordinator.instance.initialize();
      debugPrint('✅ 闸机协调器初始化完成');
      
      debugPrint('🎉 应用启动初始化测试完成');
    } catch (e) {
      debugPrint('❌ 应用启动初始化测试失败: $e');
    }
  }

  /// 测试出馆流程的数据隔离
  static Future<void> testExitFlowDataIsolation() async {
    debugPrint('🧪 测试：出馆流程的数据隔离');
    
    try {
      final sharedPool = SharedScanPoolService.instance;
      final rfidService = RFIDService.instance;
      
      // 1. 模拟一些历史数据
      debugPrint('1. 添加历史数据到共享池...');
      sharedPool.addBarcode('HISTORY_001');
      sharedPool.addBarcode('HISTORY_002');
      debugPrint('历史数据添加完成，共享池大小: ${sharedPool.poolSize}');
      
      // 2. 模拟出馆开始
      debugPrint('2. 模拟出馆开始...');
      await sharedPool.clearPoolAndBuffer();
      await rfidService.startDataCollection();
      debugPrint('出馆开始处理完成，共享池大小: ${sharedPool.poolSize}');
      
      // 3. 模拟新的扫描数据
      debugPrint('3. 添加新的扫描数据...');
      sharedPool.addBarcode('NEW_001');
      sharedPool.addBarcode('NEW_002');
      debugPrint('新数据添加完成，共享池大小: ${sharedPool.poolSize}');
      
      // 4. 模拟到达指定位置
      debugPrint('4. 模拟到达指定位置...');
      final result = await rfidService.stopDataCollection();
      debugPrint('数据收集停止，收集到${result.length}个条码: $result');
      
      // 5. 验证共享池仍有数据（持续扫描）
      debugPrint('5. 验证共享池状态...');
      debugPrint('共享池大小: ${sharedPool.poolSize}');
      debugPrint('共享池内容: ${sharedPool.getCurrentPool()}');
      
      debugPrint('🎉 出馆流程数据隔离测试完成');
    } catch (e) {
      debugPrint('❌ 出馆流程数据隔离测试失败: $e');
    }
  }

  /// 测试多次出馆的响应速度
  static Future<void> testMultipleExitResponseTime() async {
    debugPrint('🧪 测试：多次出馆的响应速度');
    
    try {
      final sharedPool = SharedScanPoolService.instance;
      final rfidService = RFIDService.instance;
      
      // 模拟第一次出馆
      debugPrint('第一次出馆测试...');
      final startTime1 = DateTime.now();
      
      await sharedPool.clearPoolAndBuffer();
      await rfidService.startDataCollection();
      
      // 添加一些数据
      sharedPool.addBarcode('FIRST_001');
      sharedPool.addBarcode('FIRST_002');
      
      await rfidService.stopDataCollection();
      final endTime1 = DateTime.now();
      final duration1 = endTime1.difference(startTime1).inMilliseconds;
      debugPrint('第一次出馆完成，耗时: ${duration1}ms');
      
      // 等待一段时间，模拟持续扫描添加数据
      await Future.delayed(const Duration(milliseconds: 500));
      sharedPool.addBarcode('CONTINUOUS_001');
      sharedPool.addBarcode('CONTINUOUS_002');
      
      // 模拟第二次出馆（应该更快，因为共享池已有数据）
      debugPrint('第二次出馆测试...');
      final startTime2 = DateTime.now();
      
      await sharedPool.clearPoolAndBuffer();
      await rfidService.startDataCollection();
      
      // 添加新数据
      sharedPool.addBarcode('SECOND_001');
      sharedPool.addBarcode('SECOND_002');
      
      await rfidService.stopDataCollection();
      final endTime2 = DateTime.now();
      final duration2 = endTime2.difference(startTime2).inMilliseconds;
      debugPrint('第二次出馆完成，耗时: ${duration2}ms');
      
      debugPrint('响应时间对比: 第一次${duration1}ms vs 第二次${duration2}ms');
      debugPrint('🎉 多次出馆响应速度测试完成');
    } catch (e) {
      debugPrint('❌ 多次出馆响应速度测试失败: $e');
    }
  }

  /// 测试RFID持续扫描状态
  static Future<void> testContinuousScanningState() async {
    debugPrint('🧪 测试：RFID持续扫描状态');
    
    try {
      final rfidService = RFIDService.instance;
      final sharedPool = SharedScanPoolService.instance;
      
      // 1. 验证初始状态
      debugPrint('1. 验证RFID服务初始状态...');
      debugPrint('RFID服务已初始化: ${rfidService.isInitialized}');
      debugPrint('共享池大小: ${sharedPool.poolSize}');
      
      // 2. 模拟出馆流程
      debugPrint('2. 执行出馆流程...');
      await rfidService.startDataCollection();
      debugPrint('数据收集已启动');
      
      await rfidService.stopDataCollection();
      debugPrint('数据收集已停止');
      
      // 3. 验证RFID扫描仍在运行
      debugPrint('3. 验证RFID扫描状态...');
      // 添加数据到共享池，验证持续扫描
      sharedPool.addBarcode('CONTINUOUS_TEST_001');
      await Future.delayed(const Duration(milliseconds: 100));
      
      if (sharedPool.poolSize > 0) {
        debugPrint('✅ RFID持续扫描正常，共享池仍在更新');
      } else {
        debugPrint('⚠️ 共享池无数据，可能扫描已停止');
      }
      
      debugPrint('🎉 RFID持续扫描状态测试完成');
    } catch (e) {
      debugPrint('❌ RFID持续扫描状态测试失败: $e');
    }
  }

  /// 运行所有测试
  static Future<void> runAllTests() async {
    debugPrint('🚀 开始运行RFID持续扫描设计方案测试套件');
    debugPrint('=' * 60);
    
    await testApplicationStartup();
    debugPrint('-' * 40);
    
    await testExitFlowDataIsolation();
    debugPrint('-' * 40);
    
    await testMultipleExitResponseTime();
    debugPrint('-' * 40);
    
    await testContinuousScanningState();
    debugPrint('-' * 40);
    
    debugPrint('🎉 所有测试完成！');
    debugPrint('=' * 60);
  }

  /// 获取系统状态报告
  static Map<String, dynamic> getSystemStatusReport() {
    final rfidService = RFIDService.instance;
    final sharedPool = SharedScanPoolService.instance;
    
    return {
      'timestamp': DateTime.now().toIso8601String(),
      'rfid_service': {
        'initialized': rfidService.isInitialized,
        'scanning': rfidService.isScanning,
        'scanned_count': rfidService.scannedCount,
      },
      'shared_pool': {
        'pool_size': sharedPool.poolSize,
        'current_pool': sharedPool.getCurrentPool().toList(),
      },
      'design_version': 'RFID持续运行设计方案 v1.0',
    };
  }
}
