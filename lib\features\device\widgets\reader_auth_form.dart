import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/utils/window_util.dart';
import '../../../core/widgets/custom_button.dart';
import '../models/device_model.dart';
import '../view_models/device_view_model.dart';
import 'auto_save_text_field.dart';

class ReaderAuthForm extends StatefulWidget {
  const ReaderAuthForm({Key? key}) : super(key: key);

  @override
  State<ReaderAuthForm> createState() => _ReaderAuthFormState();
}

class _ReaderAuthFormState extends State<ReaderAuthForm> {
  final _formKey = GlobalKey<FormState>();
  final _deviceMacController = TextEditingController();
  final _patronSnController = TextEditingController();
  final _cardSnController = TextEditingController();
  final _timeoutSecondsController = TextEditingController();
  final _retryCountController = TextEditingController();
  final _authModeController = TextEditingController();
  
  PassType _selectedPassType = PassType.enter;
  bool _usePatronSn = true; // true=使用读者证号, false=使用物理卡号

  @override
  void initState() {
    super.initState();
    // 监听ViewModel变化，同步数据到控制器
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final viewModel = context.read<DeviceViewModel>();
      _syncFromViewModel(viewModel);
    });
  }

  @override
  void dispose() {
    _deviceMacController.dispose();
    _patronSnController.dispose();
    _cardSnController.dispose();
    _timeoutSecondsController.dispose();
    _retryCountController.dispose();
    _authModeController.dispose();
    super.dispose();
  }

  void _syncFromViewModel(DeviceViewModel viewModel) {
    final data = viewModel.authData;
    _deviceMacController.text = data.deviceMac;
    _patronSnController.text = data.patronSn ?? '';
    _cardSnController.text = data.cardSn ?? '';
    _selectedPassType = PassType.fromValue(data.type);
    
    // 根据数据判断使用哪种认证方式
    if (data.patronSn != null && data.patronSn!.isNotEmpty) {
      _usePatronSn = true;
    } else if (data.cardSn != null && data.cardSn!.isNotEmpty) {
      _usePatronSn = false;
    }
  }

  void _syncToViewModel() {
    final viewModel = context.read<DeviceViewModel>();
    viewModel.updateAuthData(
      deviceMac: _deviceMacController.text.trim(),
      patronSn: _usePatronSn ? _patronSnController.text.trim().isEmpty ? null : _patronSnController.text.trim() : null,
      cardSn: !_usePatronSn ? _cardSnController.text.trim().isEmpty ? null : _cardSnController.text.trim() : null,
      type: _selectedPassType.value,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<DeviceViewModel>(
      builder: (context, viewModel, child) {
        return Card(
          margin: EdgeInsets.all(16.p),
          child: Padding(
            padding: EdgeInsets.all(24.p),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 标题
                  Text(
                    '读者认证',
                    style: TextStyle(
                      fontSize: 24.p,
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF333333),
                    ),
                  ),
                  SizedBox(height: 24.p),
                  
                  // 设备MAC输入
                  _buildDeviceMacField(),
                  
                  SizedBox(height: 20.p),
                  
                  // 认证方式选择
                  _buildAuthMethodSelector(),
                  
                  SizedBox(height: 20.p),
                  
                  // 认证标识符输入
                  _buildIdentifierField(),
                  
                  SizedBox(height: 20.p),
                  
                  // 通行类型选择
                  _buildPassTypeSelector(),

                  SizedBox(height: 20.p),

                  // 认证配置参数
                  Row(
                    children: [
                      Expanded(
                        child: AutoSaveTextField(
                          fieldKey: 'timeout_seconds',
                          formType: 'reader_auth',
                          controller: _timeoutSecondsController,
                          labelText: '超时时间(秒)',
                          hintText: '请输入认证超时时间',
                          keyboardType: TextInputType.number,
                        ),
                      ),
                      SizedBox(width: 16.p),
                      Expanded(
                        child: AutoSaveTextField(
                          fieldKey: 'retry_count',
                          formType: 'reader_auth',
                          controller: _retryCountController,
                          labelText: '重试次数',
                          hintText: '请输入重试次数',
                          keyboardType: TextInputType.number,
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: 16.p),

                  // 认证模式
                  AutoSaveTextField(
                    fieldKey: 'auth_mode',
                    formType: 'reader_auth',
                    controller: _authModeController,
                    labelText: '认证模式',
                    hintText: '请输入认证模式（如：card_and_face）',
                  ),

                  SizedBox(height: 32.p),
                  
                  // 认证按钮
                  SizedBox(
                    width: double.infinity,
                    child: CustomButton.filled(
                      text: viewModel.isAuthLoading ? '认证中...' : '开始认证',
                      onTap: viewModel.isAuthLoading ? null : () {
                        if (_formKey.currentState!.validate()) {
                          _syncToViewModel();
                          viewModel.authenticateReader();
                        }
                      },
                      disabled: viewModel.isAuthLoading,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildDeviceMacField() {
    return AutoSaveTextField(
      fieldKey: 'device_mac',
      formType: 'reader_auth',
      controller: _deviceMacController,
      labelText: '设备MAC地址 *',
      hintText: '请输入设备MAC地址',
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return '设备MAC地址不能为空';
        }
        return null;
      },
    );
  }

  Widget _buildAuthMethodSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '认证方式 *',
          style: TextStyle(
            fontSize: 16.p,
            fontWeight: FontWeight.w500,
            color: const Color(0xFF333333),
          ),
        ),
        SizedBox(height: 12.p),
        
        Row(
          children: [
            Expanded(
              child: RadioListTile<bool>(
                title: Text(
                  '读者证号',
                  style: TextStyle(fontSize: 14.p),
                ),
                value: true,
                groupValue: _usePatronSn,
                onChanged: (value) {
                  setState(() {
                    _usePatronSn = value!;
                    // 清空另一个输入框
                    if (_usePatronSn) {
                      _cardSnController.clear();
                    } else {
                      _patronSnController.clear();
                    }
                  });
                },
                contentPadding: EdgeInsets.zero,
              ),
            ),
            Expanded(
              child: RadioListTile<bool>(
                title: Text(
                  '物理卡号',
                  style: TextStyle(fontSize: 14.p),
                ),
                value: false,
                groupValue: _usePatronSn,
                onChanged: (value) {
                  setState(() {
                    _usePatronSn = value!;
                    // 清空另一个输入框
                    if (_usePatronSn) {
                      _cardSnController.clear();
                    } else {
                      _patronSnController.clear();
                    }
                  });
                },
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildIdentifierField() {
    final controller = _usePatronSn ? _patronSnController : _cardSnController;
    final label = _usePatronSn ? '读者证号' : '物理卡号';
    final hint = _usePatronSn ? '请输入读者证号' : '请输入物理卡号';
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '$label *',
          style: TextStyle(
            fontSize: 16.p,
            fontWeight: FontWeight.w500,
            color: const Color(0xFF333333),
          ),
        ),
        SizedBox(height: 8.p),
        TextFormField(
          controller: controller,
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: TextStyle(
              fontSize: 14.p,
              color: const Color(0xFF999999),
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.p),
              borderSide: const BorderSide(color: Color(0xFFE0E0E0)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.p),
              borderSide: const BorderSide(color: Color(0xFFE0E0E0)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.p),
              borderSide: const BorderSide(color: Color(0xFF54A0FF)),
            ),
            contentPadding: EdgeInsets.symmetric(horizontal: 12.p, vertical: 12.p),
          ),
          style: TextStyle(fontSize: 14.p),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return '$label不能为空';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildPassTypeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '通行类型 *',
          style: TextStyle(
            fontSize: 16.p,
            fontWeight: FontWeight.w500,
            color: const Color(0xFF333333),
          ),
        ),
        SizedBox(height: 12.p),
        
        Row(
          children: PassType.values.map((type) {
            return Expanded(
              child: Padding(
                padding: EdgeInsets.only(right: type != PassType.values.last ? 12.p : 0),
                child: _buildPassTypeOption(type),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildPassTypeOption(PassType type) {
    final isSelected = _selectedPassType == type;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedPassType = type;
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 16.p, horizontal: 12.p),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFF54A0FF).withOpacity(0.1) : Colors.white,
          border: Border.all(
            color: isSelected ? const Color(0xFF54A0FF) : const Color(0xFFE0E0E0),
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(8.p),
        ),
        child: Column(
          children: [
            // 类型图标
            Container(
              width: 40.p,
              height: 40.p,
              decoration: BoxDecoration(
                color: _getPassTypeColor(type),
                shape: BoxShape.circle,
              ),
              child: Icon(
                _getPassTypeIcon(type),
                color: Colors.white,
                size: 20.p,
              ),
            ),
            SizedBox(height: 8.p),
            
            // 类型文本
            Text(
              type.label,
              style: TextStyle(
                fontSize: 14.p,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                color: isSelected ? const Color(0xFF54A0FF) : const Color(0xFF333333),
              ),
            ),
            
            // 类型值
            Text(
              '(${type.value})',
              style: TextStyle(
                fontSize: 12.p,
                color: const Color(0xFF999999),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getPassTypeColor(PassType type) {
    switch (type) {
      case PassType.enter:
        return const Color(0xFF52C41A); // 绿色
      case PassType.exit:
        return const Color(0xFF1890FF); // 蓝色
    }
  }

  IconData _getPassTypeIcon(PassType type) {
    switch (type) {
      case PassType.enter:
        return Icons.login;
      case PassType.exit:
        return Icons.logout;
    }
  }
}
