import 'package:flutter/foundation.dart';
import 'master_slave_extension.dart';
import 'shared_scan_pool_service.dart';
import 'gate_coordinator.dart';

/// 调试助手
/// 用于测试和调试主从机扩展
class DebugHelper {
  
  /// 测试主从机扩展的完整流程
  static Future<void> testMasterSlaveFlow() async {
    debugPrint('🧪 开始测试主从机扩展流程...');
    
    final extension = MasterSlaveExtension.instance;
    final sharedPool = SharedScanPoolService.instance;
    
    // 1. 检查扩展状态
    debugPrint('📊 扩展状态: ${extension.getStatus()}');
    
    // 2. 检查共享池状态
    debugPrint('📊 共享池状态: ${sharedPool.getStatistics()}');
    
    // 3. 手动添加一个条码到共享池
    sharedPool.addBarcode('DEBUG_TEST_001');
    debugPrint('📚 添加调试条码到共享池');
    
    // 4. 检查共享池是否有数据
    final poolBarcodes = extension.getSharedPoolBarcodes();
    debugPrint('📊 共享池条码: $poolBarcodes');
    
    // 5. 手动触发出馆开始
    debugPrint('🚀 手动触发出馆开始...');
    extension.triggerExitStart();
    
    // 6. 等待一段时间
    await Future.delayed(const Duration(seconds: 1));
    
    // 7. 手动触发位置到达
    debugPrint('📍 手动触发位置到达...');
    extension.triggerPositionReached();
    
    // 8. 等待收集结果
    await Future.delayed(const Duration(seconds: 2));
    
    // 9. 检查收集结果
    final collectedBarcodes = extension.getCollectedBarcodes();
    debugPrint('📊 收集结果: $collectedBarcodes');
    
    // 10. 最终状态
    debugPrint('📊 最终状态: ${extension.getStatus()}');
    
    debugPrint('✅ 主从机扩展流程测试完成');
  }
  
  /// 监听GateCoordinator事件
  static void startEventMonitoring() {
    debugPrint('👂 开始监听GateCoordinator事件...');
    
    final coordinator = GateCoordinator.instance;
    coordinator.eventStream.listen((event) {
      debugPrint('🎯 收到GateCoordinator事件: ${event.type} - ${event.data}');
    });
  }
  
  /// 检查所有服务状态
  static void checkAllServicesStatus() {
    debugPrint('🔍 检查所有服务状态...');
    
    // 1. 主从机扩展
    final extension = MasterSlaveExtension.instance;
    debugPrint('📊 主从机扩展: ${extension.getStatus()}');
    
    // 2. 共享扫描池
    final sharedPool = SharedScanPoolService.instance;
    debugPrint('📊 共享扫描池: ${sharedPool.getStatistics()}');
    
    // 3. GateCoordinator状态
    final coordinator = GateCoordinator.instance;
    debugPrint('📊 GateCoordinator状态: ${coordinator.currentState}');
  }
  
  /// 清理所有测试数据
  static void cleanupTestData() {
    debugPrint('🧹 清理测试数据...');
    
    final sharedPool = SharedScanPoolService.instance;
    sharedPool.clearPool();
    
    debugPrint('✅ 测试数据已清理');
  }
  
  /// 模拟真实的出馆流程
  static Future<void> simulateRealExitFlow() async {
    debugPrint('🎭 模拟真实出馆流程...');
    
    // 1. 添加一些模拟条码到共享池
    final sharedPool = SharedScanPoolService.instance;
    sharedPool.addBarcode('REAL_BOOK_001');
    sharedPool.addBarcode('REAL_BOOK_002');
    debugPrint('📚 添加模拟书籍条码');
    
    // 2. 模拟出馆开始
    debugPrint('🚪 模拟出馆开始...');
    final coordinator = GateCoordinator.instance;
    // 这里应该调用coordinator的方法，但为了测试，我们直接触发扩展
    MasterSlaveExtension.instance.triggerExitStart();
    
    // 3. 等待扫描期间
    debugPrint('⏳ 模拟扫描期间...');
    await Future.delayed(const Duration(seconds: 2));
    
    // 4. 添加更多条码（模拟扫描过程中的条码）
    sharedPool.addBarcode('REAL_BOOK_003');
    debugPrint('📚 扫描期间添加更多条码');
    
    // 5. 模拟到达位置
    debugPrint('📍 模拟到达位置...');
    MasterSlaveExtension.instance.triggerPositionReached();
    
    // 6. 等待处理结果
    await Future.delayed(const Duration(seconds: 2));
    
    // 7. 显示最终结果
    final collectedBarcodes = MasterSlaveExtension.instance.getCollectedBarcodes();
    debugPrint('📊 最终收集的条码: $collectedBarcodes');
    
    debugPrint('✅ 真实出馆流程模拟完成');
  }
}
