import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:window_manager/window_manager.dart';
import 'package:seasetting/seasetting.dart';

import '../providers/app_providers.dart';
import '../services/post_auth_handlers/handlers_registry.dart';
import '../../features/auth/services/auth_service_initializer.dart';

import '../utils/theme_utils.dart';

class AppInitializer {
  static final AppInitializer _instance = AppInitializer._internal();
  factory AppInitializer() => _instance;
  AppInitializer._internal();

  /// 初始化平台配置
  Future<void> initializePlatform() async {
    if (Platform.isWindows || Platform.isLinux) {
      if (kReleaseMode) {
        await windowManager.ensureInitialized();
        WindowOptions windowOptions = const WindowOptions(
          fullScreen: true,
          center: true,
        );
        await windowManager.waitUntilReadyToShow(windowOptions, () async {
          await windowManager.show();
          await windowManager.focus();
        });
      }
    } else {
      await SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
    }
  }

  /// 初始化主题
  void initializeTheme() {
    ThemeConfig.initTheme(ThemeStore.themes.first);
  }
  
  /// 初始化门锁配置管理器
  Future<void> initializeDoorRelayConfig() async {
    try {
      debugPrint('初始化门锁继电器配置管理器...');
      await DoorRelayConfigManager.initialize();
      debugPrint('门锁继电器配置管理器初始化完成');
    } catch (e) {
      debugPrint('门锁继电器配置管理器初始化失败: $e');
      // 不抛出异常，允许应用继续运行
    }
  }

  /// 初始化认证后处理服务
  void initializePostAuthService() {
    debugPrint('初始化认证后处理服务...');
    // 注册默认处理器
    PostAuthHandlersRegistry.registerDefaultHandlers();
    debugPrint('认证后处理服务初始化完成');
  }

  /// 🔥 新增：初始化新认证服务
  Future<void> initializeAuthServices() async {
    try {
      debugPrint('初始化新认证服务...');
      await AuthServiceInitializer.instance.initialize();
      debugPrint('新认证服务初始化完成');
    } catch (e) {
      debugPrint('新认证服务初始化失败: $e');
      // 不抛出异常，允许应用继续运行
    }
  }

  /// 执行所有初始化
  Future<void> initialize() async {
    await initializePlatform();
    await AppProviders.init();
    initializeTheme();
    await initializeDoorRelayConfig(); // 初始化门锁配置管理器
    initializePostAuthService(); // 新增认证后处理服务初始化
    await initializeAuthServices(); // 🔥 新增：初始化新认证服务
  }

  /// 应用退出时的清理工作
  static Future<void> cleanup() async {
    try {
      debugPrint('开始应用清理工作...');

      // 门锁连接清理现在由认证页面负责
      // 这里可以添加其他全局资源的清理

      debugPrint('应用清理工作完成');
    } catch (e) {
      debugPrint('应用清理工作出错: $e');
    }
  }
}