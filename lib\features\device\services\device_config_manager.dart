import 'dart:io';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as path;

/// 设备配置管理器 - 负责管理API接口地址等配置的持久化
class DeviceConfigManager {
  static const String _configFileName = 'device_config.json';
  static const String _defaultBaseUrl = 'http://166.111.120.166:9000/tunano/ldc/entrance';

  // 🧪 简单模拟配置
  static const bool _simulationMode = false;  // 模拟模式开关
  static const List<String> _testPatrons = ['2017621493', '2023620666'];  // 测试patron列表
  static int _testIndex = 0;  // 当前测试索引
  
  static DeviceConfigManager? _instance;
  static DeviceConfigManager get instance => _instance ??= DeviceConfigManager._();
  
  DeviceConfigManager._();
  
  String? _currentBaseUrl;
  String? _configFilePath;
  
  /// 获取配置文件路径
  Future<String> _getConfigFilePath() async {
    if (_configFilePath != null) return _configFilePath!;
    
    // 在exe同目录下创建配置文件
    final exeDir = File(Platform.resolvedExecutable).parent.path;
    _configFilePath = path.join(exeDir, _configFileName);
    return _configFilePath!;
  }
  
  /// 加载配置
  Future<Map<String, dynamic>> _loadConfig() async {
    try {
      final configPath = await _getConfigFilePath();
      final configFile = File(configPath);
      
      if (!configFile.existsSync()) {
        print('设备配置文件不存在，使用默认配置: $configPath');
        return {
          'base_url': _defaultBaseUrl,
          'register_endpoint': '/tunano/ldc/entrance/v1/api/door/register',
          'status_endpoint': '/tunano/ldc/entrance/v1/api/door/status',
          'auth_endpoint': '/tunano/ldc/entrance/v1/api/door/verify',
          'timeout_seconds': 10,
          'last_updated': DateTime.now().toIso8601String(),
        };
      }
      
      final content = await configFile.readAsString();
      final config = json.decode(content) as Map<String, dynamic>;
      print('成功加载设备配置: $config');
      return config;
    } catch (e) {
      print('加载设备配置失败: $e，使用默认配置');
      return {
        'base_url': _defaultBaseUrl,
        'register_endpoint': '/v1/api/door/register',
        'status_endpoint': '/v1/api/door/status',
        'auth_endpoint': '/v1/api/door/verify',
        'timeout_seconds': 10,
        'device_mac': 'FFFFFFFF', // 默认设备MAC地址
        'last_updated': DateTime.now().toIso8601String(),
      };
    }
  }
  
  /// 保存配置
  Future<bool> _saveConfig(Map<String, dynamic> config) async {
    try {
      final configPath = await _getConfigFilePath();
      final configFile = File(configPath);
      
      config['last_updated'] = DateTime.now().toIso8601String();
      final content = const JsonEncoder.withIndent('  ').convert(config);
      
      await configFile.writeAsString(content);
      print('设备配置保存成功: $configPath');
      return true;
    } catch (e) {
      print('保存设备配置失败: $e');
      return false;
    }
  }
  
  /// 获取API基础URL
  Future<String> getBaseUrl() async {
    if (_currentBaseUrl != null) {
      return _currentBaseUrl!;
    }
    
    final config = await _loadConfig();
    _currentBaseUrl = config['base_url'] as String? ?? _defaultBaseUrl;
    return _currentBaseUrl!;
  }
  
  /// 设置API基础URL
  Future<bool> setBaseUrl(String newUrl) async {
    try {
      if (newUrl.isEmpty) {
        throw ArgumentError('API地址不能为空');
      }
      
      // 规范化URL
      String normalizedUrl = newUrl.trim();
      if (!normalizedUrl.startsWith('http://') && !normalizedUrl.startsWith('https://')) {
        normalizedUrl = 'http://$normalizedUrl';
      }
      if (normalizedUrl.endsWith('/')) {
        normalizedUrl = normalizedUrl.substring(0, normalizedUrl.length - 1);
      }
      
      final config = await _loadConfig();
      config['base_url'] = normalizedUrl;
      
      final success = await _saveConfig(config);
      if (success) {
        _currentBaseUrl = normalizedUrl;
        print('API地址已更新: $normalizedUrl');
        return true;
      } else {
        return false;
      }
    } catch (e) {
      print('设置API地址失败: $e');
      return false;
    }
  }
  
  /// 获取注册接口端点
  Future<String> getRegisterEndpoint() async {
    final config = await _loadConfig();
    return config['register_endpoint'] as String? ?? '/v1/api/door/register';
  }
  
  /// 获取状态上传接口端点
  Future<String> getStatusEndpoint() async {
    final config = await _loadConfig();
    return config['status_endpoint'] as String? ?? '/v1/api/door/status';
  }

  /// 获取读者认证接口端点
  Future<String> getAuthEndpoint() async {
    final config = await _loadConfig();
    return config['auth_endpoint'] as String? ?? '/v1/api/door/verify';
  }
  
  /// 获取超时时间
  Future<int> getTimeoutSeconds() async {
    final config = await _loadConfig();
    return config['timeout_seconds'] as int? ?? 10;
  }
  
  /// 获取完整的注册接口URL
  Future<String> getRegisterUrl() async {
    final baseUrl = await getBaseUrl();
    final endpoint = await getRegisterEndpoint();
    return '$baseUrl$endpoint';
  }
  
  /// 获取完整的状态上传接口URL
  Future<String> getStatusUrl() async {
    final baseUrl = await getBaseUrl();
    final endpoint = await getStatusEndpoint();
    return '$baseUrl$endpoint';
  }

  /// 获取完整的读者认证接口URL
  Future<String> getAuthUrl() async {
    final baseUrl = await getBaseUrl();
    final endpoint = await getAuthEndpoint();
    return '$baseUrl$endpoint';
  }
  
  /// 获取所有配置信息
  Future<Map<String, dynamic>> getAllConfig() async {
    return await _loadConfig();
  }
  
  /// 获取设备MAC地址
  Future<String> getDeviceMac() async {
    final config = await _loadConfig();
    return config['device_mac'] as String? ?? 'FFFFFFFF';
  }

  /// 设置设备MAC地址
  Future<bool> setDeviceMac(String mac) async {
    try {
      final config = await _loadConfig();
      config['device_mac'] = mac;
      final success = await _saveConfig(config);
      if (success) {
        debugPrint('设备MAC地址已更新: $mac');
        return true;
      } else {
        return false;
      }
    } catch (e) {
      debugPrint('设置设备MAC地址失败: $e');
      return false;
    }
  }

  /// 重置为默认配置
  Future<bool> resetToDefault() async {
    _currentBaseUrl = null;
    return await setBaseUrl(_defaultBaseUrl);
  }
  
  /// 验证URL格式
  bool isValidUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && (uri.scheme == 'http' || uri.scheme == 'https');
    } catch (e) {
      return false;
    }
  }

  // 🧪 模拟相关方法
  /// 是否启用模拟模式
  bool isSimulationMode() => _simulationMode;

  /// 获取下一个测试patron
  String getNextTestPatron() {
    final currentIndex = _testIndex % _testPatrons.length;
    final patron = _testPatrons[currentIndex];
    _testIndex++;

    // 调试信息：显示当前使用的patron和索引
    debugPrint('🧪 模拟模式 - 使用测试patron: $patron (索引: $currentIndex, 总调用次数: $_testIndex)');

    return patron;
  }

  /// 重置测试patron索引（重新从第一个开始轮流）
  void resetTestPatronIndex() {
    _testIndex = 0;
    debugPrint('🧪 模拟模式 - 测试patron索引已重置');
  }

  /// 获取当前测试patron（不递增索引）
  String getCurrentTestPatron() {
    final currentIndex = _testIndex % _testPatrons.length;
    return _testPatrons[currentIndex];
  }
}
