import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:sea_socket/sea_socket.dart';
import '../models/book_info.dart';

/// SIP2图书信息服务
/// 🔥 借鉴参考项目：使用SIP2协议获取图书信息
class Sip2BookService {
  static Sip2BookService? _instance;
  static Sip2BookService get instance => _instance ??= Sip2BookService._();
  Sip2BookService._();

  // 🚫 移除缓存：书籍信息需要实时获取，确保借阅状态准确性
  
  // 请求配置
  static const Duration _sip2Timeout = Duration(seconds: 5);
  final int _maxRetries = 3;
  final Duration _retryDelay = const Duration(milliseconds: 500);
  
  // 并发控制
  final Map<String, Future<BookInfo?>> _pendingRequests = {};
  final int _maxConcurrentRequests = 10;
  int _currentRequests = 0;

  /// 初始化服务
  void initialize() {
    debugPrint('SIP2图书信息服务初始化完成');
  }

  /// 🔥 借鉴参考项目：通过SIP2协议获取图书信息（实时获取，不缓存）
  Future<BookInfo?> getBookInfo(String barcode) async {
    if (barcode.isEmpty) {
      debugPrint('条码为空，无法获取图书信息');
      return null;
    }

    try {
      // 检查并发请求（防止重复请求，但不缓存结果）
      if (_pendingRequests.containsKey(barcode)) {
        debugPrint('等待正在进行的请求: $barcode');
        return await _pendingRequests[barcode];
      }

      // 检查并发限制
      if (_currentRequests >= _maxConcurrentRequests) {
        debugPrint('达到最大并发请求数，等待...');
        await Future.delayed(const Duration(milliseconds: 100));
        return await getBookInfo(barcode);
      }

      // 创建新的请求
      final future = _fetchBookInfoWithRetry(barcode);
      _pendingRequests[barcode] = future;
      _currentRequests++;

      try {
        final result = await future;
        debugPrint('实时获取图书信息: $barcode - ${result?.bookName ?? "未找到"}');
        return result;
      } finally {
        _pendingRequests.remove(barcode);
        _currentRequests--;
      }

    } catch (e) {
      debugPrint('获取图书信息失败: $barcode, 错误: $e');
      return null;
    }
  }

  /// 🔥 新增：获取书籍信息（实时获取，强制不使用缓存）
  Future<BookInfo?> getBookInfoRealTime(String barcode) async {
    if (barcode.isEmpty) {
      debugPrint('条码为空，无法获取图书信息');
      return null;
    }

    debugPrint('强制实时获取书籍信息（不使用任何缓存）: $barcode');

    // 直接调用SIP2请求，强制不使用缓存
    return await _fetchBookInfoWithRetry(barcode);
  }

  /// 🔥 借鉴参考项目：带重试的SIP2请求
  Future<BookInfo?> _fetchBookInfoWithRetry(String barcode) async {
    for (int attempt = 1; attempt <= _maxRetries; attempt++) {
      try {
        debugPrint('请求书籍信息: $barcode (第$attempt次尝试)');
        
        // 🔥 修改：强制不使用缓存，实时获取
        final sip2BookData = await NewSip2Request.instance
            .requestBookInfo(barcode, readCache: false)
            .timeout(_sip2Timeout);

        if (sip2BookData != null) {
          // 转换SIP2数据为BookInfo
          final bookInfo = _convertSip2DataToBookInfo(sip2BookData);
          if (bookInfo != null) {
            debugPrint('获取书籍信息成功: ${bookInfo.bookName} - ${bookInfo.author}');
            return bookInfo;
          } else {
            debugPrint('SIP2数据转换失败，可能是其他馆的条码: $barcode');
            return null;
          }
        } else {
          debugPrint('SIP2返回空数据: $barcode');
        }

      } catch (e) {
        debugPrint('SIP2请求失败 (第$attempt次): $barcode, 错误: $e');
        
        if (attempt < _maxRetries) {
          await Future.delayed(_retryDelay * attempt);
        }
      }
    }

    // 所有重试都失败，返回模拟数据
    debugPrint('SIP2请求失败，返回模拟数据: $barcode');
    return _createMockBookInfo(barcode);
  }

  /// 🔥 修改：将Sip2BookData转换为BookInfo（实现新的业务逻辑）
  BookInfo? _convertSip2DataToBookInfo(Sip2BookData sip2Data) {
    // 🔥 新逻辑：检查是否有TitleIdentifier，没有则返回null（表示不是本馆图书）
    if (sip2Data.TitleIdentifier == null || sip2Data.TitleIdentifier!.isEmpty) {
      debugPrint('📚 条码 ${sip2Data.ItemIdentifier} 没有TitleIdentifier，可能是其他馆的条码，忽略');
      return null;
    }

    // 🎯 新的借阅状态判断逻辑：
    // 放行条件：CirculationStatus是已借出(04) AND returnDate有时间
    // 拦截条件：CirculationStatus不是已借出 OR returnDate为空

    final isCheckedOut = sip2Data.CirculationStatus == '04'; // 已借出
    final hasValidReturnDate = sip2Data.DueDate != null &&
                              sip2Data.DueDate!.isNotEmpty &&
                              sip2Data.DueDate != '0';

    // 双重验证：必须同时满足已借出状态和有效的归还日期
    final isBorrowed = isCheckedOut && hasValidReturnDate;

    debugPrint('📚 书籍状态判断: 条码=${sip2Data.ItemIdentifier}');
    debugPrint('   书名: ${sip2Data.TitleIdentifier}');
    debugPrint('   流通状态: ${sip2Data.CirculationStatus} (${_getStatusName(sip2Data.CirculationStatus)})');
    debugPrint('   归还日期: ${sip2Data.DueDate ?? "无"}');
    debugPrint('   是否已借出: $isCheckedOut');
    debugPrint('   有效归还日期: $hasValidReturnDate');
    debugPrint('   最终判断: ${isBorrowed ? "✅ 允许通过" : "❌ 需要拦截"}');

    // 🔥 优化：使用Sip2BookData的dayCheckOutIn getter（已处理格式转换）
    DateTime? borrowDate;
    final borrowDateStr = sip2Data.dayCheckOutIn; // 使用getter，已处理格式
    if (borrowDateStr != null && borrowDateStr.isNotEmpty) {
      try {
        borrowDate = DateTime.parse(borrowDateStr);
      } catch (e) {
        debugPrint('解析借阅日期失败: $borrowDateStr');
      }
    }

    // 🔥 优化：使用Sip2BookData的returnDate getter（已处理格式转换）
    DateTime? returnDate;
    final returnDateStr = sip2Data.returnDate; // 使用getter，已处理格式
    if (returnDateStr != null && returnDateStr.isNotEmpty && returnDateStr != '0') {
      try {
        returnDate = DateTime.parse(returnDateStr);
      } catch (e) {
        debugPrint('解析归还日期失败: $returnDateStr');
      }
    }

    return BookInfo(
      barcode: sip2Data.ItemIdentifier ?? '',
      bookName: sip2Data.TitleIdentifier ?? '未知书名',
      author: sip2Data.ItemAuthor,
      isbn: sip2Data.ISBN,
      isBorrowed: isBorrowed,
      borrowDate: borrowDate,
      returnDate: returnDate,
      borrowerName: sip2Data.PatronIdentifier, // 使用读者证号作为借阅者名称
      borrowerId: sip2Data.PatronIdentifier,
    );
  }

  /// 获取流通状态名称
  String _getStatusName(String? circulationStatus) {
    switch (circulationStatus) {
      case '01':
        return '其他';
      case '02':
        return '在架';
      case '03':
        return '已借';
      case '04':
        return '已借出'; // 🎯 关键状态：只有这个状态才允许通过
      case '05':
        return '已预约并通知';
      case '06':
        return '在途';
      case '07':
        return '已预约';
      case '08':
        return '等待重新上架';
      case '09':
        return '等待重新上架并预约';
      case '10':
        return '在途并预约';
      case '11':
        return '丢失';
      case '12':
        return '缺失';
      case '13':
        return '丢失并已付费';
      default:
        return '未知';
    }
  }

  /// 创建模拟图书信息
  BookInfo _createMockBookInfo(String barcode) {
    return BookInfo(
      barcode: barcode,
      bookName: '图书名称_$barcode',
      author: '作者${barcode.length}',
      isbn: '978-7-${barcode.substring(0, 3.clamp(0, barcode.length))}-${barcode.substring(3.clamp(0, barcode.length))}',
      isBorrowed: true, // 模拟已借状态
      borrowDate: DateTime.now().subtract(const Duration(days: 7)),
      returnDate: DateTime.now().add(const Duration(days: 23)),
      borrowerName: '读者${barcode.substring(barcode.length - 3)}',
      borrowerId: 'R${barcode.substring(barcode.length - 6)}',
    );
  }

  /// 获取服务统计信息
  Map<String, dynamic> getServiceStats() {
    return {
      'current_requests': _currentRequests,
      'max_concurrent_requests': _maxConcurrentRequests,
      'pending_requests': _pendingRequests.length,
      'cache_disabled': true, // 标记缓存已禁用
      'real_time_data': true, // 标记使用实时数据
    };
  }

  /// 资源清理
  void dispose() {
    _pendingRequests.clear();
    _currentRequests = 0;
    debugPrint('SIP2图书信息服务已清理');
  }
}
