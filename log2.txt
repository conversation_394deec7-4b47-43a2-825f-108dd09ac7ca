2025-08-27 15:17:42.755548: currentXmlPath:C:\Users\<USER>\Desktop\Release (1)\Release\packages\sea_socket\lib\src\protocols
2025-08-27 15:17:42.756540: 61
2025-08-27 15:17:44.068703: 复制 动态库完毕
2025-08-27 15:17:44.075679: ✅ 数据库更新成功: planConfig
2025-08-27 15:17:44.075679: ✅ 数据库更新成功: SysConfig
2025-08-27 15:17:44.075679: ✅ 数据库更新成功: Sip2Config
2025-08-27 15:17:44.076676: ✅ 数据库更新成功: pageConfig
2025-08-27 15:17:44.076676: ✅ 数据库更新成功: readerConfig
2025-08-27 15:17:44.076676: ✅ 数据库更新成功: banZhengConfig
2025-08-27 15:17:44.077673: ✅ 数据库更新成功: printConfig
2025-08-27 15:17:44.281070: 开始初始化闸机协调器...
2025-08-27 15:17:44.282062: ✅ 已清除串口配置缓存，下次访问将重新从数据库读取
2025-08-27 15:17:44.283058: ✅ 已清除 SettingProvider 串口配置缓存
2025-08-27 15:17:44.301994: ✅ 通过 getSerialConfig 获取串口配置: COM1 @ 115200
2025-08-27 15:17:44.302994: ✅ 串口配置加载完成: COM1 @ 115200
2025-08-27 15:17:44.302994: 可用串口: [COM1, COM2, COM3, COM4, COM5, COM6]
2025-08-27 15:17:44.306977: 连接闸机串口: COM1
2025-08-27 15:17:44.306977: 尝试连接串口: COM1, 波特率: 115200
2025-08-27 15:17:44.307974: 串口连接成功: COM1 at 115200 baud
2025-08-27 15:17:44.307974: 开始监听串口数据
2025-08-27 15:17:44.308971: 串口连接状态变化: true
2025-08-27 15:17:44.308971: 闸机串口连接成功
2025-08-27 15:17:44.308971: 串口 COM1 连接成功 (波特率: 115200)
2025-08-27 15:17:44.308971: 闸机串口服务初始化成功: COM1
2025-08-27 15:17:44.309967: 开始监听串口数据（通过 GateSerialManager 事件流）
2025-08-27 15:17:44.309967: 开始监听闸机串口命令
2025-08-27 15:17:44.309967: 开始初始化RFID服务和共享池...
2025-08-27 15:17:44.309967: 开始初始化增强RFID服务...
2025-08-27 15:17:44.310964: 开始初始化增强RFID服务...
2025-08-27 15:17:44.310964: SIP2图书信息服务初始化完成
2025-08-27 15:17:44.310964: 增强RFID服务初始化完成，配置了1个阅读器
2025-08-27 15:17:44.311961: 📋 从数据库读取主从机配置: channel_2
2025-08-27 15:17:44.311961: 📋 配置详情: 从机模式
2025-08-27 15:17:44.312958: ⚠️ 从机模式：跳过RFID硬件扫描启动，等待主机数据同步
2025-08-27 15:17:44.312958: 📋 从机配置: 通道=channel_2, 主机地址=**************
2025-08-27 15:17:44.312958: 增强RFID服务初始化完成，持续扫描已启动
2025-08-27 15:17:44.313955: 📋 从数据库读取主从机配置: channel_2
2025-08-27 15:17:44.314951: 📋 配置详情: 从机模式
2025-08-27 15:17:44.314951: ⚠️ 从机模式：跳过持续数据收集，等待主机数据同步
2025-08-27 15:17:44.314951: 📋 从机配置: 通道=channel_2, 主机地址=**************
2025-08-27 15:17:44.314951: 开始全局初始化共享扫描池服务...
2025-08-27 15:17:44.315948: 共享扫描池已集成现有RFID服务
2025-08-27 15:17:44.315948: 📡 初始化后RFID扫描状态: scanning=false
2025-08-27 15:17:44.315948: 🔄 开始重置已处理条码集合...
2025-08-27 15:17:44.315948: 📊 重置前状态: 大小=0, 内容=[]
2025-08-27 15:17:44.316945: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-27 15:17:44.316945: 🔄 当前场上标签将被重新识别为新标签
2025-08-27 15:17:44.316945: 📊 当前tagList状态: 0个标签
2025-08-27 15:17:44.316945: ⚠️ 当前未在扫描状态，跳过立即轮询
2025-08-27 15:17:44.317941: 共享扫描池服务全局初始化完成
2025-08-27 15:17:44.317941: 🚀 初始化新架构服务...
2025-08-27 15:17:44.318938: 🖥️ 主机模式：使用主机集合A服务
2025-08-27 15:17:44.318938: ⏹️ 书籍信息查询服务停止监听
2025-08-27 15:17:44.318938: 🚀 书籍信息查询服务开始监听集合A变化
2025-08-27 15:17:44.318938: ✅ 新架构服务初始化完成
2025-08-27 15:17:44.319934: RFID服务和共享池初始化完成，持续扫描已启动
2025-08-27 15:17:44.319934: 闸机协调器初始化完成
2025-08-27 15:17:44.319934: 🔧 开始初始化主从机扩展（使用持久化配置）...
2025-08-27 15:17:44.320931: 开始初始化主从机扩展...
2025-08-27 15:17:44.320931: 从 seasetting 数据库加载主从机配置成功: channel_2
2025-08-27 15:17:44.320931: 配置详情: 从机模式
2025-08-27 15:17:44.321928: 📡 从 SettingProvider 获取串口配置: COM1 @ 115200
2025-08-27 15:17:44.322924: ✅ 通过 SettingProvider 加载串口配置成功
2025-08-27 15:17:44.322924: 启用主从机扩展: channel_2 (从机)
2025-08-27 15:17:44.322924: ✅ 数据变化通知流已创建
2025-08-27 15:17:44.322924: 共享扫描池已集成现有RFID服务
2025-08-27 15:17:44.323921: 📡 初始化后RFID扫描状态: scanning=false
2025-08-27 15:17:44.323921: 🔄 开始重置已处理条码集合...
2025-08-27 15:17:44.323921: 📊 重置前状态: 大小=0, 内容=[]
2025-08-27 15:17:44.323921: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-27 15:17:44.324918: 🔄 当前场上标签将被重新识别为新标签
2025-08-27 15:17:44.324918: 📊 当前tagList状态: 0个标签
2025-08-27 15:17:44.324918: ⚠️ 当前未在扫描状态，跳过立即轮询
2025-08-27 15:17:44.324918: 配置为从机模式: **************:8888
2025-08-27 15:17:44.327908: 成功连接到主机: **************:8888
2025-08-27 15:17:44.327908: 从机模式配置完成
2025-08-27 15:17:44.328906: [channel_2] 已集成现有GateCoordinator，开始监听事件
2025-08-27 15:17:44.328906: 主从机扩展启用成功
2025-08-27 15:17:44.328906: 主从机扩展初始化完成
2025-08-27 15:17:44.329901: 配置信息: MasterSlaveConfig(channelId: channel_2, isMaster: false, slaveAddress: null, masterAddress: **************, port: 8888)
2025-08-27 15:17:44.330899: ✅ 加载到持久化配置: 从机模式, 通道: channel_2
2025-08-27 15:17:44.332893: 主从机扩展初始化完成
2025-08-27 15:17:44.332893: 安全闸机系统初始化完成
2025-08-27 15:17:44.485383: socket 连接成功,isBroadcast:false
2025-08-27 15:17:44.485383: changeSocketStatus:true
2025-08-27 15:17:44.485383: Sip2HeartBeatManager start loginACS:false askACS:false
2025-08-27 15:17:44.486380: Req msgType：Sip2MsgType.login ,length:72， ret:  9300CNhlsp_sip2|COsip2|CP3a40852d-49fd-4df2-a1f9-6e2641a6e91f|AY1AZEC16
2025-08-27 15:17:44.496347: 开始初始化MultiAuthManager...
2025-08-27 15:17:44.496347: 多认证管理器状态变更: initializing
2025-08-27 15:17:44.497349: 认证优先级管理器: 开始加载认证方式
2025-08-27 15:17:44.498344: 配置的排序: [读者证认证, 身份证认证, 腾讯E证通认证, 手动输入认证, 人脸识别认证, 社保卡认证, 市民卡认证, 微信二维码认证, 借阅宝认证, 支付宝二维码认证, 芝麻信用码认证, 支付宝二维码认证（阿里信用）, 微信/支付宝认证, 二维码读者认证, IMI身份认证, 电子社保卡认证, 拍照配置, 上海随申码认证, 微信扫码认证, 汇文二维码]
2025-08-27 15:17:44.498344: 可用的认证方式: [读者证认证, 身份证认证, 腾讯E证通认证, 手动输入认证, 人脸识别认证, 社保卡认证, 市民卡认证, 微信二维码认证, 借阅宝认证, 支付宝二维码认证, 芝麻信用码认证, 支付宝二维码认证（阿里信用）, 微信/支付宝认证, 二维码读者认证, IMI身份认证, 电子社保卡认证, 拍照配置, 上海随申码认证, 微信扫码认证, 汇文二维码]
2025-08-27 15:17:44.498344: 认证优先级管理器: 按配置顺序添加 读者证认证 -> 读者证
2025-08-27 15:17:44.498344: 认证优先级管理器: 最终排序结果: 读者证
2025-08-27 15:17:44.499338: 认证优先级管理器: 主要认证方式: 读者证
2025-08-27 15:17:44.499338: 多认证管理器: 从优先级管理器加载的认证方式: 读者证
2025-08-27 15:17:44.500339: 多认证管理器: 当前默认显示方式: 读者证
2025-08-27 15:17:44.500339: 初始化读卡器认证服务
2025-08-27 15:17:44.501331: 读卡器认证服务初始化成功
2025-08-27 15:17:44.503328: 初始化共享读卡器认证服务
2025-08-27 15:17:44.504320: 读者证 认证服务初始化成功
2025-08-27 15:17:44.504320: 认证服务初始化完成，共初始化 1 种认证方式
2025-08-27 15:17:44.505318: 多认证管理器状态变更: idle
2025-08-27 15:17:44.505318: 多认证管理器初始化完成，启用的认证方式: [AuthMethod.readerCard]
2025-08-27 15:17:44.506314: MultiAuthManager初始化完成
2025-08-27 15:17:44.506314: 开始初始化SilencePageViewModel...
2025-08-27 15:17:44.506314: 闸机串口服务已经初始化
2025-08-27 15:17:44.506314: 开始初始化闸机认证服务...
2025-08-27 15:17:44.507311: 闸机认证服务初始化完成，启用认证方式: 人脸识别、读者证、AuthMethod.wechatScanQRCode
2025-08-27 15:17:44.507311: RFID服务已经初始化
2025-08-27 15:17:44.508319: SIP2图书信息服务初始化完成
2025-08-27 15:17:44.508319: 💡 主从机扩展已准备就绪，请通过配置页面手动启用
2025-08-27 15:17:44.509305: 💡 可以通过MasterSlaveConfigPage进行配置
2025-08-27 15:17:44.509305: ✅ 统一事件监听已设置：SilencePageViewModel → GateCoordinator.eventStream
2025-08-27 15:17:44.509305: 串口监听已经启动
2025-08-27 15:17:44.510301: SilencePageViewModel初始化完成
2025-08-27 15:17:44.621930: Rsp : 941AY1AZFDFC
2025-08-27 15:17:44.645849: loginRsp:{OK: 1, MsgSeqId: 1AZFDFC}
2025-08-27 15:17:44.646846: Sip2HeartBeatManager start loginACS:false askACS:true
2025-08-27 15:17:44.646846: 发送心跳
2025-08-27 15:17:44.647842: Req msgType：Sip2MsgType.scStatus ,length:20， ret:  9900522.00AY2AZFC9F
2025-08-27 15:17:44.697682: dispose IndexPage
2025-08-27 15:17:44.698674: IndexPage dispose
2025-08-27 15:17:44.763457: Rsp : 98YYYNNN00500320250827    1517302.00AOhlsp|AM海恒图书馆|BXYYYYYYYYYYYYYYYY|AN3a40852d-49fd-4df2-a1f9-6e2641a6e91f|AF|AG|AY2AZD51F
2025-08-27 15:17:45.009638: 🔍 主从机模式检测: 启用=true, 主机模式=false
2025-08-27 15:17:45.010636: 🔍 扩展详细状态: {enabled: true, channel_id: channel_2, is_master: false, data_stream_ready: true, data_stream_exists: true, data_stream_closed: false, shared_pool_size: 0, queue_size: 0, comm_connected: true, timestamp: 2025-08-27T15:17:45.009638}
2025-08-27 15:17:45.011632: 🎯 检测到从机模式，设置数据监听
2025-08-27 15:17:45.012629: 🔧 开始设置从机持续数据监听...
2025-08-27 15:17:45.013626: ✅ 从机持续数据监听已设置
2025-08-27 15:18:11.933758: 接收到数据: aa 00 c8 80 00 00 27 82
2025-08-27 15:18:11.935746: 🔍 接收到串口数据: aa 00 c8 80 00 00 27 82
2025-08-27 15:18:11.935746: 🔍 数据长度: 8 字节
2025-08-27 15:18:11.936742: 🔍 预定义命令列表:
2025-08-27 15:18:11.936742:   GateCommand.enterStart: aa 00 64 80 00 00 06 d2
2025-08-27 15:18:11.937739:   GateCommand.enterEnd: aa 00 65 80 00 00 07 2e
2025-08-27 15:18:11.937739:   GateCommand.exitStart: aa 00 c8 80 00 00 27 82
2025-08-27 15:18:11.937739:   GateCommand.exitEnd: aa 00 c9 80 00 00 26 7e
2025-08-27 15:18:11.938735:   GateCommand.reachPosition: aa 00 0a 80 00 00
2025-08-27 15:18:11.938735:   GateCommand.enterOpen: aa 00 02 01 00 00 48 72
2025-08-27 15:18:11.938735:   GateCommand.exitOpen: aa 00 01 01 00 00 48 36
2025-08-27 15:18:11.938735:   GateCommand.failSignal: aa 00 02 01 00 00 48 72
2025-08-27 15:18:11.938735:   GateCommand.tailgating: aa 00 0f 80 00 00
2025-08-27 15:18:11.939732:   GateCommand.doorHasPerson: aa 00 0b 80 00 00
2025-08-27 15:18:11.939732: ✅ 解析到闸机命令: GateCommand.exitStart
2025-08-27 15:18:11.939732: 解析到闸机命令: exit_start (出馆开始)
2025-08-27 15:18:11.939732: 收到闸机命令: exit_start (出馆开始)
2025-08-27 15:18:11.940728: 🚪 收到出馆开始命令，等待出馆到位信号...
2025-08-27 15:18:11.940728: 闸机状态变更: GateState.idle -> GateState.exitStarted
2025-08-27 15:18:11.940728: 闸机状态更新: GateState.idle -> GateState.exitStarted
2025-08-27 15:18:11.941725: 📊 流程状态：出馆流程已开始，等待到位信号
2025-08-27 15:18:11.941725: [channel_2] 收到闸机事件: state_changed
2025-08-27 15:18:11.941725: 📨 收到GateCoordinator事件: state_changed
2025-08-27 15:18:11.942722: 闸机状态变更: GateState.exitStarted
2025-08-27 15:18:11.942722: 🎨 处理状态变更UI: exitStarted
2025-08-27 15:18:11.942722: 未处理的状态变更UI: exitStarted
2025-08-27 15:18:11.943718: [channel_2] 收到闸机事件: exit_start
2025-08-27 15:18:11.943718: [channel_2] 主从机扩展：处理出馆开始（等待到位信号）
2025-08-27 15:18:11.943718: 📱 [channel_2] 从机模式：等待到位信号
2025-08-27 15:18:11.943718: ⏳ [channel_2] 出馆开始完成，等待到位信号...
2025-08-27 15:18:11.944715: 📨 收到GateCoordinator事件: exit_start
2025-08-27 15:18:11.944715: 页面状态变更: SilencePageState.waitingExit
2025-08-27 15:18:13.652586: 接收到数据: aa 00 0a 80 00 00 1a 3a
2025-08-27 15:18:13.653585: 🔍 接收到串口数据: aa 00 0a 80 00 00 1a 3a
2025-08-27 15:18:13.654580: 🔍 数据长度: 8 字节
2025-08-27 15:18:13.654580: 🔍 预定义命令列表:
2025-08-27 15:18:13.654580:   GateCommand.enterStart: aa 00 64 80 00 00 06 d2
2025-08-27 15:18:13.654580:   GateCommand.enterEnd: aa 00 65 80 00 00 07 2e
2025-08-27 15:18:13.655576:   GateCommand.exitStart: aa 00 c8 80 00 00 27 82
2025-08-27 15:18:13.655576:   GateCommand.exitEnd: aa 00 c9 80 00 00 26 7e
2025-08-27 15:18:13.655576:   GateCommand.reachPosition: aa 00 0a 80 00 00
2025-08-27 15:18:13.655576:   GateCommand.enterOpen: aa 00 02 01 00 00 48 72
2025-08-27 15:18:13.656573:   GateCommand.exitOpen: aa 00 01 01 00 00 48 36
2025-08-27 15:18:13.656573:   GateCommand.failSignal: aa 00 02 01 00 00 48 72
2025-08-27 15:18:13.656573:   GateCommand.tailgating: aa 00 0f 80 00 00
2025-08-27 15:18:13.656573:   GateCommand.doorHasPerson: aa 00 0b 80 00 00
2025-08-27 15:18:13.656573: ✅ 解析到闸机命令: GateCommand.reachPosition
2025-08-27 15:18:13.657569: 解析到闸机命令: position_reached (到达指定位置)
2025-08-27 15:18:13.689464: 收到闸机命令: position_reached (到达指定位置)
2025-08-27 15:18:13.690460: 📍 收到到位信号，当前状态: GateState.exitStarted
2025-08-27 15:18:13.690460: 📊 流程状态：进馆=false, 出馆=true
2025-08-27 15:18:13.690460: 📊 待处理认证：进馆=false, 出馆=false
2025-08-27 15:18:13.691457: 🚪 出馆到位信号，启动认证和10秒数据收集...
2025-08-27 15:18:13.691457: 闸机状态变更: GateState.exitStarted -> GateState.exitWaitingAuth
2025-08-27 15:18:13.691457: 闸机状态更新: GateState.exitStarted -> GateState.exitWaitingAuth
2025-08-27 15:18:13.691457: 🔐 启动出馆认证系统（不关注结果）...
2025-08-27 15:18:13.692453: 闸机状态变更: GateState.exitWaitingAuth -> GateState.exitScanning
2025-08-27 15:18:13.692453: 闸机状态更新: GateState.exitWaitingAuth -> GateState.exitScanning
2025-08-27 15:18:13.692453: 多认证管理器状态变更: listening
2025-08-27 15:18:13.693450: 启动所有认证方式监听: [AuthMethod.readerCard]
2025-08-27 15:18:13.693450: 准备启动 1 个物理认证服务
2025-08-27 15:18:13.693450: 开始读卡器认证监听
2025-08-27 15:18:13.693450: 🔥 测试：跳过强制重新配置，保持现有连接
2025-08-27 15:18:13.693450: 已移除读卡器状态监听器
2025-08-27 15:18:13.694446: 已移除标签数据监听器
2025-08-27 15:18:13.694446: 所有卡片监听器已移除
2025-08-27 15:18:13.694446: 已添加读卡器状态监听器
2025-08-27 15:18:13.694446: 已添加标签数据监听器
2025-08-27 15:18:13.695443: 开始监听卡片数据 - 所有监听器已就绪
2025-08-27 15:18:13.695443: 读卡器认证监听启动成功
2025-08-27 15:18:13.695443: ✅ 出馆认证系统已启动
2025-08-27 15:18:13.695443: 🚀 启动出馆5秒数据收集...
2025-08-27 15:18:13.696440: 🔧 启动5秒计时器，当前时间: 2025-08-27 15:18:13.658577
2025-08-27 15:18:13.696440: 🔧 5秒计时器已设置，计时器对象: Instance of '_Timer'
2025-08-27 15:18:13.696440: 📡 开始从共享池收集数据...
2025-08-27 15:18:13.696440: 🔧 从机模式：请求主机清空共享池准备收集新数据
2025-08-27 15:18:13.697437: 🧹 [channel_2] 已清空收集的数据
2025-08-27 15:18:13.697437: 🧹 已清空从机收集数据，避免去重问题
2025-08-27 15:18:13.697437: 📤 [channel_2] 发送清空请求 (尝试1/2): d70e8132-e44f-42a6-8b15-5a4eaa0ca80c
2025-08-27 15:18:13.697437: [channel_2] 收到闸机事件: state_changed
2025-08-27 15:18:13.698433: 📨 收到GateCoordinator事件: state_changed
2025-08-27 15:18:13.698433: 闸机状态变更: GateState.exitWaitingAuth
2025-08-27 15:18:13.698433: 🎨 处理状态变更UI: exitWaitingAuth
2025-08-27 15:18:13.698433: 未处理的状态变更UI: exitWaitingAuth
2025-08-27 15:18:13.699430: 读者证 认证服务启动成功
2025-08-27 15:18:13.699430: 所有认证服务启动完成，成功启动 1 个服务
2025-08-27 15:18:13.699430: 当前可用的认证方式: 读者证
2025-08-27 15:18:13.699430: [channel_2] 收到闸机事件: state_changed
2025-08-27 15:18:13.700427: 📨 收到GateCoordinator事件: state_changed
2025-08-27 15:18:13.700427: 闸机状态变更: GateState.exitScanning
2025-08-27 15:18:13.700427: 🎨 处理状态变更UI: exitScanning
2025-08-27 15:18:13.700427: 页面状态变更: SilencePageState.rfidScanning
2025-08-27 15:18:13.918700: 📨 收到清空响应: d70e8132-e44f-42a6-8b15-5a4eaa0ca80c - true
2025-08-27 15:18:13.919699: ✅ [channel_2] 清空请求成功: 清除2个条码
2025-08-27 15:18:13.919699: ✅ 从机请求主机清空共享池成功
2025-08-27 15:18:13.919699: 🔄 从机开始5秒数据收集...
2025-08-27 15:18:13.920694: 🔄 [channel_2] 启动5秒数据收集...
2025-08-27 15:18:13.920694: 🔄 [channel_2] 数据收集定时器已启动（每500ms）
2025-08-27 15:18:13.920694: ✅ [channel_2] 持续数据获取已启动
2025-08-27 15:18:14.420030: 📤 [channel_2] 请求主机当前数据 (尝试1/1)...
2025-08-27 15:18:14.421028: 📨 收到数据响应: fe91be97-3c86-454e-9a8b-c21a24c1918d - true
2025-08-27 15:18:14.421028: ✅ [channel_2] 获取主机数据成功: 2个条码
2025-08-27 15:18:14.422024: 📋 [channel_2] 数据详情: [E004015304F3DD22, E004015305F68508]
2025-08-27 15:18:14.422024: 📥 [channel_2] 收集到新数据: 2个条码 - [E004015304F3DD22, E004015305F68508]
2025-08-27 15:18:14.422024: 📊 [channel_2] 累计收集: 2个条码 - [E004015304F3DD22, E004015305F68508]
2025-08-27 15:18:14.422024: [channel_2] 通知收集到的条码: [E004015304F3DD22, E004015305F68508]
2025-08-27 15:18:14.423020: ✅ [channel_2] 数据流通知发送成功: 2个条码
2025-08-27 15:18:14.423020: 🎯 持续数据监听触发: 2个条码
2025-08-27 15:18:14.423020: 🎯 _handleMasterSlaveData 被调用: 2个条码
2025-08-27 15:18:14.424018: 📊 当前模式: 主从机=true, 主机=false
2025-08-27 15:18:14.424018: 📊 当前页面状态: SilencePageState.rfidScanning
2025-08-27 15:18:14.425014: 📥 从机接收到数据: 2个条码 - [E004015304F3DD22, E004015305F68508]
2025-08-27 15:18:14.425014: 📊 扫描列表更新: 0 -> 2
2025-08-27 15:18:14.426012: 📱 更新页面显示: 2个条码
2025-08-27 15:18:14.426012: 页面状态变更: SilencePageState.rfidScanning
2025-08-27 15:18:14.426012: ✅ 从机数据处理完成
2025-08-27 15:18:14.921361: 📤 [channel_2] 请求主机当前数据 (尝试1/1)...
2025-08-27 15:18:14.922358: 📨 收到数据响应: 63e50397-3d8c-4864-af5d-7e60051f15c7 - true
2025-08-27 15:18:14.922358: ✅ [channel_2] 获取主机数据成功: 2个条码
2025-08-27 15:18:14.922358: 📋 [channel_2] 数据详情: [E004015304F3DD22, E004015305F68508]
2025-08-27 15:18:15.421695: 📤 [channel_2] 请求主机当前数据 (尝试1/1)...
2025-08-27 15:18:15.422692: 📨 收到数据响应: 0132e618-b4b2-437b-aad1-667e70035b35 - true
2025-08-27 15:18:15.422692: ✅ [channel_2] 获取主机数据成功: 2个条码
2025-08-27 15:18:15.423689: 📋 [channel_2] 数据详情: [E004015304F3DD22, E004015305F68508]
2025-08-27 15:18:15.921031: 📤 [channel_2] 请求主机当前数据 (尝试1/1)...
2025-08-27 15:18:15.922034: 📨 收到数据响应: 07a0a0e5-ef72-4776-982f-8bfe4bd780e2 - true
2025-08-27 15:18:15.923028: ✅ [channel_2] 获取主机数据成功: 2个条码
2025-08-27 15:18:15.923028: 📋 [channel_2] 数据详情: [E004015304F3DD22, E004015305F68508]
2025-08-27 15:18:16.420369: 📤 [channel_2] 请求主机当前数据 (尝试1/1)...
2025-08-27 15:18:16.421367: 📨 收到数据响应: 4165b884-0cf5-4434-a592-beb3da4c8032 - true
2025-08-27 15:18:16.421367: ✅ [channel_2] 获取主机数据成功: 2个条码
2025-08-27 15:18:16.421367: 📋 [channel_2] 数据详情: [E004015304F3DD22, E004015305F68508]
2025-08-27 15:18:16.920275: 📤 [channel_2] 请求主机当前数据 (尝试1/1)...
2025-08-27 15:18:16.921273: 📨 收到数据响应: f33787ee-9a5b-4a03-ba2a-b2e5586ae000 - true
2025-08-27 15:18:16.922269: ✅ [channel_2] 获取主机数据成功: 2个条码
2025-08-27 15:18:16.922269: 📋 [channel_2] 数据详情: [E004015304F3DD22, E004015305F68508]
2025-08-27 15:18:17.421605: 📤 [channel_2] 请求主机当前数据 (尝试1/1)...
2025-08-27 15:18:17.422604: 📨 收到数据响应: c6c97966-6e43-4b22-b547-50a37566bba6 - true
2025-08-27 15:18:17.423600: ✅ [channel_2] 获取主机数据成功: 2个条码
2025-08-27 15:18:17.423600: 📋 [channel_2] 数据详情: [E004015304F3DD22, E004015305F68508]
2025-08-27 15:18:17.920942: 📤 [channel_2] 请求主机当前数据 (尝试1/1)...
2025-08-27 15:18:17.921940: 📨 收到数据响应: dfe69a5a-ecce-4c65-ace0-40c0c74db65a - true
2025-08-27 15:18:17.921940: ✅ [channel_2] 获取主机数据成功: 2个条码
2025-08-27 15:18:17.921940: 📋 [channel_2] 数据详情: [E004015304F3DD22, E004015305F68508]
2025-08-27 15:18:18.420279: 📤 [channel_2] 请求主机当前数据 (尝试1/1)...
2025-08-27 15:18:18.421277: 📨 收到数据响应: 4c57dbf5-3c21-4bbf-bf6c-a26d6f8a1e1d - true
2025-08-27 15:18:18.421277: ✅ [channel_2] 获取主机数据成功: 2个条码
2025-08-27 15:18:18.421277: 📋 [channel_2] 数据详情: [E004015304F3DD22, E004015305F68508]
2025-08-27 15:18:18.659483: ⏰ 5秒数据收集完成，开始书籍检查... 时间: 2025-08-27 15:18:18.659483
2025-08-27 15:18:18.660481: 🛑 停止出馆数据收集，收集到UID数量: 0
2025-08-27 15:18:18.660481: 🔧 停止时间: 2025-08-27 15:18:18.659483
2025-08-27 15:18:18.661477: 📡 停止从共享池收集数据...
2025-08-27 15:18:18.661477: 🔧 保持RFID扫描运行，只停止从共享池收集数据
2025-08-27 15:18:18.661477: 📡 开始从RFID服务收集UID数据...
2025-08-27 15:18:18.661477: 📊 HWTagProvider.tagList为空，无UID数据
2025-08-27 15:18:18.662474: 📡 从RFID服务收集到UID数量: 0
2025-08-27 15:18:18.662474: 📋 UID列表: 
2025-08-27 15:18:18.663471: 📊 数据收集完成，UID列表: []
2025-08-27 15:18:18.663471: 🔍 开始三步书籍检查，UID数量: 0
2025-08-27 15:18:18.663471: 闸机状态变更: GateState.exitScanning -> GateState.exitChecking
2025-08-27 15:18:18.664468: 闸机状态更新: GateState.exitScanning -> GateState.exitChecking
2025-08-27 15:18:18.664468: 未收集到UID，允许通过
2025-08-27 15:18:18.665468: ✅ 允许出馆: 未检测到书籍，请通过
2025-08-27 15:18:18.665468: ✅ 命令顺序正确：出馆流程中且最后命令是到位信号
2025-08-27 15:18:18.666460: 📤 正在发送成功信号到闸机：AA 00 01 01 00 00 48 36
2025-08-27 15:18:18.666460: 📤 准备发送闸机命令: success_signal
2025-08-27 15:18:18.667456: 📤 成功信号已发送（异步）
2025-08-27 15:18:18.667456: 闸机状态变更: GateState.exitChecking -> GateState.exitOver
2025-08-27 15:18:18.668453: 闸机状态更新: GateState.exitChecking -> GateState.exitOver
2025-08-27 15:18:18.668453: [channel_2] 收到闸机事件: state_changed
2025-08-27 15:18:18.668453: 📨 收到GateCoordinator事件: state_changed
2025-08-27 15:18:18.669450: 闸机状态变更: GateState.exitChecking
2025-08-27 15:18:18.669450: 🎨 处理状态变更UI: exitChecking
2025-08-27 15:18:18.669450: 未处理的状态变更UI: exitChecking
2025-08-27 15:18:18.670446: 发送原始命令数据: aa 00 01 01 00 00 48 36
2025-08-27 15:18:18.670446: 发送原始数据: aa 00 01 01 00 00 48 36
2025-08-27 15:18:18.670446: 发送数据成功: aa 00 01 01 00 00 48 36
2025-08-27 15:18:18.671443: [channel_2] 收到闸机事件: state_changed
2025-08-27 15:18:18.671443: 📨 收到GateCoordinator事件: state_changed
2025-08-27 15:18:18.671443: 闸机状态变更: GateState.exitOver
2025-08-27 15:18:18.672440: 🎨 处理状态变更UI: exitOver
2025-08-27 15:18:18.672440: 未处理的状态变更UI: exitOver
2025-08-27 15:18:18.672440: 发送数据成功: aa 00 01 01 00 00 48 36
2025-08-27 15:18:18.672440: 闸机命令发送成功: success_signal
2025-08-27 15:18:18.672440: ✅ 闸机命令 success_signal 发送成功
2025-08-27 15:18:18.673437: [channel_2] 收到闸机事件: exit_allowed
2025-08-27 15:18:18.673437: 📨 收到GateCoordinator事件: exit_allowed
2025-08-27 15:18:18.673437: 页面状态变更: SilencePageState.exitAllowed
2025-08-27 15:18:18.693370: 接收到数据: aa 00 01 81 00 00 49 de
2025-08-27 15:18:18.694368: 🔍 接收到串口数据: aa 00 01 81 00 00 49 de
2025-08-27 15:18:18.694368: 🔍 数据长度: 8 字节
2025-08-27 15:18:18.695364: 🔍 预定义命令列表:
2025-08-27 15:18:18.695364:   GateCommand.enterStart: aa 00 64 80 00 00 06 d2
2025-08-27 15:18:18.696361:   GateCommand.enterEnd: aa 00 65 80 00 00 07 2e
2025-08-27 15:18:18.696361:   GateCommand.exitStart: aa 00 c8 80 00 00 27 82
2025-08-27 15:18:18.697361:   GateCommand.exitEnd: aa 00 c9 80 00 00 26 7e
2025-08-27 15:18:18.697361:   GateCommand.reachPosition: aa 00 0a 80 00 00
2025-08-27 15:18:18.698354:   GateCommand.enterOpen: aa 00 02 01 00 00 48 72
2025-08-27 15:18:18.698354:   GateCommand.exitOpen: aa 00 01 01 00 00 48 36
2025-08-27 15:18:18.699352:   GateCommand.failSignal: aa 00 02 01 00 00 48 72
2025-08-27 15:18:18.699352:   GateCommand.tailgating: aa 00 0f 80 00 00
2025-08-27 15:18:18.699352:   GateCommand.doorHasPerson: aa 00 0b 80 00 00
2025-08-27 15:18:18.700353: ❌ 未识别的闸机命令 - 数据不匹配任何预定义命令
2025-08-27 15:18:18.700353: ❌ 接收数据: aa 00 01 81 00 00 49 de
2025-08-27 15:18:18.920614: 📤 [channel_2] 请求主机当前数据 (尝试1/1)...
2025-08-27 15:18:18.921611: 📨 收到数据响应: f5ff0226-8c3d-4807-869f-95c9db8183b7 - true
2025-08-27 15:18:18.921611: ✅ [channel_2] 获取主机数据成功: 2个条码
2025-08-27 15:18:18.922607: 📋 [channel_2] 数据详情: [E004015304F3DD22, E004015305F68508]
2025-08-27 15:18:18.923603: ⏰ [channel_2] 5秒数据收集完成，自动停止
2025-08-27 15:18:18.923603: ⏹️ [channel_2] 持续数据获取已停止
2025-08-27 15:18:21.661846: 模拟出馆完成，状态重置为idle
2025-08-27 15:18:21.661846: 闸机状态变更: GateState.exitOver -> GateState.idle
2025-08-27 15:18:21.662844: 闸机状态更新: GateState.exitOver -> GateState.idle
2025-08-27 15:18:21.662844: [channel_2] 收到闸机事件: state_changed
2025-08-27 15:18:21.662844: 📨 收到GateCoordinator事件: state_changed
2025-08-27 15:18:21.663840: 闸机状态变更: GateState.idle
2025-08-27 15:18:21.663840: 🎨 处理状态变更UI: idle
2025-08-27 15:18:21.663840: 页面状态变更: SilencePageState.welcome
2025-08-27 15:18:28.153310: 接收到数据: aa 00 c9 80 00 00 26 7e
2025-08-27 15:18:28.154313: 🔍 接收到串口数据: aa 00 c9 80 00 00 26 7e
2025-08-27 15:18:28.154313: 🔍 数据长度: 8 字节
2025-08-27 15:18:28.154313: 🔍 预定义命令列表:
2025-08-27 15:18:28.155304:   GateCommand.enterStart: aa 00 64 80 00 00 06 d2
2025-08-27 15:18:28.155304:   GateCommand.enterEnd: aa 00 65 80 00 00 07 2e
2025-08-27 15:18:28.155304:   GateCommand.exitStart: aa 00 c8 80 00 00 27 82
2025-08-27 15:18:28.155304:   GateCommand.exitEnd: aa 00 c9 80 00 00 26 7e
2025-08-27 15:18:28.156301:   GateCommand.reachPosition: aa 00 0a 80 00 00
2025-08-27 15:18:28.156301:   GateCommand.enterOpen: aa 00 02 01 00 00 48 72
2025-08-27 15:18:28.156301:   GateCommand.exitOpen: aa 00 01 01 00 00 48 36
2025-08-27 15:18:28.156301:   GateCommand.failSignal: aa 00 02 01 00 00 48 72
2025-08-27 15:18:28.157297:   GateCommand.tailgating: aa 00 0f 80 00 00
2025-08-27 15:18:28.157297:   GateCommand.doorHasPerson: aa 00 0b 80 00 00
2025-08-27 15:18:28.157297: ✅ 解析到闸机命令: GateCommand.exitEnd
2025-08-27 15:18:28.157297: 解析到闸机命令: exit_end (出馆结束)
2025-08-27 15:18:28.158293: 收到闸机命令: exit_end (出馆结束)
2025-08-27 15:18:28.158293: 出馆流程结束
2025-08-27 15:18:28.158293: 📊 流程状态已清除：进馆=false, 出馆=false
2025-08-27 15:18:28.159290: [channel_2] 收到闸机事件: exit_end
2025-08-27 15:18:28.159290: [channel_2] 主从机扩展：处理出馆结束
2025-08-27 15:18:28.160287: [channel_2] 清空处理队列，当前大小: 0
2025-08-27 15:18:28.160287: [channel_2] 处理队列已清空
2025-08-27 15:18:28.161284: ⏹️ [channel_2] 持续数据获取已停止
2025-08-27 15:18:28.161284: 🧹 [channel_2] 出馆结束：已停止数据获取
2025-08-27 15:18:28.161284: 📨 收到GateCoordinator事件: exit_end
2025-08-27 15:18:28.161284: 页面状态变更: SilencePageState.welcome
2025-08-27 15:18:28.161284: [channel_2] 通知收集到的条码: []
2025-08-27 15:18:28.162280: ✅ [channel_2] 数据流通知发送成功: 0个条码
2025-08-27 15:18:28.162280: 🎯 持续数据监听触发: 0个条码
2025-08-27 15:18:28.163277: 🎯 _handleMasterSlaveData 被调用: 0个条码
2025-08-27 15:18:28.163277: 📊 当前模式: 主从机=true, 主机=false
2025-08-27 15:18:28.163277: 📊 当前页面状态: SilencePageState.welcome
2025-08-27 15:18:28.163277: 📥 从机接收到数据: 0个条码 - []
2025-08-27 15:18:28.163277: 📊 扫描列表更新: 2 -> 0
2025-08-27 15:18:28.164273: ⚠️ 页面状态不是扫描相关状态，跳过页面更新: SilencePageState.welcome
2025-08-27 15:18:28.164273: ✅ 从机数据处理完成
