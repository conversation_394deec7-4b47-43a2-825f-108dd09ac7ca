import 'package:flutter/foundation.dart';
import 'door_auth_service.dart';
import 'user_info_service.dart';
import 'gate_context_service.dart';
import '../../device/services/device_config_manager.dart';

/// 认证服务初始化器
/// 负责初始化所有新的认证相关服务
class AuthServiceInitializer {
  static AuthServiceInitializer? _instance;
  static AuthServiceInitializer get instance => _instance ??= AuthServiceInitializer._();
  AuthServiceInitializer._();

  bool _isInitialized = false;
  bool get isInitialized => _isInitialized;

  /// 初始化所有认证服务
  Future<void> initialize() async {
    if (_isInitialized) {
      debugPrint('认证服务已经初始化，跳过重复初始化');
      return;
    }

    try {
      debugPrint('开始初始化认证服务...');

      // 1. 初始化设备配置管理器
      debugPrint('1. 初始化设备配置管理器...');
      final configManager = DeviceConfigManager.instance;
      // DeviceConfigManager 无需特殊初始化，在首次使用时自动初始化

      // 2. 初始化门径认证服务
      debugPrint('2. 初始化门径认证服务...');
      final doorAuthService = DoorAuthService.instance;
      await doorAuthService.initialize();

      // 3. 初始化用户信息服务（无需特殊初始化）
      debugPrint('3. 初始化用户信息服务...');
      // UserInfoService 是无状态的，无需特殊初始化

      // 4. 初始化闸机上下文服务（无需特殊初始化）
      debugPrint('4. 初始化闸机上下文服务...');
      // GateContextService 是无状态的，无需特殊初始化

      // 5. 验证服务状态
      debugPrint('5. 验证服务状态...');
      await _validateServices();

      _isInitialized = true;
      debugPrint('✅ 认证服务初始化完成');

    } catch (e) {
      debugPrint('❌ 认证服务初始化失败: $e');
      _isInitialized = false;
      rethrow;
    }
  }

  /// 验证所有服务的状态
  Future<void> _validateServices() async {
    try {
      // 验证门径认证服务
      final doorAuthStatus = DoorAuthService.instance.getStatus();
      debugPrint('服务状态: ${doorAuthStatus['service_name']} - ${doorAuthStatus['version']}');

      // 验证用户信息服务
      final userInfoStatus = UserInfoService.instance.getStatus();
      debugPrint('服务状态: ${userInfoStatus['service_name']} - ${userInfoStatus['version']}');

      // 验证闸机上下文服务
      final gateContextStatus = GateContextService.instance.getStatus();
      debugPrint('服务状态: ${gateContextStatus['service_name']} - ${gateContextStatus['version']}');
    } catch (e) {
      debugPrint('验证服务状态失败: $e');
    }

    // 测试门径认证服务连接
    try {
      final doorAuthService = DoorAuthService.instance;
      final connectionOk = await doorAuthService.testConnection();
      debugPrint('门径认证服务连接测试: ${connectionOk ? '成功' : '失败'}');
    } catch (e) {
      debugPrint('门径认证服务连接测试异常: $e');
    }
  }

  /// 重置所有服务
  Future<void> reset() async {
    try {
      debugPrint('重置认证服务...');

      // 重置闸机上下文服务
      GateContextService.instance.reset();

      _isInitialized = false;
      debugPrint('认证服务已重置');
    } catch (e) {
      debugPrint('重置认证服务失败: $e');
    }
  }

  /// 获取所有服务的状态
  Map<String, dynamic> getAllServicesStatus() {
    return {
      'initializer_status': {
        'is_initialized': _isInitialized,
        'service_name': 'AuthServiceInitializer',
        'version': '1.0.0',
      },
      'door_auth_service': DoorAuthService.instance.getStatus(),
      'user_info_service': UserInfoService.instance.getStatus(),
      'gate_context_service': GateContextService.instance.getStatus(),
    };
  }

  /// 设置设备MAC地址（便捷方法）
  Future<void> setDeviceMac(String mac) async {
    try {
      await DoorAuthService.instance.setDeviceMac(mac);
      debugPrint('设备MAC地址已更新: $mac');
    } catch (e) {
      debugPrint('设置设备MAC地址失败: $e');
      rethrow;
    }
  }

  /// 获取当前设备MAC地址
  String? getDeviceMac() {
    return DoorAuthService.instance.deviceMac;
  }
}
