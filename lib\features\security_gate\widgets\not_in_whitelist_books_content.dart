import 'package:a3g/core/utils/window_util.dart';
import 'package:flutter/material.dart';
import '../models/silence_page_state.dart';
import '../models/book_info.dart';

/// 不在白名单书籍信息显示组件
class NotInWhitelistBooksContent extends StatefulWidget {
  final UIContentData data;
  
  const NotInWhitelistBooksContent({
    Key? key,
    required this.data,
  }) : super(key: key);

  @override
  State<NotInWhitelistBooksContent> createState() => _NotInWhitelistBooksContentState();
}

class _NotInWhitelistBooksContentState extends State<NotInWhitelistBooksContent>
    with TickerProviderStateMixin {
  
  late AnimationController _slideController;
  late AnimationController _fadeController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;
  
  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimations();
  }
  
  void _initializeAnimations() {
    _slideController = AnimationController(
      duration: Duration(milliseconds: 800),
      vsync: this,
    );
    
    _fadeController = AnimationController(
      duration: Duration(milliseconds: 600),
      vsync: this,
    );
    
    _slideAnimation = Tween<Offset>(
      begin: Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.elasticOut,
    ));
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));
  }
  
  void _startAnimations() {
    _fadeController.forward();
    _slideController.forward();
  }
  
  @override
  Widget build(BuildContext context) {
    final books = widget.data.books ?? [];
    
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.black.withOpacity(0.8),
        child: Center(
          child: SlideTransition(
            position: _slideAnimation,
            child: Container(
              width: 800.p,
              height: 600.p,
              margin: EdgeInsets.all(40.p),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20.p),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.3),
                    blurRadius: 20,
                    offset: Offset(0, 10),
                  ),
                ],
              ),
              child: Column(
                children: [
                  // 标题栏
                  _buildHeader(books.length),
                  
                  // 书籍列表内容
                  Expanded(
                    child: _buildBooksContent(books),
                  ),
                  
                  // 底部提示
                  _buildBottomTip(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
  
  /// 构建标题栏
  Widget _buildHeader(int bookCount) {
    return Container(
      padding: EdgeInsets.all(20.p),
      decoration: BoxDecoration(
        color: Colors.red,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.p),
          topRight: Radius.circular(20.p),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error,
            color: Colors.white,
            size: 32.p,
          ),
          SizedBox(width: 15.p),
          Expanded(
            child: Text(
              '检测到不在白名单的书籍',
              style: TextStyle(
                color: Colors.white,
                fontSize: 24.p,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Text(
            '共 $bookCount 本',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16.p,
            ),
          ),
        ],
      ),
    );
  }
  
  /// 构建书籍内容
  Widget _buildBooksContent(List<BookInfo> books) {
    if (books.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.book_outlined,
              size: 80.p,
              color: Colors.grey[400],
            ),
            SizedBox(height: 20.p),
            Text(
              '未检测到书籍',
              style: TextStyle(
                fontSize: 24.p,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }
    
    return Padding(
      padding: EdgeInsets.all(20.p),
      child: Column(
        children: [
          // 提示信息
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(15.p),
            decoration: BoxDecoration(
              color: Colors.red.withOpacity(0.1),
              borderRadius: BorderRadius.circular(10.p),
              border: Border.all(color: Colors.red.withOpacity(0.3)),
            ),
            child: Row(
              children: [
                Icon(Icons.warning, color: Colors.red, size: 20.p),
                SizedBox(width: 10.p),
                Expanded(
                  child: Text(
                    '以下书籍不在白名单中，禁止通行',
                    style: TextStyle(
                      fontSize: 16.p,
                      color: Colors.red,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          SizedBox(height: 20.p),
          
          // 书籍列表
          Expanded(
            child: _buildBooksList(books),
          ),
        ],
      ),
    );
  }
  
  /// 构建书籍列表
  Widget _buildBooksList(List<BookInfo> books) {
    return ListView.builder(
      itemCount: books.length,
      itemBuilder: (context, index) {
        final book = books[index];
        
        return Container(
          margin: EdgeInsets.only(bottom: 10.p),
          padding: EdgeInsets.all(15.p),
          decoration: BoxDecoration(
            color: Colors.red.withOpacity(0.05),
            borderRadius: BorderRadius.circular(10.p),
            border: Border.all(
              color: Colors.red.withOpacity(0.2),
              width: 1,
            ),
          ),
          child: Row(
            children: [
              // 状态图标
              Container(
                width: 40.p,
                height: 40.p,
                decoration: BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.close,
                  color: Colors.white,
                  size: 20.p,
                ),
              ),
              
              SizedBox(width: 15.p),
              
              // 书籍信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 条码
                    Row(
                      children: [
                        Text(
                          '条码: ',
                          style: TextStyle(
                            fontSize: 14.p,
                            color: Colors.grey[600],
                          ),
                        ),
                        Text(
                          book.barcode,
                          style: TextStyle(
                            fontSize: 16.p,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                    
                    SizedBox(height: 5.p),
                    
                    // 书名
                    Row(
                      children: [
                        Text(
                          '书名: ',
                          style: TextStyle(
                            fontSize: 14.p,
                            color: Colors.grey[600],
                          ),
                        ),
                        Expanded(
                          child: Text(
                            book.bookName,
                            style: TextStyle(
                              fontSize: 14.p,
                              color: Colors.grey[800],
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                    
                    SizedBox(height: 5.p),
                    
                    // 状态
                    Row(
                      children: [
                        Text(
                          '状态: ',
                          style: TextStyle(
                            fontSize: 14.p,
                            color: Colors.grey[600],
                          ),
                        ),
                        Text(
                          _getStatusText(book),
                          style: TextStyle(
                            fontSize: 14.p,
                            color: _getStatusColor(book),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                    
                    // 异常信息（暂时不显示，因为BookInfo模型中没有message字段）
                    // if (book.message.isNotEmpty) ...[
                    //   SizedBox(height: 5.p),
                    //   Row(
                    //     children: [
                    //       Text(
                    //         '异常: ',
                    //         style: TextStyle(
                    //           fontSize: 14.p,
                    //           color: Colors.grey[600],
                    //         ),
                    //       ),
                    //       Expanded(
                    //         child: Text(
                    //           book.message,
                    //           style: TextStyle(
                    //             fontSize: 14.p,
                    //             color: Colors.orange[700],
                    //           ),
                    //           maxLines: 1,
                    //           overflow: TextOverflow.ellipsis,
                    //         ),
                    //       ),
                    //     ],
                    //   ),
                    // ],
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
  
  /// 获取状态文本
  String _getStatusText(BookInfo book) {
    if (book.isBorrowed) {
      return '已借阅';
    } else {
      return '未借阅';
    }
  }

  /// 获取状态颜色
  Color _getStatusColor(BookInfo book) {
    if (book.isBorrowed) {
      return Colors.green;
    } else {
      return Colors.red;
    }
  }
  
  /// 构建底部提示
  Widget _buildBottomTip() {
    return Container(
      padding: EdgeInsets.all(20.p),
      child: Row(
        children: [
          Expanded(
            child: Text(
              '请退出通道处理不在白名单的书籍',
              style: TextStyle(
                fontSize: 16.p,
                color: Colors.red,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Text(
            '6秒后自动消失',
            style: TextStyle(
              fontSize: 14.p,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }
  
  @override
  void dispose() {
    _slideController.dispose();
    _fadeController.dispose();
    super.dispose();
  }
}
