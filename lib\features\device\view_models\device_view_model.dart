import 'package:flutter/material.dart';
import 'package:oktoast/oktoast.dart';
import '../models/device_model.dart';
import '../services/device_api_service.dart';
import '../services/unified_api_config_manager.dart';

class DeviceViewModel extends ChangeNotifier {
  // 常量定义
  static const int _maxRetries = 3;
  static const Duration _requestTimeout = Duration(seconds: 10);

  // 私有变量
  bool _isLoading = false;
  bool _isRegisterLoading = false;
  bool _isStatusLoading = false;
  bool _isAuthLoading = false;
  String _errorMessage = '';
  bool _isDisposed = false;
  String _authBaseUrl = '';      // 认证相关接口baseurl
  String _bookBaseUrl = '';      // 图书相关接口baseurl
  String _libraryCode = '';
  String _deviceMac = '';

  // 设备注册表单数据
  DeviceRegisterModel _registerData = DeviceRegisterModel(deviceMac: '');

  // 设备状态数据
  DeviceStatusModel _statusData = DeviceStatusModel(deviceMac: '', status: 1);

  // 读者认证数据
  ReaderAuthModel _authData = ReaderAuthModel(deviceMac: '', type: 1);

  // 服务实例
  final DeviceApiService _apiService = DeviceApiService.instance;
  final UnifiedApiConfigManager _configManager = UnifiedApiConfigManager.instance;
  
  // Getters
  bool get isLoading => _isLoading;
  bool get isRegisterLoading => _isRegisterLoading;
  bool get isStatusLoading => _isStatusLoading;
  bool get isAuthLoading => _isAuthLoading;
  String get errorMessage => _errorMessage;
  String get authBaseUrl => _authBaseUrl;      // 认证相关接口baseurl
  String get bookBaseUrl => _bookBaseUrl;      // 图书相关接口baseurl
  String get libraryCode => _libraryCode;
  String get deviceMac => _deviceMac;
  DeviceRegisterModel get registerData => _registerData;
  DeviceStatusModel get statusData => _statusData;
  ReaderAuthModel get authData => _authData;

  // 保持向后兼容
  @Deprecated('请使用 authBaseUrl 或 bookBaseUrl')
  String get baseUrl => _authBaseUrl;
  
  /// 安全执行函数：仅在组件未被销毁时执行
  void _safeRun(VoidCallback callback) {
    if (!_isDisposed) callback();
  }

  /// 安全修改状态：仅在组件未被销毁时执行并通知监听器
  void _safeUpdate(VoidCallback callback) {
    if (_isDisposed) return;
    callback();
    notifyListeners();
  }
  
  /// 显示错误信息
  void _showError(String message) {
    _safeUpdate(() {
      _errorMessage = message;
      _isLoading = false;
      _isRegisterLoading = false;
      _isStatusLoading = false;
      _isAuthLoading = false;
    });
    showToast(_errorMessage, position: ToastPosition.bottom);
  }
  
  /// 显示成功信息
  void _showSuccess(String message) {
    showToast(message, position: ToastPosition.bottom);
  }
  
  /// 初始化ViewModel
  Future<void> init() async {
    if (_isDisposed) return;
    
    try {
      await _loadConfig();
      await _apiService.initialize();
    } catch (e) {
      _showError('初始化失败: ${e.toString()}');
    }
  }
  
  /// 加载配置
  Future<void> _loadConfig() async {
    try {
      final authUrl = await _configManager.getAuthBaseUrl();
      final bookUrl = await _configManager.getBookBaseUrl();
      final libraryCode = await _configManager.getLibraryCode();
      final deviceMac = await _configManager.getDeviceMac();

      _safeUpdate(() {
        _authBaseUrl = authUrl;
        _bookBaseUrl = bookUrl;
        _libraryCode = libraryCode;
        _deviceMac = deviceMac;
      });
    } catch (e) {
      _showError('加载配置失败: ${e.toString()}');
    }
  }
  
  /// 更新认证相关接口的基础URL
  Future<void> updateAuthBaseUrl(String newUrl) async {
    if (_isDisposed) return;

    try {
      if (newUrl.isEmpty) {
        _showError('认证接口地址不能为空');
        return;
      }

      await _configManager.updateAuthBaseUrl(newUrl);
      _safeUpdate(() {
        _authBaseUrl = newUrl;
      });
      await _apiService.initialize(); // 重新初始化API服务
      _showSuccess('认证接口地址更新成功');
    } catch (e) {
      _showError('更新认证接口地址失败: ${e.toString()}');
    }
  }

  /// 更新图书相关接口的基础URL
  Future<void> updateBookBaseUrl(String newUrl) async {
    if (_isDisposed) return;

    try {
      if (newUrl.isEmpty) {
        _showError('图书接口地址不能为空');
        return;
      }

      await _configManager.updateBookBaseUrl(newUrl);
      _safeUpdate(() {
        _bookBaseUrl = newUrl;
      });
      _showSuccess('图书接口地址更新成功');
    } catch (e) {
      _showError('更新图书接口地址失败: ${e.toString()}');
    }
  }

  /// 更新API基础URL（保持向后兼容，更新认证baseurl）
  @Deprecated('请使用 updateAuthBaseUrl() 或 updateBookBaseUrl()')
  Future<void> updateBaseUrl(String newUrl) async {
    await updateAuthBaseUrl(newUrl);
  }

  /// 更新图书馆代码
  Future<void> updateLibraryCode(String newLibraryCode) async {
    if (_isDisposed) return;

    try {
      if (newLibraryCode.isEmpty) {
        _showError('图书馆代码不能为空');
        return;
      }

      await _configManager.updateLibraryCode(newLibraryCode);
      _safeUpdate(() {
        _libraryCode = newLibraryCode;
      });
      _showSuccess('图书馆代码更新成功');
    } catch (e) {
      _showError('图书馆代码更新失败: ${e.toString()}');
    }
  }

  /// 更新设备MAC地址
  Future<void> updateDeviceMac(String newDeviceMac) async {
    if (_isDisposed) return;

    try {
      if (newDeviceMac.isEmpty) {
        _showError('设备MAC地址不能为空');
        return;
      }

      await _configManager.updateDeviceMac(newDeviceMac);
      _safeUpdate(() {
        _deviceMac = newDeviceMac;
      });
      _showSuccess('设备MAC地址更新成功');
    } catch (e) {
      _showError('设备MAC地址更新失败: ${e.toString()}');
    }
  }
  
  /// 更新设备注册数据
  void updateRegisterData({
    String? deviceMac,
    String? deviceName,
    String? doorCode,
    String? doorName,
    String? areaCode,
    String? areaName,
    String? libId,
    String? libName,
  }) {
    _safeUpdate(() {
      _registerData = _registerData.copyWith(
        deviceMac: deviceMac,
        deviceName: deviceName,
        doorCode: doorCode,
        doorName: doorName,
        areaCode: areaCode,
        areaName: areaName,
        libId: libId,
        libName: libName,
      );
    });
  }
  
  /// 更新设备状态数据
  void updateStatusData({
    String? deviceMac,
    int? status,
  }) {
    _safeUpdate(() {
      _statusData = _statusData.copyWith(
        deviceMac: deviceMac,
        status: status,
      );
    });
  }

  /// 更新读者认证数据
  void updateAuthData({
    String? deviceMac,
    String? patronSn,
    String? cardSn,
    int? type,
  }) {
    _safeUpdate(() {
      _authData = _authData.copyWith(
        deviceMac: deviceMac,
        patronSn: patronSn,
        cardSn: cardSn,
        type: type,
      );
    });
  }
  
  /// 设备注册
  Future<void> registerDevice() async {
    if (_isDisposed || _isRegisterLoading) return;
    
    // 验证必填字段
    if (_registerData.deviceMac.isEmpty) {
      _showError('设备MAC地址不能为空');
      return;
    }
    
    _safeUpdate(() {
      _isRegisterLoading = true;
      _errorMessage = '';
    });
    
    try {
      final result = await _apiService.registerDevice(_registerData);
      
      if (result.isSuccess) {
        _safeUpdate(() {
          _isRegisterLoading = false;
        });
        _showSuccess(result.message.isNotEmpty ? result.message : '设备注册成功');
      } else {
        _showError(result.message.isNotEmpty ? result.message : '设备注册失败');
      }
    } catch (e) {
      _showError('设备注册失败: ${e.toString()}');
    }
  }
  
  /// 上传设备状态
  Future<void> uploadDeviceStatus() async {
    if (_isDisposed || _isStatusLoading) return;

    // 验证必填字段
    if (_statusData.deviceMac.isEmpty) {
      _showError('设备MAC地址不能为空');
      return;
    }

    _safeUpdate(() {
      _isStatusLoading = true;
      _errorMessage = '';
    });

    try {
      final result = await _apiService.uploadDeviceStatus(_statusData);

      if (result.isSuccess) {
        _safeUpdate(() {
          _isStatusLoading = false;
        });
        _showSuccess(result.message.isNotEmpty ? result.message : '状态上传成功');
      } else {
        _showError(result.message.isNotEmpty ? result.message : '状态上传失败');
      }
    } catch (e) {
      _showError('状态上传失败: ${e.toString()}');
    }
  }

  /// 读者认证
  Future<void> authenticateReader() async {
    if (_isDisposed || _isAuthLoading) return;

    // 验证数据有效性
    if (!_authData.isValid) {
      if (_authData.deviceMac.isEmpty) {
        _showError('设备MAC地址不能为空');
      } else if (_authData.identifier.isEmpty) {
        _showError('读者证号或物理卡号不能为空');
      } else {
        _showError('通行类型必须是进(1)或出(2)');
      }
      return;
    }

    _safeUpdate(() {
      _isAuthLoading = true;
      _errorMessage = '';
    });

    try {
      final result = await _apiService.authenticateReader(_authData);

      if (result.isSuccess) {
        _safeUpdate(() {
          _isAuthLoading = false;
        });
        _showSuccess(result.message.isNotEmpty ? result.message : '认证成功');
      } else {
        _showError(result.message.isNotEmpty ? result.message : '认证失败');
      }
    } catch (e) {
      _showError('认证失败: ${e.toString()}');
    }
  }
  
  /// 测试API连接
  Future<void> testConnection() async {
    if (_isDisposed) return;
    
    _safeUpdate(() {
      _isLoading = true;
      _errorMessage = '';
    });
    
    try {
      final success = await _apiService.testConnection();
      _safeUpdate(() {
        _isLoading = false;
      });
      
      if (success) {
        _showSuccess('API连接测试成功');
      } else {
        _showError('API连接测试失败');
      }
    } catch (e) {
      _showError('连接测试失败: ${e.toString()}');
    }
  }
  
  /// 重置为默认配置
  Future<void> resetToDefault() async {
    if (_isDisposed) return;
    
    try {
      await _configManager.resetToDefault();
      await _loadConfig();
      await _apiService.initialize();
      _showSuccess('已重置为默认配置');
    } catch (e) {
      _showError('重置配置失败: ${e.toString()}');
    }
  }
  
  @override
  void dispose() {
    _isDisposed = true;
    super.dispose();
  }
}
