import 'package:flutter/material.dart';

class RecordModel {
  final int id;
  final String name;
  final String readerCardNo;
  final DateTime openTime;
  final String authMethod;    // 认证方式
  final String status;        // 认证状态
  final String remark;        // 备注

  RecordModel({
    required this.id,
    required this.name,
    required this.readerCardNo,
    required this.openTime,
    required this.authMethod,
    required this.status,
    required this.remark,
  });

  factory RecordModel.fromJson(Map<String, dynamic> json) {
    return RecordModel(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      readerCardNo: json['reader_card_no'] ?? '',
      openTime: json['open_time'] != null
          ? DateTime.parse(json['open_time'])
          : DateTime.now(),
      authMethod: json['auth_method'] ?? '',
      status: json['status'] ?? '',
      remark: json['remark'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'reader_card_no': readerCardNo,
      'open_time': openTime.toIso8601String(),
      'auth_method': authMethod,
      'status': status,
      'remark': remark,
    };
  }
}