2025-08-28 17:44:36.878194: currentXmlPath:C:\Users\<USER>\Desktop\Release\Release\packages\sea_socket\lib\src\protocols
2025-08-28 17:44:36.878194: 61
2025-08-28 17:44:36.882181: ✅ 数据库更新成功: planConfig
2025-08-28 17:44:36.882181: ✅ 数据库更新成功: SysConfig
2025-08-28 17:44:36.882181: ✅ 数据库更新成功: Sip2Config
2025-08-28 17:44:36.882181: ✅ 数据库更新成功: pageConfig
2025-08-28 17:44:36.883178: ✅ 数据库更新成功: readerConfig
2025-08-28 17:44:36.883178: ✅ 数据库更新成功: banZhengConfig
2025-08-28 17:44:36.883178: ✅ 数据库更新成功: printConfig
2025-08-28 17:44:37.095981: 开始初始化闸机协调器...
2025-08-28 17:44:37.096979: ✅ 已清除串口配置缓存，下次访问将重新从数据库读取
2025-08-28 17:44:37.097975: ✅ 已清除 SettingProvider 串口配置缓存
2025-08-28 17:44:37.117908: ✅ 通过 getSerialConfig 获取串口配置: COM1 @ 115200
2025-08-28 17:44:37.117908: ✅ 串口配置加载完成: COM1 @ 115200
2025-08-28 17:44:37.117908: 可用串口: [COM1, COM2, COM3, COM4, COM5, COM6]
2025-08-28 17:44:37.120897: 连接闸机串口: COM1
2025-08-28 17:44:37.121894: 尝试连接串口: COM1, 波特率: 115200
2025-08-28 17:44:37.121894: 串口连接成功: COM1 at 115200 baud
2025-08-28 17:44:37.121894: 开始监听串口数据
2025-08-28 17:44:37.121894: 串口连接状态变化: true
2025-08-28 17:44:37.121894: 闸机串口连接成功
2025-08-28 17:44:37.121894: 串口 COM1 连接成功 (波特率: 115200)
2025-08-28 17:44:37.121894: 闸机串口服务初始化成功: COM1
2025-08-28 17:44:37.122891: 开始监听串口数据（通过 GateSerialManager 事件流）
2025-08-28 17:44:37.122891: 开始监听闸机串口命令
2025-08-28 17:44:37.122891: 开始初始化RFID服务和共享池...
2025-08-28 17:44:37.122891: 开始初始化增强RFID服务...
2025-08-28 17:44:37.122891: 开始初始化增强RFID服务...
2025-08-28 17:44:37.122891: SIP2图书信息服务初始化完成
2025-08-28 17:44:37.122891: 增强RFID服务初始化完成，配置了1个阅读器
2025-08-28 17:44:37.123888: 📋 从数据库读取主从机配置: channel_1
2025-08-28 17:44:37.123888: 📋 配置详情: 主机模式
2025-08-28 17:44:37.123888: 🚀 主机模式：启动RFID硬件持续扫描
2025-08-28 17:44:37.123888: 启动RFID持续扫描...
2025-08-28 17:44:37.123888: changeReaders
2025-08-28 17:44:37.123888: createIsolate isOpen:false,isOpening:false
2025-08-28 17:44:37.124884: createIsolate newport null
2025-08-28 17:44:37.146812: socket 连接成功,isBroadcast:false
2025-08-28 17:44:37.147809: changeSocketStatus:true
2025-08-28 17:44:37.147809: Sip2HeartBeatManager start loginACS:false askACS:false
2025-08-28 17:44:37.147809: Req msgType：Sip2MsgType.login ,length:72， ret:  9300CNhlsp_sip2|COsip2|CP3a40852d-49fd-4df2-a1f9-6e2641a6e91f|AY1AZEC16
2025-08-28 17:44:37.244490: Rsp : 941AY1AZFDFC
2025-08-28 17:44:37.263428: loginRsp:{OK: 1, MsgSeqId: 1AZFDFC}
2025-08-28 17:44:37.264427: Sip2HeartBeatManager start loginACS:false askACS:true
2025-08-28 17:44:37.264427: 发送心跳
2025-08-28 17:44:37.265421: Req msgType：Sip2MsgType.scStatus ,length:20， ret:  9900522.00AY2AZFC9F
2025-08-28 17:44:37.385029: Rsp : 98YYYNNN00500320250828    1744282.00AOhlsp|AM海恒图书馆|BXYYYYYYYYYYYYYYYY|AN3a40852d-49fd-4df2-a1f9-6e2641a6e91f|AF|AG|AY2AZD515
2025-08-28 17:44:37.626228: 找到网口配置: LSGate图书馆安全门RFID阅读器 - **************:6012
2025-08-28 17:44:37.627228: 使用网口连接: **************:6012
2025-08-28 17:44:37.627228: open():SendPort
2025-08-28 17:44:37.628224: untilDetcted():SendPort
2025-08-28 17:44:37.628224: 网口连接成功: **************:6012
2025-08-28 17:44:37.629219: startInventory():SendPort
2025-08-28 17:44:37.629219: RFID硬件扫描已启动，阅读器开始持续工作
2025-08-28 17:44:37.629219: RFID持续扫描启动完成
2025-08-28 17:44:37.630216: 增强RFID服务初始化完成，持续扫描已启动
2025-08-28 17:44:37.631213: 📋 从数据库读取主从机配置: channel_1
2025-08-28 17:44:37.631213: 📋 配置详情: 主机模式
2025-08-28 17:44:37.631213: 🚀 主机模式：启动持续数据收集，数据将持续进入共享池
2025-08-28 17:44:37.632210: 🎯 关键修复：使用轮询机制确保标签持续被发现
2025-08-28 17:44:37.632210: 🧹 清空RFID缓冲区（保持tagList），确保数据收集正常工作...
2025-08-28 17:44:37.632210: 清空RFID扫描缓冲区...
2025-08-28 17:44:37.632210: 🧹 已清空HWTagProvider: 0 -> 0个标签
2025-08-28 17:44:37.632210: 🔧 主机模式：清空软件缓冲区，保持硬件持续扫描
2025-08-28 17:44:37.632210: 🔄 开始重置已处理条码集合...
2025-08-28 17:44:37.633205: 📊 重置前状态: 大小=0, 内容=[]
2025-08-28 17:44:37.633205: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-28 17:44:37.633205: 🔄 当前场上标签将被重新识别为新标签
2025-08-28 17:44:37.633205: 📊 当前tagList状态: 0个标签
2025-08-28 17:44:37.633205: ⚠️ 当前未在扫描状态，跳过立即轮询
2025-08-28 17:44:37.634202: ✅ 软件缓冲区已清空，硬件扫描保持运行
2025-08-28 17:44:37.634202: 🚀 启动增强数据收集（事件监听 + 轮询备用）...
2025-08-28 17:44:37.634202: 🚀 开始RFID数据收集...
2025-08-28 17:44:37.634202: 📋 扫描结果和缓存已清空
2025-08-28 17:44:37.634202: 清空RFID扫描缓冲区...
2025-08-28 17:44:37.635198: 🧹 已清空HWTagProvider: 0 -> 0个标签
2025-08-28 17:44:37.635198: 🔧 主机模式：清空软件缓冲区，保持硬件持续扫描
2025-08-28 17:44:37.635198: 🔄 开始重置已处理条码集合...
2025-08-28 17:44:37.635198: 📊 重置前状态: 大小=0, 内容=[]
2025-08-28 17:44:37.635198: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-28 17:44:37.635198: 🔄 当前场上标签将被重新识别为新标签
2025-08-28 17:44:37.635198: 📊 当前tagList状态: 0个标签
2025-08-28 17:44:37.635198: ⚠️ 当前未在扫描状态，跳过立即轮询
2025-08-28 17:44:37.636197: ✅ 软件缓冲区已清空，硬件扫描保持运行
2025-08-28 17:44:37.636197: 🎯 启动数据监听和轮询机制...
2025-08-28 17:44:37.636197: 🚀 标签轮询机制已启动 (每500ms轮询一次)
2025-08-28 17:44:37.636197: ✅ 标签监听改为仅轮询机制（每500ms轮询tagList）
2025-08-28 17:44:37.636197: 📊 当前HWTagProvider状态:
2025-08-28 17:44:37.636197:   - tagList: 0个标签
2025-08-28 17:44:37.636197:   - type: null
2025-08-28 17:44:37.637191: 🎯 标签数据获取已统一为轮询机制
2025-08-28 17:44:37.637191: ✅ RFID数据收集已启动，轮询机制运行中
2025-08-28 17:44:37.637191: 📊 当前tagList状态: 0个标签
2025-08-28 17:44:37.637191: subThread :ReaderCommand.readerList
2025-08-28 17:44:37.637191: commandRsp:ReaderCommand.readerList
2025-08-28 17:44:37.637191: readerList：1,readerSetting：1
2025-08-28 17:44:37.637191: cacheUsedReaders:1
2025-08-28 17:44:37.637191: subThread :ReaderCommand.open
2025-08-28 17:44:37.638187: commandRsp:ReaderCommand.open
2025-08-28 17:44:37.638187: LSGate使用网络连接 IP: **************, Port: 6012, DeviceType: LSGControlCenter
2025-08-28 17:44:37.638187: LSGate device opened successfully, handle: 2402617358784
2025-08-28 17:44:37.639184: open reader readerType ：22 ret：0
2025-08-28 17:44:37.639184: [[22, 0]]
2025-08-28 17:44:37.639184: changeType:ReaderErrorType.openSuccess
2025-08-28 17:44:37.640187: subThread :ReaderCommand.untilDetected
2025-08-28 17:44:37.640187: commandRsp:ReaderCommand.untilDetected
2025-08-28 17:44:37.640187: subThread :ReaderCommand.startInventory
2025-08-28 17:44:37.641178: commandRsp:ReaderCommand.startInventory
2025-08-28 17:44:37.736862: 🔄 执行首次轮询...
2025-08-28 17:44:37.736862: 🔄 开始RFID轮询检查...
2025-08-28 17:44:37.737859: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:44:37.737859: ⚠️ tagList为空，场上无标签
2025-08-28 17:44:38.137537: 🔄 开始RFID轮询检查...
2025-08-28 17:44:38.138535: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:44:38.138535: ⚠️ tagList为空，场上无标签
2025-08-28 17:44:38.243189: 🔍 LSGate硬件扫描详情:
2025-08-28 17:44:38.244185:   - 设备句柄: 2402617358784
2025-08-28 17:44:38.244185:   - FetchRecords返回值: 0
2025-08-28 17:44:38.245181:   - 报告数量: 0
2025-08-28 17:44:38.245181: 📊 LSGate扫描结果汇总:
2025-08-28 17:44:38.246178:   - 发现标签数量: 0
2025-08-28 17:44:38.246178:   - 未发现任何RFID标签
2025-08-28 17:44:38.246178: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:44:38.247174: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:44:38.638880: 🔍 验证数据收集状态...
2025-08-28 17:44:38.639877: 📊 当前tagList: 0个标签
2025-08-28 17:44:38.639877: ⚠️ tagList为空，等待RFID硬件扫描到标签
2025-08-28 17:44:38.640874: ✅ 主机持续数据收集已启动，共享池将持续接收RFID数据
2025-08-28 17:44:38.640874: 🔄 轮询机制每500ms检查一次tagList，确保标签不会丢失
2025-08-28 17:44:38.640874: 开始全局初始化共享扫描池服务...
2025-08-28 17:44:38.641871: 共享扫描池已集成现有RFID服务
2025-08-28 17:44:38.641871: 📡 初始化后RFID扫描状态: scanning=false
2025-08-28 17:44:38.641871: 🔄 开始重置已处理条码集合...
2025-08-28 17:44:38.642867: 📊 重置前状态: 大小=0, 内容=[]
2025-08-28 17:44:38.642867: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-28 17:44:38.642867: 🔄 当前场上标签将被重新识别为新标签
2025-08-28 17:44:38.643863: 📊 当前tagList状态: 0个标签
2025-08-28 17:44:38.643863: 🚀 立即触发轮询，加速标签重新发现...
2025-08-28 17:44:38.643863: 🔄 开始RFID轮询检查...
2025-08-28 17:44:38.643863: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:44:38.644859: ⚠️ tagList为空，场上无标签
2025-08-28 17:44:38.644859: 🔄 开始RFID轮询检查...
2025-08-28 17:44:38.644859: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:44:38.644859: ⚠️ tagList为空，场上无标签
2025-08-28 17:44:38.645856: 共享扫描池服务全局初始化完成
2025-08-28 17:44:38.645856: 🚀 初始化新架构服务...
2025-08-28 17:44:38.645856: 🖥️ 主机模式：使用主机集合A服务
2025-08-28 17:44:38.645856: ⏹️ 书籍信息查询服务停止监听
2025-08-28 17:44:38.645856: 🚀 书籍信息查询服务开始监听集合A变化
2025-08-28 17:44:38.646852: ✅ 新架构服务初始化完成
2025-08-28 17:44:38.646852: RFID服务和共享池初始化完成，持续扫描已启动
2025-08-28 17:44:38.646852: 闸机协调器初始化完成
2025-08-28 17:44:38.646852: 🔧 开始初始化主从机扩展（使用持久化配置）...
2025-08-28 17:44:38.646852: 开始初始化主从机扩展...
2025-08-28 17:44:38.646852: 从 seasetting 数据库加载主从机配置成功: channel_1
2025-08-28 17:44:38.647849: 配置详情: 主机模式
2025-08-28 17:44:38.647849: 📡 从 SettingProvider 获取串口配置: COM1 @ 115200
2025-08-28 17:44:38.647849: ✅ 通过 SettingProvider 加载串口配置成功
2025-08-28 17:44:38.647849: 启用主从机扩展: channel_1 (主机)
2025-08-28 17:44:38.648846: ✅ 数据变化通知流已创建
2025-08-28 17:44:38.648846: 共享扫描池已集成现有RFID服务
2025-08-28 17:44:38.648846: 📡 初始化后RFID扫描状态: scanning=false
2025-08-28 17:44:38.648846: 🔄 开始重置已处理条码集合...
2025-08-28 17:44:38.648846: 📊 重置前状态: 大小=0, 内容=[]
2025-08-28 17:44:38.648846: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-28 17:44:38.648846: 🔄 当前场上标签将被重新识别为新标签
2025-08-28 17:44:38.648846: 📊 当前tagList状态: 0个标签
2025-08-28 17:44:38.649842: 🚀 立即触发轮询，加速标签重新发现...
2025-08-28 17:44:38.649842: 🔄 开始RFID轮询检查...
2025-08-28 17:44:38.649842: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:44:38.649842: ⚠️ tagList为空，场上无标签
2025-08-28 17:44:38.649842: 配置为主机模式，监听端口: 8888
2025-08-28 17:44:38.650839: 主机服务器启动成功，监听端口: 8888
2025-08-28 17:44:38.650839: 主机模式配置完成（请求-响应模式）
2025-08-28 17:44:38.650839: [channel_1] 已集成现有GateCoordinator，开始监听事件
2025-08-28 17:44:38.650839: 主从机扩展启用成功
2025-08-28 17:44:38.650839: 主从机扩展初始化完成
2025-08-28 17:44:38.650839: 配置信息: MasterSlaveConfig(channelId: channel_1, isMaster: true, slaveAddress: null, masterAddress: null, port: 8888)
2025-08-28 17:44:38.651837: ✅ 加载到持久化配置: 主机模式, 通道: channel_1
2025-08-28 17:44:38.651837: 主从机扩展初始化完成
2025-08-28 17:44:38.651837: 安全闸机系统初始化完成
2025-08-28 17:44:38.651837: 🔍 LSGate硬件扫描详情:
2025-08-28 17:44:38.651837:   - 设备句柄: 2402617358784
2025-08-28 17:44:38.651837:   - FetchRecords返回值: 0
2025-08-28 17:44:38.651837:   - 报告数量: 0
2025-08-28 17:44:38.652833: 📊 LSGate扫描结果汇总:
2025-08-28 17:44:38.652833:   - 发现标签数量: 0
2025-08-28 17:44:38.652833:   - 未发现任何RFID标签
2025-08-28 17:44:38.652833: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:44:38.652833: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:44:38.660806: 开始初始化MultiAuthManager...
2025-08-28 17:44:38.661803: 多认证管理器状态变更: initializing
2025-08-28 17:44:38.661803: 认证优先级管理器: 开始加载认证方式
2025-08-28 17:44:38.661803: 配置的排序: [读者证认证, 身份证认证, 腾讯E证通认证, 手动输入认证, 人脸识别认证, 社保卡认证, 市民卡认证, 微信二维码认证, 借阅宝认证, 支付宝二维码认证, 芝麻信用码认证, 支付宝二维码认证（阿里信用）, 微信/支付宝认证, 二维码读者认证, IMI身份认证, 电子社保卡认证, 拍照配置, 上海随申码认证, 微信扫码认证, 汇文二维码]
2025-08-28 17:44:38.661803: 可用的认证方式: [读者证认证, 身份证认证, 腾讯E证通认证, 手动输入认证, 人脸识别认证, 社保卡认证, 市民卡认证, 微信二维码认证, 借阅宝认证, 支付宝二维码认证, 芝麻信用码认证, 支付宝二维码认证（阿里信用）, 微信/支付宝认证, 二维码读者认证, IMI身份认证, 电子社保卡认证, 拍照配置, 上海随申码认证, 微信扫码认证, 汇文二维码]
2025-08-28 17:44:38.661803: 认证优先级管理器: 按配置顺序添加 读者证认证 -> 读者证
2025-08-28 17:44:38.661803: 认证优先级管理器: 最终排序结果: 读者证
2025-08-28 17:44:38.662800: 认证优先级管理器: 主要认证方式: 读者证
2025-08-28 17:44:38.662800: 多认证管理器: 从优先级管理器加载的认证方式: 读者证
2025-08-28 17:44:38.662800: 多认证管理器: 当前默认显示方式: 读者证
2025-08-28 17:44:38.662800: 初始化读卡器认证服务
2025-08-28 17:44:38.662800: 读卡器认证服务初始化成功
2025-08-28 17:44:38.662800: 初始化共享读卡器认证服务
2025-08-28 17:44:38.663797: 读者证 认证服务初始化成功
2025-08-28 17:44:38.663797: 认证服务初始化完成，共初始化 1 种认证方式
2025-08-28 17:44:38.663797: 多认证管理器状态变更: idle
2025-08-28 17:44:38.663797: 多认证管理器初始化完成，启用的认证方式: [AuthMethod.readerCard]
2025-08-28 17:44:38.663797: MultiAuthManager初始化完成
2025-08-28 17:44:38.663797: 开始初始化SilencePageViewModel...
2025-08-28 17:44:38.663797: 闸机串口服务已经初始化
2025-08-28 17:44:38.663797: 开始初始化闸机认证服务...
2025-08-28 17:44:38.664794: 闸机认证服务初始化完成，启用认证方式: 人脸识别、读者证、AuthMethod.wechatScanQRCode
2025-08-28 17:44:38.664794: RFID服务已经初始化
2025-08-28 17:44:38.664794: SIP2图书信息服务初始化完成
2025-08-28 17:44:38.664794: 💡 主从机扩展已准备就绪，请通过配置页面手动启用
2025-08-28 17:44:38.664794: 💡 可以通过MasterSlaveConfigPage进行配置
2025-08-28 17:44:38.664794: ✅ 统一事件监听已设置：SilencePageViewModel → GateCoordinator.eventStream
2025-08-28 17:44:38.664794: 串口监听已经启动
2025-08-28 17:44:38.664794: SilencePageViewModel初始化完成
2025-08-28 17:44:39.006207: dispose IndexPage
2025-08-28 17:44:39.007205: IndexPage dispose
2025-08-28 17:44:39.137773: 🔄 开始RFID轮询检查...
2025-08-28 17:44:39.137773: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:44:39.138770: ⚠️ tagList为空，场上无标签
2025-08-28 17:44:39.138770: 🔍 LSGate硬件扫描详情:
2025-08-28 17:44:39.138770:   - 设备句柄: 2402617358784
2025-08-28 17:44:39.139767:   - FetchRecords返回值: 0
2025-08-28 17:44:39.139767:   - 报告数量: 0
2025-08-28 17:44:39.139767: 📊 LSGate扫描结果汇总:
2025-08-28 17:44:39.140763:   - 发现标签数量: 0
2025-08-28 17:44:39.140763:   - 未发现任何RFID标签
2025-08-28 17:44:39.140763: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:44:39.140763: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:44:39.166677: 🔍 主从机模式检测: 启用=true, 主机模式=true
2025-08-28 17:44:39.166677: 🔍 扩展详细状态: {enabled: true, channel_id: channel_1, is_master: true, data_stream_ready: true, data_stream_exists: true, data_stream_closed: false, shared_pool_size: 0, queue_size: 0, comm_connected: false, timestamp: 2025-08-28T17:44:39.166677}
2025-08-28 17:44:39.166677: 🎯 检测到主机模式，无需设置额外监听
2025-08-28 17:44:39.638118: 🔄 开始RFID轮询检查...
2025-08-28 17:44:39.638118: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:44:39.638118: ⚠️ tagList为空，场上无标签
2025-08-28 17:44:39.639116: 🔍 LSGate硬件扫描详情:
2025-08-28 17:44:39.640113:   - 设备句柄: 2402617358784
2025-08-28 17:44:39.640113:   - FetchRecords返回值: 0
2025-08-28 17:44:39.640113:   - 报告数量: 0
2025-08-28 17:44:39.640113: 📊 LSGate扫描结果汇总:
2025-08-28 17:44:39.640113:   - 发现标签数量: 0
2025-08-28 17:44:39.641108:   - 未发现任何RFID标签
2025-08-28 17:44:39.641108: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:44:39.641108: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:44:40.137468: 🔄 开始RFID轮询检查...
2025-08-28 17:44:40.137468: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:44:40.138465: ⚠️ tagList为空，场上无标签
2025-08-28 17:44:40.140458: 🔍 LSGate硬件扫描详情:
2025-08-28 17:44:40.140458:   - 设备句柄: 2402617358784
2025-08-28 17:44:40.141457:   - FetchRecords返回值: 0
2025-08-28 17:44:40.141457:   - 报告数量: 0
2025-08-28 17:44:40.141457: 📊 LSGate扫描结果汇总:
2025-08-28 17:44:40.141457:   - 发现标签数量: 0
2025-08-28 17:44:40.141457:   - 未发现任何RFID标签
2025-08-28 17:44:40.141457: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:44:40.142451: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:44:40.637814: 🔄 开始RFID轮询检查...
2025-08-28 17:44:40.638812: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:44:40.638812: ⚠️ tagList为空，场上无标签
2025-08-28 17:44:40.638812: 🔍 LSGate硬件扫描详情:
2025-08-28 17:44:40.639807:   - 设备句柄: 2402617358784
2025-08-28 17:44:40.639807:   - FetchRecords返回值: 0
2025-08-28 17:44:40.640804:   - 报告数量: 0
2025-08-28 17:44:40.640804: 📊 LSGate扫描结果汇总:
2025-08-28 17:44:40.641801:   - 发现标签数量: 0
2025-08-28 17:44:40.641801:   - 未发现任何RFID标签
2025-08-28 17:44:40.642798: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:44:40.642798: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:44:41.138159: 🔄 开始RFID轮询检查...
2025-08-28 17:44:41.138159: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:44:41.139156: ⚠️ tagList为空，场上无标签
2025-08-28 17:44:41.139156: 🔍 LSGate硬件扫描详情:
2025-08-28 17:44:41.140153:   - 设备句柄: 2402617358784
2025-08-28 17:44:41.140153:   - FetchRecords返回值: 0
2025-08-28 17:44:41.140153:   - 报告数量: 0
2025-08-28 17:44:41.141149: 📊 LSGate扫描结果汇总:
2025-08-28 17:44:41.141149:   - 发现标签数量: 0
2025-08-28 17:44:41.141149:   - 未发现任何RFID标签
2025-08-28 17:44:41.142146: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:44:41.142146: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:44:41.637508: 🔄 开始RFID轮询检查...
2025-08-28 17:44:41.638507: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:44:41.639507: ⚠️ tagList为空，场上无标签
2025-08-28 17:44:41.639507: 🔍 LSGate硬件扫描详情:
2025-08-28 17:44:41.640498:   - 设备句柄: 2402617358784
2025-08-28 17:44:41.640498:   - FetchRecords返回值: 0
2025-08-28 17:44:41.640498:   - 报告数量: 0
2025-08-28 17:44:41.641495: 📊 LSGate扫描结果汇总:
2025-08-28 17:44:41.641495:   - 发现标签数量: 0
2025-08-28 17:44:41.641495:   - 未发现任何RFID标签
2025-08-28 17:44:41.642492: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:44:41.642492: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:44:42.138392: 🔄 开始RFID轮询检查...
2025-08-28 17:44:42.138392: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:44:42.139390: ⚠️ tagList为空，场上无标签
2025-08-28 17:44:42.139390: 🔍 LSGate硬件扫描详情:
2025-08-28 17:44:42.140387:   - 设备句柄: 2402617358784
2025-08-28 17:44:42.140387:   - FetchRecords返回值: 0
2025-08-28 17:44:42.140387:   - 报告数量: 0
2025-08-28 17:44:42.141383: 📊 LSGate扫描结果汇总:
2025-08-28 17:44:42.141383:   - 发现标签数量: 0
2025-08-28 17:44:42.141383:   - 未发现任何RFID标签
2025-08-28 17:44:42.142380: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:44:42.142380: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:44:42.637741: 🔄 开始RFID轮询检查...
2025-08-28 17:44:42.637741: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:44:42.638740: ⚠️ tagList为空，场上无标签
2025-08-28 17:44:42.638740: 🔍 LSGate硬件扫描详情:
2025-08-28 17:44:42.639735:   - 设备句柄: 2402617358784
2025-08-28 17:44:42.639735:   - FetchRecords返回值: 0
2025-08-28 17:44:42.639735:   - 报告数量: 0
2025-08-28 17:44:42.640733: 📊 LSGate扫描结果汇总:
2025-08-28 17:44:42.640733:   - 发现标签数量: 0
2025-08-28 17:44:42.640733:   - 未发现任何RFID标签
2025-08-28 17:44:42.641730: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:44:42.641730: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:44:43.138087: 🔄 开始RFID轮询检查...
2025-08-28 17:44:43.138087: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:44:43.139084: ⚠️ tagList为空，场上无标签
2025-08-28 17:44:43.139084: 🔍 LSGate硬件扫描详情:
2025-08-28 17:44:43.139084:   - 设备句柄: 2402617358784
2025-08-28 17:44:43.140081:   - FetchRecords返回值: 0
2025-08-28 17:44:43.140081:   - 报告数量: 0
2025-08-28 17:44:43.140081: 📊 LSGate扫描结果汇总:
2025-08-28 17:44:43.141077:   - 发现标签数量: 0
2025-08-28 17:44:43.141077:   - 未发现任何RFID标签
2025-08-28 17:44:43.141077: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:44:43.142073: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:44:43.637436: 🔄 开始RFID轮询检查...
2025-08-28 17:44:43.637436: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:44:43.638433: ⚠️ tagList为空，场上无标签
2025-08-28 17:44:43.640426: 🔍 LSGate硬件扫描详情:
2025-08-28 17:44:43.640426:   - 设备句柄: 2402617358784
2025-08-28 17:44:43.641423:   - FetchRecords返回值: 0
2025-08-28 17:44:43.641423:   - 报告数量: 0
2025-08-28 17:44:43.641423: 📊 LSGate扫描结果汇总:
2025-08-28 17:44:43.642419:   - 发现标签数量: 0
2025-08-28 17:44:43.642419:   - 未发现任何RFID标签
2025-08-28 17:44:43.642419: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:44:43.643416: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:44:44.138289: 🔄 开始RFID轮询检查...
2025-08-28 17:44:44.138289: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:44:44.139286: ⚠️ tagList为空，场上无标签
2025-08-28 17:44:44.139286: 🔍 LSGate硬件扫描详情:
2025-08-28 17:44:44.139286:   - 设备句柄: 2402617358784
2025-08-28 17:44:44.140283:   - FetchRecords返回值: 0
2025-08-28 17:44:44.140283:   - 报告数量: 0
2025-08-28 17:44:44.140283: 📊 LSGate扫描结果汇总:
2025-08-28 17:44:44.141280:   - 发现标签数量: 0
2025-08-28 17:44:44.141280:   - 未发现任何RFID标签
2025-08-28 17:44:44.141280: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:44:44.142276: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:44:44.637638: 🔄 开始RFID轮询检查...
2025-08-28 17:44:44.637638: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:44:44.638635: ⚠️ tagList为空，场上无标签
2025-08-28 17:44:44.638635: 🔍 LSGate硬件扫描详情:
2025-08-28 17:44:44.638635:   - 设备句柄: 2402617358784
2025-08-28 17:44:44.639632:   - FetchRecords返回值: 0
2025-08-28 17:44:44.639632:   - 报告数量: 0
2025-08-28 17:44:44.639632: 📊 LSGate扫描结果汇总:
2025-08-28 17:44:44.640629:   - 发现标签数量: 0
2025-08-28 17:44:44.640629:   - 未发现任何RFID标签
2025-08-28 17:44:44.640629: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:44:44.641625: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:44:45.138140: 🔄 开始RFID轮询检查...
2025-08-28 17:44:45.138140: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:44:45.139137: ⚠️ tagList为空，场上无标签
2025-08-28 17:44:45.139137: 🔍 LSGate硬件扫描详情:
2025-08-28 17:44:45.139137:   - 设备句柄: 2402617358784
2025-08-28 17:44:45.140133:   - FetchRecords返回值: 0
2025-08-28 17:44:45.140133:   - 报告数量: 0
2025-08-28 17:44:45.140133: 📊 LSGate扫描结果汇总:
2025-08-28 17:44:45.141130:   - 发现标签数量: 0
2025-08-28 17:44:45.141130:   - 未发现任何RFID标签
2025-08-28 17:44:45.141130: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:44:45.142126: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:44:45.637489: 🔄 开始RFID轮询检查...
2025-08-28 17:44:45.637489: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:44:45.638486: ⚠️ tagList为空，场上无标签
2025-08-28 17:44:45.640479: 🔍 LSGate硬件扫描详情:
2025-08-28 17:44:45.640479:   - 设备句柄: 2402617358784
2025-08-28 17:44:45.641475:   - FetchRecords返回值: 0
2025-08-28 17:44:45.641475:   - 报告数量: 0
2025-08-28 17:44:45.641475: 📊 LSGate扫描结果汇总:
2025-08-28 17:44:45.642472:   - 发现标签数量: 0
2025-08-28 17:44:45.642472:   - 未发现任何RFID标签
2025-08-28 17:44:45.642472: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:44:45.643469: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:44:46.138343: 🔄 开始RFID轮询检查...
2025-08-28 17:44:46.138343: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:44:46.139340: ⚠️ tagList为空，场上无标签
2025-08-28 17:44:46.139340: 🔍 LSGate硬件扫描详情:
2025-08-28 17:44:46.139340:   - 设备句柄: 2402617358784
2025-08-28 17:44:46.140336:   - FetchRecords返回值: 0
2025-08-28 17:44:46.140336:   - 报告数量: 0
2025-08-28 17:44:46.140336: 📊 LSGate扫描结果汇总:
2025-08-28 17:44:46.141333:   - 发现标签数量: 0
2025-08-28 17:44:46.141333:   - 未发现任何RFID标签
2025-08-28 17:44:46.141333: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:44:46.142330: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:44:46.508121: 接收到数据: aa 00 c8 80 00 00 27 82
2025-08-28 17:44:46.508121: 🔍 接收到串口数据: aa 00 c8 80 00 00 27 82
2025-08-28 17:44:46.509117: 🔍 数据长度: 8 字节
2025-08-28 17:44:46.509117: 🔍 预定义命令列表:
2025-08-28 17:44:46.509117:   GateCommand.enterStart: aa 00 64 80 00 00 06 d2
2025-08-28 17:44:46.509117:   GateCommand.enterEnd: aa 00 65 80 00 00 07 2e
2025-08-28 17:44:46.510114:   GateCommand.exitStart: aa 00 c8 80 00 00 27 82
2025-08-28 17:44:46.510114:   GateCommand.exitEnd: aa 00 c9 80 00 00 26 7e
2025-08-28 17:44:46.510114:   GateCommand.reachPosition: aa 00 0a 80 00 00
2025-08-28 17:44:46.510114:   GateCommand.enterOpen: aa 00 02 01 00 00 48 72
2025-08-28 17:44:46.511110:   GateCommand.exitOpen: aa 00 01 01 00 00 48 36
2025-08-28 17:44:46.511110:   GateCommand.failSignal: aa 00 02 01 00 00 48 72
2025-08-28 17:44:46.511110:   GateCommand.tailgating: aa 00 0f 80 00 00
2025-08-28 17:44:46.511110:   GateCommand.doorHasPerson: aa 00 0b 80 00 00
2025-08-28 17:44:46.511110: ✅ 解析到闸机命令: GateCommand.exitStart
2025-08-28 17:44:46.512107: 解析到闸机命令: exit_start (出馆开始)
2025-08-28 17:44:46.512107: 收到闸机命令: exit_start (出馆开始)
2025-08-28 17:44:46.512107: 🚪 收到出馆开始命令，等待出馆到位信号...
2025-08-28 17:44:46.512107: 闸机状态变更: GateState.idle -> GateState.exitStarted
2025-08-28 17:44:46.512107: 闸机状态更新: GateState.idle -> GateState.exitStarted
2025-08-28 17:44:46.512107: 📊 流程状态：出馆流程已开始，等待到位信号
2025-08-28 17:44:46.513103: [channel_1] 收到闸机事件: state_changed
2025-08-28 17:44:46.513103: 📨 收到GateCoordinator事件: state_changed
2025-08-28 17:44:46.513103: 闸机状态变更: GateState.exitStarted
2025-08-28 17:44:46.513103: 🎨 处理状态变更UI: exitStarted
2025-08-28 17:44:46.513103: 未处理的状态变更UI: exitStarted
2025-08-28 17:44:46.513103: [channel_1] 收到闸机事件: exit_start
2025-08-28 17:44:46.513103: [channel_1] 主从机扩展：处理出馆开始（等待到位信号）
2025-08-28 17:44:46.513103: 🖥️ [channel_1] 主机模式：等待到位信号
2025-08-28 17:44:46.514100: ⏳ [channel_1] 出馆开始完成，等待到位信号...
2025-08-28 17:44:46.514100: 📨 收到GateCoordinator事件: exit_start
2025-08-28 17:44:46.514100: 页面状态变更: SilencePageState.waitingExit
2025-08-28 17:44:46.637691: 🔄 开始RFID轮询检查...
2025-08-28 17:44:46.637691: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:44:46.637691: ⚠️ tagList为空，场上无标签
2025-08-28 17:44:46.640681: 🔍 LSGate硬件扫描详情:
2025-08-28 17:44:46.641680:   - 设备句柄: 2402617358784
2025-08-28 17:44:46.641680:   - FetchRecords返回值: 0
2025-08-28 17:44:46.642677:   - 报告数量: 0
2025-08-28 17:44:46.642677: 📊 LSGate扫描结果汇总:
2025-08-28 17:44:46.642677:   - 发现标签数量: 0
2025-08-28 17:44:46.642677:   - 未发现任何RFID标签
2025-08-28 17:44:46.642677: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:44:46.643672: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:44:47.138037: 🔄 开始RFID轮询检查...
2025-08-28 17:44:47.138037: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:44:47.138037: ⚠️ tagList为空，场上无标签
2025-08-28 17:44:47.139033: 🔍 LSGate硬件扫描详情:
2025-08-28 17:44:47.139033:   - 设备句柄: 2402617358784
2025-08-28 17:44:47.139033:   - FetchRecords返回值: 0
2025-08-28 17:44:47.139033:   - 报告数量: 0
2025-08-28 17:44:47.140030: 📊 LSGate扫描结果汇总:
2025-08-28 17:44:47.140030:   - 发现标签数量: 0
2025-08-28 17:44:47.140030:   - 未发现任何RFID标签
2025-08-28 17:44:47.140030: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:44:47.140030: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:44:47.638382: 🔄 开始RFID轮询检查...
2025-08-28 17:44:47.638382: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:44:47.639380: ⚠️ tagList为空，场上无标签
2025-08-28 17:44:47.639380: 🔍 LSGate硬件扫描详情:
2025-08-28 17:44:47.639380:   - 设备句柄: 2402617358784
2025-08-28 17:44:47.639380:   - FetchRecords返回值: 0
2025-08-28 17:44:47.640376:   - 报告数量: 0
2025-08-28 17:44:47.640376: 📊 LSGate扫描结果汇总:
2025-08-28 17:44:47.640376:   - 发现标签数量: 0
2025-08-28 17:44:47.640376:   - 未发现任何RFID标签
2025-08-28 17:44:47.640376: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:44:47.640376: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:44:48.137366: 🔄 开始RFID轮询检查...
2025-08-28 17:44:48.137366: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:44:48.137366: ⚠️ tagList为空，场上无标签
2025-08-28 17:44:48.140357: 🔍 LSGate硬件扫描详情:
2025-08-28 17:44:48.141353:   - 设备句柄: 2402617358784
2025-08-28 17:44:48.141353:   - FetchRecords返回值: 0
2025-08-28 17:44:48.141353:   - 报告数量: 0
2025-08-28 17:44:48.141353: 📊 LSGate扫描结果汇总:
2025-08-28 17:44:48.141353:   - 发现标签数量: 0
2025-08-28 17:44:48.141353:   - 未发现任何RFID标签
2025-08-28 17:44:48.142350: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:44:48.142350: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:44:48.638109: 🔄 开始RFID轮询检查...
2025-08-28 17:44:48.638109: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:44:48.638109: ⚠️ tagList为空，场上无标签
2025-08-28 17:44:48.639106: 🔍 LSGate硬件扫描详情:
2025-08-28 17:44:48.639106:   - 设备句柄: 2402617358784
2025-08-28 17:44:48.639106:   - FetchRecords返回值: 0
2025-08-28 17:44:48.639106:   - 报告数量: 0
2025-08-28 17:44:48.639106: 📊 LSGate扫描结果汇总:
2025-08-28 17:44:48.639106:   - 发现标签数量: 0
2025-08-28 17:44:48.640103:   - 未发现任何RFID标签
2025-08-28 17:44:48.640103: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:44:48.640103: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:44:49.137962: 🔄 开始RFID轮询检查...
2025-08-28 17:44:49.137962: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:44:49.137962: ⚠️ tagList为空，场上无标签
2025-08-28 17:44:49.138959: 🔍 LSGate硬件扫描详情:
2025-08-28 17:44:49.138959:   - 设备句柄: 2402617358784
2025-08-28 17:44:49.138959:   - FetchRecords返回值: 0
2025-08-28 17:44:49.138959:   - 报告数量: 0
2025-08-28 17:44:49.139955: 📊 LSGate扫描结果汇总:
2025-08-28 17:44:49.139955:   - 发现标签数量: 0
2025-08-28 17:44:49.139955:   - 未发现任何RFID标签
2025-08-28 17:44:49.139955: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:44:49.139955: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:44:49.638308: 🔄 开始RFID轮询检查...
2025-08-28 17:44:49.638308: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:44:49.639307: ⚠️ tagList为空，场上无标签
2025-08-28 17:44:49.639307: 🔍 LSGate硬件扫描详情:
2025-08-28 17:44:49.639307:   - 设备句柄: 2402617358784
2025-08-28 17:44:49.639307:   - FetchRecords返回值: 0
2025-08-28 17:44:49.640301:   - 报告数量: 0
2025-08-28 17:44:49.640301: 📊 LSGate扫描结果汇总:
2025-08-28 17:44:49.640301:   - 发现标签数量: 0
2025-08-28 17:44:49.640301:   - 未发现任何RFID标签
2025-08-28 17:44:49.640301: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:44:49.640301: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:44:49.927354: 接收到数据: aa 00 0a 80 00 00 1a 3a
2025-08-28 17:44:49.927354: 🔍 接收到串口数据: aa 00 0a 80 00 00 1a 3a
2025-08-28 17:44:49.927354: 🔍 数据长度: 8 字节
2025-08-28 17:44:49.928349: 🔍 预定义命令列表:
2025-08-28 17:44:49.928349:   GateCommand.enterStart: aa 00 64 80 00 00 06 d2
2025-08-28 17:44:49.928349:   GateCommand.enterEnd: aa 00 65 80 00 00 07 2e
2025-08-28 17:44:49.928349:   GateCommand.exitStart: aa 00 c8 80 00 00 27 82
2025-08-28 17:44:49.928349:   GateCommand.exitEnd: aa 00 c9 80 00 00 26 7e
2025-08-28 17:44:49.928349:   GateCommand.reachPosition: aa 00 0a 80 00 00
2025-08-28 17:44:49.929345:   GateCommand.enterOpen: aa 00 02 01 00 00 48 72
2025-08-28 17:44:49.929345:   GateCommand.exitOpen: aa 00 01 01 00 00 48 36
2025-08-28 17:44:49.929345:   GateCommand.failSignal: aa 00 02 01 00 00 48 72
2025-08-28 17:44:49.929345:   GateCommand.tailgating: aa 00 0f 80 00 00
2025-08-28 17:44:49.929345:   GateCommand.doorHasPerson: aa 00 0b 80 00 00
2025-08-28 17:44:49.929345: ✅ 解析到闸机命令: GateCommand.reachPosition
2025-08-28 17:44:49.929345: 解析到闸机命令: position_reached (到达指定位置)
2025-08-28 17:44:49.930343: 收到闸机命令: position_reached (到达指定位置)
2025-08-28 17:44:49.930343: 📍 收到到位信号，当前状态: GateState.exitStarted
2025-08-28 17:44:49.930343: 📊 流程状态：进馆=false, 出馆=true
2025-08-28 17:44:49.930343: 📊 待处理认证：进馆=false, 出馆=false
2025-08-28 17:44:49.931339: 🚪 出馆到位信号，启动认证和10秒数据收集...
2025-08-28 17:44:49.931339: 闸机状态变更: GateState.exitStarted -> GateState.exitWaitingAuth
2025-08-28 17:44:49.931339: 闸机状态更新: GateState.exitStarted -> GateState.exitWaitingAuth
2025-08-28 17:44:49.931339: 🔐 启动出馆认证系统（不关注结果）...
2025-08-28 17:44:49.931339: 闸机状态变更: GateState.exitWaitingAuth -> GateState.exitScanning
2025-08-28 17:44:49.931339: 闸机状态更新: GateState.exitWaitingAuth -> GateState.exitScanning
2025-08-28 17:44:49.931339: 多认证管理器状态变更: listening
2025-08-28 17:44:49.931339: 启动所有认证方式监听: [AuthMethod.readerCard]
2025-08-28 17:44:49.931339: 准备启动 1 个物理认证服务
2025-08-28 17:44:49.932335: 开始读卡器认证监听
2025-08-28 17:44:49.932335: 🔥 测试：跳过强制重新配置，保持现有连接
2025-08-28 17:44:49.932335: 已移除读卡器状态监听器
2025-08-28 17:44:49.932335: 已移除标签数据监听器
2025-08-28 17:44:49.932335: 所有卡片监听器已移除
2025-08-28 17:44:49.932335: 已添加读卡器状态监听器
2025-08-28 17:44:49.932335: 已添加标签数据监听器
2025-08-28 17:44:49.932335: 开始监听卡片数据 - 所有监听器已就绪
2025-08-28 17:44:49.932335: 读卡器认证监听启动成功
2025-08-28 17:44:49.932335: ✅ 出馆认证系统已启动
2025-08-28 17:44:49.933332: 🚀 启动出馆5秒数据收集...
2025-08-28 17:44:49.933332: 🔧 启动5秒计时器，当前时间: 2025-08-28 17:44:49.930343
2025-08-28 17:44:49.933332: 🔧 5秒计时器已设置，计时器对象: Instance of '_Timer'
2025-08-28 17:44:49.933332: 📡 开始从共享池收集数据...
2025-08-28 17:44:49.933332: 🔧 主机模式：直接清空共享池准备收集新数据
2025-08-28 17:44:49.933332: 🧹 开始清空共享扫描池和所有缓冲区...
2025-08-28 17:44:49.933332: 📡 检测到LSGate设备配置: readerType=22
2025-08-28 17:44:49.933332: 🧹 检测到LSGate设备，开始清空硬件缓冲区...
2025-08-28 17:44:49.933332: clearLSGateCache newPort:SendPort
2025-08-28 17:44:49.933332: 📊 LSGate硬件缓存清空命令发送结果: 0
2025-08-28 17:44:49.934328: 📱 已通知页面清空显示（主机模式）
2025-08-28 17:44:49.934328: [channel_1] 收到闸机事件: state_changed
2025-08-28 17:44:49.934328: 📨 收到GateCoordinator事件: state_changed
2025-08-28 17:44:49.934328: 闸机状态变更: GateState.exitWaitingAuth
2025-08-28 17:44:49.934328: 🎨 处理状态变更UI: exitWaitingAuth
2025-08-28 17:44:49.934328: 未处理的状态变更UI: exitWaitingAuth
2025-08-28 17:44:49.934328: 读者证 认证服务启动成功
2025-08-28 17:44:49.934328: 所有认证服务启动完成，成功启动 1 个服务
2025-08-28 17:44:49.934328: 当前可用的认证方式: 读者证
2025-08-28 17:44:49.935325: [channel_1] 收到闸机事件: state_changed
2025-08-28 17:44:49.935325: 📨 收到GateCoordinator事件: state_changed
2025-08-28 17:44:49.935325: 闸机状态变更: GateState.exitScanning
2025-08-28 17:44:49.935325: 🎨 处理状态变更UI: exitScanning
2025-08-28 17:44:49.935325: 页面状态变更: SilencePageState.rfidScanning
2025-08-28 17:44:49.935325: [channel_1] 收到闸机事件: page_clear
2025-08-28 17:44:49.935325: 📨 收到GateCoordinator事件: page_clear
2025-08-28 17:44:49.935325: 📱 处理页面清空事件
2025-08-28 17:44:49.935325: 页面状态变更: SilencePageState.rfidScanning
2025-08-28 17:44:49.936322: 📱 页面已清空并更新显示为0个条码
2025-08-28 17:44:49.936322: subThread :ReaderCommand.clearLSGateCache
2025-08-28 17:44:49.936322: commandRsp:ReaderCommand.clearLSGateCache
2025-08-28 17:44:49.936322: 🧹 开始清空LSGate硬件缓存...
2025-08-28 17:44:50.033999: 🧹 LSGate设备 缓存清空成功
2025-08-28 17:44:50.033999: ✅ LSGate硬件缓存清空完成
2025-08-28 17:44:50.131676: ✅ LSGate硬件缓冲区清空完成
2025-08-28 17:44:50.132674: 🧹 开始清空共享扫描池...
2025-08-28 17:44:50.132674: 📊 清空前状态: 大小=0, 内容=[]
2025-08-28 17:44:50.132674: 🔄 重置RFID去重集合...
2025-08-28 17:44:50.133675: 🔄 开始重置已处理条码集合...
2025-08-28 17:44:50.133675: 📊 重置前状态: 大小=0, 内容=[]
2025-08-28 17:44:50.133675: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-28 17:44:50.133675: 🔄 当前场上标签将被重新识别为新标签
2025-08-28 17:44:50.133675: 📊 当前tagList状态: 0个标签
2025-08-28 17:44:50.133675: 🚀 立即触发轮询，加速标签重新发现...
2025-08-28 17:44:50.133675: 🔄 开始RFID轮询检查...
2025-08-28 17:44:50.133675: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:44:50.133675: ⚠️ tagList为空，场上无标签
2025-08-28 17:44:50.134667: ✅ 已重置RFID去重集合，现有标签将被重新识别
2025-08-28 17:44:50.134667: 📡 RFID扫描状态（清空后）: isScanning=false
2025-08-28 17:44:50.134667: ⚠️ RFID未在扫描状态，尝试启动数据收集以恢复轮询...
2025-08-28 17:44:50.134667: 开始RFID数据收集...
2025-08-28 17:44:50.134667: 🔄 RFID数据收集已在进行中，重置防重复机制
2025-08-28 17:44:50.134667: ✅ 已处理条码列表已清空，轮询将重新发现标签
2025-08-28 17:44:50.134667: ✅ 共享扫描池已清空: 0 -> 0
2025-08-28 17:44:50.134667: 📡 清空通知已发送，等待RFID重新检测标签...
2025-08-28 17:44:50.134667: 清空RFID扫描缓冲区...
2025-08-28 17:44:50.135664: 🧹 已清空HWTagProvider: 0 -> 0个标签
2025-08-28 17:44:50.135664: 🔧 主机模式：清空软件缓冲区，保持硬件持续扫描
2025-08-28 17:44:50.135664: 🔄 开始重置已处理条码集合...
2025-08-28 17:44:50.135664: 📊 重置前状态: 大小=0, 内容=[]
2025-08-28 17:44:50.135664: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-28 17:44:50.135664: 🔄 当前场上标签将被重新识别为新标签
2025-08-28 17:44:50.135664: 📊 当前tagList状态: 0个标签
2025-08-28 17:44:50.135664: 🚀 立即触发轮询，加速标签重新发现...
2025-08-28 17:44:50.135664: 🔄 开始RFID轮询检查...
2025-08-28 17:44:50.135664: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:44:50.136660: ⚠️ tagList为空，场上无标签
2025-08-28 17:44:50.136660: ✅ 软件缓冲区已清空，硬件扫描保持运行
2025-08-28 17:44:50.136660: RFID数据收集已启动
2025-08-28 17:44:50.136660: ✅ 已启动RFID数据收集（恢复轮询）
2025-08-28 17:44:50.136660: 🔄 开始重置已处理条码集合...
2025-08-28 17:44:50.136660: 📊 重置前状态: 大小=0, 内容=[]
2025-08-28 17:44:50.136660: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-28 17:44:50.136660: 🔄 当前场上标签将被重新识别为新标签
2025-08-28 17:44:50.137657: 📊 当前tagList状态: 0个标签
2025-08-28 17:44:50.137657: 🚀 立即触发轮询，加速标签重新发现...
2025-08-28 17:44:50.137657: 🔄 开始RFID轮询检查...
2025-08-28 17:44:50.137657: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:44:50.137657: ⚠️ tagList为空，场上无标签
2025-08-28 17:44:50.137657: ✅ 共享扫描池、RFID缓冲区和LSGate硬件缓冲区已清空
2025-08-28 17:44:50.137657: 🔄 开始RFID轮询检查...
2025-08-28 17:44:50.138653: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:44:50.138653: ⚠️ tagList为空，场上无标签
2025-08-28 17:44:50.139650: 🔍 LSGate硬件扫描详情:
2025-08-28 17:44:50.139650:   - 设备句柄: 2402617358784
2025-08-28 17:44:50.139650:   - FetchRecords返回值: 0
2025-08-28 17:44:50.140648:   - 报告数量: 0
2025-08-28 17:44:50.140648: 📊 LSGate扫描结果汇总:
2025-08-28 17:44:50.140648:   - 发现标签数量: 0
2025-08-28 17:44:50.140648:   - 未发现任何RFID标签
2025-08-28 17:44:50.140648: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:44:50.141644: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:44:50.637005: 🔄 开始RFID轮询检查...
2025-08-28 17:44:50.637005: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:44:50.637005: ⚠️ tagList为空，场上无标签
2025-08-28 17:44:50.639995: 🔍 LSGate硬件扫描详情:
2025-08-28 17:44:50.639995:   - 设备句柄: 2402617358784
2025-08-28 17:44:50.639995:   - FetchRecords返回值: 0
2025-08-28 17:44:50.639995:   - 报告数量: 0
2025-08-28 17:44:50.639995: 📊 LSGate扫描结果汇总:
2025-08-28 17:44:50.640992:   - 发现标签数量: 0
2025-08-28 17:44:50.640992:   - 未发现任何RFID标签
2025-08-28 17:44:50.640992: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:44:50.641989: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:44:51.136873: 🔄 开始RFID轮询检查...
2025-08-28 17:44:51.136873: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:44:51.136873: ⚠️ tagList为空，场上无标签
2025-08-28 17:44:51.139861: 🔍 LSGate硬件扫描详情:
2025-08-28 17:44:51.139861:   - 设备句柄: 2402617358784
2025-08-28 17:44:51.139861:   - FetchRecords返回值: 0
2025-08-28 17:44:51.140859:   - 报告数量: 0
2025-08-28 17:44:51.140859: 📊 LSGate扫描结果汇总:
2025-08-28 17:44:51.140859:   - 发现标签数量: 0
2025-08-28 17:44:51.140859:   - 未发现任何RFID标签
2025-08-28 17:44:51.140859: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:44:51.140859: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:44:51.637221: 🔄 开始RFID轮询检查...
2025-08-28 17:44:51.637221: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:44:51.637221: ⚠️ tagList为空，场上无标签
2025-08-28 17:44:51.640207: 🔍 LSGate硬件扫描详情:
2025-08-28 17:44:51.640207:   - 设备句柄: 2402617358784
2025-08-28 17:44:51.640207:   - FetchRecords返回值: 0
2025-08-28 17:44:51.640207:   - 报告数量: 0
2025-08-28 17:44:51.641204: 📊 LSGate扫描结果汇总:
2025-08-28 17:44:51.641204:   - 发现标签数量: 0
2025-08-28 17:44:51.641204:   - 未发现任何RFID标签
2025-08-28 17:44:51.641204: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:44:51.641204: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:44:52.137562: 🔄 开始RFID轮询检查...
2025-08-28 17:44:52.137562: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:44:52.137562: ⚠️ tagList为空，场上无标签
2025-08-28 17:44:52.140554: 🔍 LSGate硬件扫描详情:
2025-08-28 17:44:52.141550:   - 设备句柄: 2402617358784
2025-08-28 17:44:52.141550:   - FetchRecords返回值: 0
2025-08-28 17:44:52.141550:   - 报告数量: 0
2025-08-28 17:44:52.141550: 📊 LSGate扫描结果汇总:
2025-08-28 17:44:52.142546:   - 发现标签数量: 0
2025-08-28 17:44:52.142546:   - 未发现任何RFID标签
2025-08-28 17:44:52.142546: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:44:52.142546: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:44:52.636911: 🔄 开始RFID轮询检查...
2025-08-28 17:44:52.636911: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:44:52.636911: ⚠️ tagList为空，场上无标签
2025-08-28 17:44:52.639901: 🔍 LSGate硬件扫描详情:
2025-08-28 17:44:52.639901:   - 设备句柄: 2402617358784
2025-08-28 17:44:52.639901:   - FetchRecords返回值: 0
2025-08-28 17:44:52.639901:   - 报告数量: 0
2025-08-28 17:44:52.639901: 📊 LSGate扫描结果汇总:
2025-08-28 17:44:52.640898:   - 发现标签数量: 0
2025-08-28 17:44:52.640898:   - 未发现任何RFID标签
2025-08-28 17:44:52.640898: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:44:52.640898: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:44:53.137258: 🔄 开始RFID轮询检查...
2025-08-28 17:44:53.137258: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:44:53.137258: ⚠️ tagList为空，场上无标签
2025-08-28 17:44:53.140247: 🔍 LSGate硬件扫描详情:
2025-08-28 17:44:53.140247:   - 设备句柄: 2402617358784
2025-08-28 17:44:53.140247:   - FetchRecords返回值: 0
2025-08-28 17:44:53.140247:   - 报告数量: 0
2025-08-28 17:44:53.140247: 📊 LSGate扫描结果汇总:
2025-08-28 17:44:53.141244:   - 发现标签数量: 0
2025-08-28 17:44:53.141244:   - 未发现任何RFID标签
2025-08-28 17:44:53.141244: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:44:53.141244: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:44:53.637604: 🔄 开始RFID轮询检查...
2025-08-28 17:44:53.638601: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:44:53.638601: ⚠️ tagList为空，场上无标签
2025-08-28 17:44:53.638601: 🔍 LSGate硬件扫描详情:
2025-08-28 17:44:53.638601:   - 设备句柄: 2402617358784
2025-08-28 17:44:53.639602:   - FetchRecords返回值: 0
2025-08-28 17:44:53.639602:   - 报告数量: 0
2025-08-28 17:44:53.639602: 📊 LSGate扫描结果汇总:
2025-08-28 17:44:53.639602:   - 发现标签数量: 0
2025-08-28 17:44:53.639602:   - 未发现任何RFID标签
2025-08-28 17:44:53.640595: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:44:53.640595: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:44:54.137471: 🔄 开始RFID轮询检查...
2025-08-28 17:44:54.137471: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:44:54.137471: ⚠️ tagList为空，场上无标签
2025-08-28 17:44:54.140461: 🔍 LSGate硬件扫描详情:
2025-08-28 17:44:54.140461:   - 设备句柄: 2402617358784
2025-08-28 17:44:54.140461:   - FetchRecords返回值: 0
2025-08-28 17:44:54.141458:   - 报告数量: 0
2025-08-28 17:44:54.141458: 📊 LSGate扫描结果汇总:
2025-08-28 17:44:54.141458:   - 发现标签数量: 0
2025-08-28 17:44:54.141458:   - 未发现任何RFID标签
2025-08-28 17:44:54.141458: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:44:54.141458: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:44:54.637816: 🔄 开始RFID轮询检查...
2025-08-28 17:44:54.637816: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:44:54.637816: ⚠️ tagList为空，场上无标签
2025-08-28 17:44:54.638814: 🔍 LSGate硬件扫描详情:
2025-08-28 17:44:54.638814:   - 设备句柄: 2402617358784
2025-08-28 17:44:54.638814:   - FetchRecords返回值: 0
2025-08-28 17:44:54.638814:   - 报告数量: 0
2025-08-28 17:44:54.639810: 📊 LSGate扫描结果汇总:
2025-08-28 17:44:54.639810:   - 发现标签数量: 0
2025-08-28 17:44:54.639810:   - 未发现任何RFID标签
2025-08-28 17:44:54.639810: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:44:54.639810: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:44:54.931847: ⏰ 5秒数据收集完成，开始书籍检查... 时间: 2025-08-28 17:44:54.931847
2025-08-28 17:44:54.932843: 🛑 停止出馆数据收集，收集到UID数量: 0
2025-08-28 17:44:54.932843: 🔧 停止时间: 2025-08-28 17:44:54.931847
2025-08-28 17:44:54.933847: 📡 停止从共享池收集数据...
2025-08-28 17:44:54.933847: 🔧 主机模式：从RFID服务获取数据
2025-08-28 17:44:54.933847: 📡 开始从RFID服务收集UID数据...
2025-08-28 17:44:54.933847: 📊 HWTagProvider.tagList为空，无UID数据
2025-08-28 17:44:54.933847: 📡 从RFID服务收集到UID数量: 0
2025-08-28 17:44:54.933847: 📋 UID列表: 
2025-08-28 17:44:54.934834: 📊 数据收集完成，UID列表: []
2025-08-28 17:44:54.934834: 🔍 开始三步书籍检查，UID数量: 0
2025-08-28 17:44:54.934834: 闸机状态变更: GateState.exitScanning -> GateState.exitChecking
2025-08-28 17:44:54.934834: 闸机状态更新: GateState.exitScanning -> GateState.exitChecking
2025-08-28 17:44:54.934834: 未收集到UID，允许通过
2025-08-28 17:44:54.934834: ✅ 允许出馆: 未检测到书籍，请通过
2025-08-28 17:44:54.934834: ✅ 命令顺序正确：出馆流程中且最后命令是到位信号
2025-08-28 17:44:54.934834: 📤 正在发送成功信号到闸机：AA 00 01 01 00 00 48 36
2025-08-28 17:44:54.935831: 📤 准备发送闸机命令: success_signal
2025-08-28 17:44:54.935831: 📤 成功信号已发送（异步）
2025-08-28 17:44:54.935831: 闸机状态变更: GateState.exitChecking -> GateState.exitOver
2025-08-28 17:44:54.935831: 闸机状态更新: GateState.exitChecking -> GateState.exitOver
2025-08-28 17:44:54.935831: [channel_1] 收到闸机事件: state_changed
2025-08-28 17:44:54.935831: 📨 收到GateCoordinator事件: state_changed
2025-08-28 17:44:54.935831: 闸机状态变更: GateState.exitChecking
2025-08-28 17:44:54.936828: 🎨 处理状态变更UI: exitChecking
2025-08-28 17:44:54.936828: 未处理的状态变更UI: exitChecking
2025-08-28 17:44:54.936828: 发送原始命令数据: aa 00 01 01 00 00 48 36
2025-08-28 17:44:54.936828: 发送原始数据: aa 00 01 01 00 00 48 36
2025-08-28 17:44:54.936828: 发送数据成功: aa 00 01 01 00 00 48 36
2025-08-28 17:44:54.936828: [channel_1] 收到闸机事件: state_changed
2025-08-28 17:44:54.936828: 📨 收到GateCoordinator事件: state_changed
2025-08-28 17:44:54.937824: 闸机状态变更: GateState.exitOver
2025-08-28 17:44:54.937824: 🎨 处理状态变更UI: exitOver
2025-08-28 17:44:54.937824: 未处理的状态变更UI: exitOver
2025-08-28 17:44:54.937824: 发送数据成功: aa 00 01 01 00 00 48 36
2025-08-28 17:44:54.937824: 闸机命令发送成功: success_signal
2025-08-28 17:44:54.937824: ✅ 闸机命令 success_signal 发送成功
2025-08-28 17:44:54.937824: [channel_1] 收到闸机事件: exit_allowed
2025-08-28 17:44:54.937824: 📨 收到GateCoordinator事件: exit_allowed
2025-08-28 17:44:54.938821: 页面状态变更: SilencePageState.exitAllowed
2025-08-28 17:44:54.967727: 接收到数据: aa 00 01 81 00 00 49 de
2025-08-28 17:44:54.967727: 🔍 接收到串口数据: aa 00 01 81 00 00 49 de
2025-08-28 17:44:54.967727: 🔍 数据长度: 8 字节
2025-08-28 17:44:54.967727: 🔍 预定义命令列表:
2025-08-28 17:44:54.967727:   GateCommand.enterStart: aa 00 64 80 00 00 06 d2
2025-08-28 17:44:54.968723:   GateCommand.enterEnd: aa 00 65 80 00 00 07 2e
2025-08-28 17:44:54.968723:   GateCommand.exitStart: aa 00 c8 80 00 00 27 82
2025-08-28 17:44:54.968723:   GateCommand.exitEnd: aa 00 c9 80 00 00 26 7e
2025-08-28 17:44:54.968723:   GateCommand.reachPosition: aa 00 0a 80 00 00
2025-08-28 17:44:54.968723:   GateCommand.enterOpen: aa 00 02 01 00 00 48 72
2025-08-28 17:44:54.968723:   GateCommand.exitOpen: aa 00 01 01 00 00 48 36
2025-08-28 17:44:54.968723:   GateCommand.failSignal: aa 00 02 01 00 00 48 72
2025-08-28 17:44:54.968723:   GateCommand.tailgating: aa 00 0f 80 00 00
2025-08-28 17:44:54.968723:   GateCommand.doorHasPerson: aa 00 0b 80 00 00
2025-08-28 17:44:54.968723: ❌ 未识别的闸机命令 - 数据不匹配任何预定义命令
2025-08-28 17:44:54.969719: ❌ 接收数据: aa 00 01 81 00 00 49 de
2025-08-28 17:44:55.138162: 🔄 开始RFID轮询检查...
2025-08-28 17:44:55.138162: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:44:55.138162: ⚠️ tagList为空，场上无标签
2025-08-28 17:44:55.139161: 🔍 LSGate硬件扫描详情:
2025-08-28 17:44:55.139161:   - 设备句柄: 2402617358784
2025-08-28 17:44:55.139161:   - FetchRecords返回值: 0
2025-08-28 17:44:55.139161:   - 报告数量: 0
2025-08-28 17:44:55.140156: 📊 LSGate扫描结果汇总:
2025-08-28 17:44:55.140156:   - 发现标签数量: 0
2025-08-28 17:44:55.140156:   - 未发现任何RFID标签
2025-08-28 17:44:55.140156: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:44:55.140156: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:44:55.637511: 🔄 开始RFID轮询检查...
2025-08-28 17:44:55.637511: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:44:55.637511: ⚠️ tagList为空，场上无标签
2025-08-28 17:44:55.640501: 🔍 LSGate硬件扫描详情:
2025-08-28 17:44:55.640501:   - 设备句柄: 2402617358784
2025-08-28 17:44:55.640501:   - FetchRecords返回值: 0
2025-08-28 17:44:55.640501:   - 报告数量: 0
2025-08-28 17:44:55.640501: 📊 LSGate扫描结果汇总:
2025-08-28 17:44:55.640501:   - 发现标签数量: 0
2025-08-28 17:44:55.641498:   - 未发现任何RFID标签
2025-08-28 17:44:55.641498: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:44:55.641498: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:44:56.137856: 🔄 开始RFID轮询检查...
2025-08-28 17:44:56.137856: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:44:56.137856: ⚠️ tagList为空，场上无标签
2025-08-28 17:44:56.138853: 🔍 LSGate硬件扫描详情:
2025-08-28 17:44:56.138853:   - 设备句柄: 2402617358784
2025-08-28 17:44:56.138853:   - FetchRecords返回值: 0
2025-08-28 17:44:56.138853:   - 报告数量: 0
2025-08-28 17:44:56.138853: 📊 LSGate扫描结果汇总:
2025-08-28 17:44:56.138853:   - 发现标签数量: 0
2025-08-28 17:44:56.139850:   - 未发现任何RFID标签
2025-08-28 17:44:56.139850: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:44:56.139850: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:44:56.637205: 🔄 开始RFID轮询检查...
2025-08-28 17:44:56.637205: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:44:56.637205: ⚠️ tagList为空，场上无标签
2025-08-28 17:44:56.640195: 🔍 LSGate硬件扫描详情:
2025-08-28 17:44:56.640195:   - 设备句柄: 2402617358784
2025-08-28 17:44:56.640195:   - FetchRecords返回值: 0
2025-08-28 17:44:56.640195:   - 报告数量: 0
2025-08-28 17:44:56.640195: 📊 LSGate扫描结果汇总:
2025-08-28 17:44:56.641193:   - 发现标签数量: 0
2025-08-28 17:44:56.641193:   - 未发现任何RFID标签
2025-08-28 17:44:56.641193: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:44:56.641193: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:44:57.138070: 🔄 开始RFID轮询检查...
2025-08-28 17:44:57.138070: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:44:57.138070: ⚠️ tagList为空，场上无标签
2025-08-28 17:44:57.139067: 🔍 LSGate硬件扫描详情:
2025-08-28 17:44:57.139067:   - 设备句柄: 2402617358784
2025-08-28 17:44:57.139067:   - FetchRecords返回值: 0
2025-08-28 17:44:57.139067:   - 报告数量: 0
2025-08-28 17:44:57.140064: 📊 LSGate扫描结果汇总:
2025-08-28 17:44:57.140064:   - 发现标签数量: 0
2025-08-28 17:44:57.140064:   - 未发现任何RFID标签
2025-08-28 17:44:57.140064: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:44:57.140064: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:44:57.637419: 🔄 开始RFID轮询检查...
2025-08-28 17:44:57.637419: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:44:57.637419: ⚠️ tagList为空，场上无标签
2025-08-28 17:44:57.640409: 🔍 LSGate硬件扫描详情:
2025-08-28 17:44:57.640409:   - 设备句柄: 2402617358784
2025-08-28 17:44:57.640409:   - FetchRecords返回值: 0
2025-08-28 17:44:57.640409:   - 报告数量: 0
2025-08-28 17:44:57.640409: 📊 LSGate扫描结果汇总:
2025-08-28 17:44:57.640409:   - 发现标签数量: 0
2025-08-28 17:44:57.641406:   - 未发现任何RFID标签
2025-08-28 17:44:57.641406: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:44:57.641406: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:44:57.933476: 模拟出馆完成，状态重置为idle
2025-08-28 17:44:57.933476: 闸机状态变更: GateState.exitOver -> GateState.idle
2025-08-28 17:44:57.933476: 闸机状态更新: GateState.exitOver -> GateState.idle
2025-08-28 17:44:57.933476: [channel_1] 收到闸机事件: state_changed
2025-08-28 17:44:57.933476: 📨 收到GateCoordinator事件: state_changed
2025-08-28 17:44:57.934471: 闸机状态变更: GateState.idle
2025-08-28 17:44:57.934471: 🎨 处理状态变更UI: idle
2025-08-28 17:44:57.934471: 页面状态变更: SilencePageState.welcome
2025-08-28 17:44:58.137798: 🔄 开始RFID轮询检查...
2025-08-28 17:44:58.137798: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:44:58.137798: ⚠️ tagList为空，场上无标签
2025-08-28 17:44:58.138800: 🔍 LSGate硬件扫描详情:
2025-08-28 17:44:58.138800:   - 设备句柄: 2402617358784
2025-08-28 17:44:58.138800:   - FetchRecords返回值: 0
2025-08-28 17:44:58.139798:   - 报告数量: 0
2025-08-28 17:44:58.139798: 📊 LSGate扫描结果汇总:
2025-08-28 17:44:58.139798:   - 发现标签数量: 0
2025-08-28 17:44:58.139798:   - 未发现任何RFID标签
2025-08-28 17:44:58.140789: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:44:58.140789: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:44:58.637147: 🔄 开始RFID轮询检查...
2025-08-28 17:44:58.637147: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:44:58.637147: ⚠️ tagList为空，场上无标签
2025-08-28 17:44:58.640137: 🔍 LSGate硬件扫描详情:
2025-08-28 17:44:58.640137:   - 设备句柄: 2402617358784
2025-08-28 17:44:58.640137:   - FetchRecords返回值: 0
2025-08-28 17:44:58.640137:   - 报告数量: 0
2025-08-28 17:44:58.640137: 📊 LSGate扫描结果汇总:
2025-08-28 17:44:58.641134:   - 发现标签数量: 0
2025-08-28 17:44:58.641134:   - 未发现任何RFID标签
2025-08-28 17:44:58.641134: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:44:58.641134: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:44:59.137493: 🔄 开始RFID轮询检查...
2025-08-28 17:44:59.137493: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:44:59.137493: ⚠️ tagList为空，场上无标签
2025-08-28 17:44:59.140483: 🔍 LSGate硬件扫描详情:
2025-08-28 17:44:59.140483:   - 设备句柄: 2402617358784
2025-08-28 17:44:59.140483:   - FetchRecords返回值: 0
2025-08-28 17:44:59.140483:   - 报告数量: 0
2025-08-28 17:44:59.141479: 📊 LSGate扫描结果汇总:
2025-08-28 17:44:59.141479:   - 发现标签数量: 0
2025-08-28 17:44:59.141479:   - 未发现任何RFID标签
2025-08-28 17:44:59.141479: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:44:59.141479: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:44:59.487335: 接收到数据: aa 00 c9 80 00 00 26 7e
2025-08-28 17:44:59.487335: 🔍 接收到串口数据: aa 00 c9 80 00 00 26 7e
2025-08-28 17:44:59.488333: 🔍 数据长度: 8 字节
2025-08-28 17:44:59.488333: 🔍 预定义命令列表:
2025-08-28 17:44:59.488333:   GateCommand.enterStart: aa 00 64 80 00 00 06 d2
2025-08-28 17:44:59.488333:   GateCommand.enterEnd: aa 00 65 80 00 00 07 2e
2025-08-28 17:44:59.488333:   GateCommand.exitStart: aa 00 c8 80 00 00 27 82
2025-08-28 17:44:59.488333:   GateCommand.exitEnd: aa 00 c9 80 00 00 26 7e
2025-08-28 17:44:59.488333:   GateCommand.reachPosition: aa 00 0a 80 00 00
2025-08-28 17:44:59.488333:   GateCommand.enterOpen: aa 00 02 01 00 00 48 72
2025-08-28 17:44:59.489329:   GateCommand.exitOpen: aa 00 01 01 00 00 48 36
2025-08-28 17:44:59.489329:   GateCommand.failSignal: aa 00 02 01 00 00 48 72
2025-08-28 17:44:59.489329:   GateCommand.tailgating: aa 00 0f 80 00 00
2025-08-28 17:44:59.489329:   GateCommand.doorHasPerson: aa 00 0b 80 00 00
2025-08-28 17:44:59.489329: ✅ 解析到闸机命令: GateCommand.exitEnd
2025-08-28 17:44:59.489329: 解析到闸机命令: exit_end (出馆结束)
2025-08-28 17:44:59.489329: 收到闸机命令: exit_end (出馆结束)
2025-08-28 17:44:59.490326: 出馆流程结束
2025-08-28 17:44:59.490326: 🧹 开始出馆结束清空操作...
2025-08-28 17:44:59.490326: 🖥️ 主机模式：清空本地集合和关键信息
2025-08-28 17:44:59.490326: 🧹 清空RFID去重信息...
2025-08-28 17:44:59.490326: 扫描结果已清空
2025-08-28 17:44:59.490326: 🔄 开始重置已处理条码集合...
2025-08-28 17:44:59.490326: 📊 重置前状态: 大小=0, 内容=[]
2025-08-28 17:44:59.490326: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-28 17:44:59.491322: 🔄 当前场上标签将被重新识别为新标签
2025-08-28 17:44:59.491322: 📊 当前tagList状态: 0个标签
2025-08-28 17:44:59.491322: 🚀 立即触发轮询，加速标签重新发现...
2025-08-28 17:44:59.491322: 🔄 开始RFID轮询检查...
2025-08-28 17:44:59.491322: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:44:59.491322: ⚠️ tagList为空，场上无标签
2025-08-28 17:44:59.491322: ✅ RFID去重信息已清空
2025-08-28 17:44:59.491322: 🧹 清空收集的UID...
2025-08-28 17:44:59.491322: ✅ 收集的UID已清空: 0个
2025-08-28 17:44:59.491322: 📱 通知页面清空显示...
2025-08-28 17:44:59.492319: 📱 已通知页面清空显示（主机模式）
2025-08-28 17:44:59.492319: ✅ 出馆结束清空操作完成
2025-08-28 17:44:59.492319: 📊 流程状态已清除：进馆=false, 出馆=false
2025-08-28 17:44:59.492319: [channel_1] 收到闸机事件: page_clear
2025-08-28 17:44:59.493317: 📨 收到GateCoordinator事件: page_clear
2025-08-28 17:44:59.493317: 📱 处理页面清空事件
2025-08-28 17:44:59.494315: [channel_1] 收到闸机事件: exit_end
2025-08-28 17:44:59.494315: [channel_1] 主从机扩展：处理出馆结束
2025-08-28 17:44:59.494315: 🧹 [channel_1] 清空队列...
2025-08-28 17:44:59.494315: [channel_1] 清空处理队列，当前大小: 0
2025-08-28 17:44:59.495313: [channel_1] 处理队列已清空
2025-08-28 17:44:59.495313: ⏹️ [channel_1] 停止持续数据获取...
2025-08-28 17:44:59.495313: ⏹️ [channel_1] 持续数据获取已停止
2025-08-28 17:44:59.495313: 🧹 [channel_1] 清空收集的数据并通知页面更新...
2025-08-28 17:44:59.495313: 🧹 [channel_1] 已清空收集的数据
2025-08-28 17:44:59.495313: [channel_1] 通知收集到的条码: []
2025-08-28 17:44:59.495313: ✅ [channel_1] 数据流通知发送成功: 0个条码
2025-08-28 17:44:59.495313: 📱 [channel_1] 已通知页面更新显示为0个条码
2025-08-28 17:44:59.496306: 🔄 [channel_1] 重置数据流状态...
2025-08-28 17:44:59.496306: ✅ 数据变化通知流已创建
2025-08-28 17:44:59.496306: ✅ [channel_1] 数据流状态已重置
2025-08-28 17:44:59.496306: ✅ [channel_1] 出馆结束处理完成：已停止数据获取并清空所有本地状态
2025-08-28 17:44:59.496306: 📨 收到GateCoordinator事件: exit_end
2025-08-28 17:44:59.496306: 📱 处理出馆结束事件
2025-08-28 17:44:59.496306: 🧹 清空扫描条码列表...
2025-08-28 17:44:59.496306: 🏠 更新页面状态为欢迎界面...
2025-08-28 17:44:59.496306: 页面状态变更: SilencePageState.welcome
2025-08-28 17:44:59.496306: ✅ 出馆结束事件处理完成
2025-08-28 17:44:59.497303: [channel_1] 通知收集到的条码: []
2025-08-28 17:44:59.497303: ✅ [channel_1] 数据流通知发送成功: 0个条码
2025-08-28 17:44:59.637845: 🔄 开始RFID轮询检查...
2025-08-28 17:44:59.637845: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:44:59.637845: ⚠️ tagList为空，场上无标签
2025-08-28 17:44:59.638835: 🔍 LSGate硬件扫描详情:
2025-08-28 17:44:59.638835:   - 设备句柄: 2402617358784
2025-08-28 17:44:59.638835:   - FetchRecords返回值: 0
2025-08-28 17:44:59.638835:   - 报告数量: 0
2025-08-28 17:44:59.639832: 📊 LSGate扫描结果汇总:
2025-08-28 17:44:59.639832:   - 发现标签数量: 0
2025-08-28 17:44:59.639832:   - 未发现任何RFID标签
2025-08-28 17:44:59.639832: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:44:59.639832: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:00.137234: 🔄 开始RFID轮询检查...
2025-08-28 17:45:00.137234: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:00.137234: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:00.140225: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:00.141233:   - 设备句柄: 2402617358784
2025-08-28 17:45:00.141233:   - FetchRecords返回值: 0
2025-08-28 17:45:00.142218:   - 报告数量: 0
2025-08-28 17:45:00.142218: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:00.142218:   - 发现标签数量: 0
2025-08-28 17:45:00.142218:   - 未发现任何RFID标签
2025-08-28 17:45:00.142218: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:00.142218: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:00.637580: 🔄 开始RFID轮询检查...
2025-08-28 17:45:00.637580: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:00.637580: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:00.640570: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:00.640570:   - 设备句柄: 2402617358784
2025-08-28 17:45:00.640570:   - FetchRecords返回值: 0
2025-08-28 17:45:00.641572:   - 报告数量: 0
2025-08-28 17:45:00.641572: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:00.641572:   - 发现标签数量: 0
2025-08-28 17:45:00.641572:   - 未发现任何RFID标签
2025-08-28 17:45:00.641572: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:00.642564: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:01.137925: 🔄 开始RFID轮询检查...
2025-08-28 17:45:01.137925: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:01.137925: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:01.138923: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:01.138923:   - 设备句柄: 2402617358784
2025-08-28 17:45:01.138923:   - FetchRecords返回值: 0
2025-08-28 17:45:01.138923:   - 报告数量: 0
2025-08-28 17:45:01.139919: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:01.139919:   - 发现标签数量: 0
2025-08-28 17:45:01.139919:   - 未发现任何RFID标签
2025-08-28 17:45:01.139919: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:01.139919: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:01.638271: 🔄 开始RFID轮询检查...
2025-08-28 17:45:01.638271: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:01.638271: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:01.639268: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:01.639268:   - 设备句柄: 2402617358784
2025-08-28 17:45:01.639268:   - FetchRecords返回值: 0
2025-08-28 17:45:01.639268:   - 报告数量: 0
2025-08-28 17:45:01.639268: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:01.640264:   - 发现标签数量: 0
2025-08-28 17:45:01.640264:   - 未发现任何RFID标签
2025-08-28 17:45:01.640264: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:01.640264: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:02.137620: 🔄 开始RFID轮询检查...
2025-08-28 17:45:02.137620: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:02.137620: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:02.140610: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:02.140610:   - 设备句柄: 2402617358784
2025-08-28 17:45:02.140610:   - FetchRecords返回值: 0
2025-08-28 17:45:02.140610:   - 报告数量: 0
2025-08-28 17:45:02.140610: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:02.141607:   - 发现标签数量: 0
2025-08-28 17:45:02.141607:   - 未发现任何RFID标签
2025-08-28 17:45:02.141607: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:02.141607: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:02.637471: 🔄 开始RFID轮询检查...
2025-08-28 17:45:02.637471: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:02.637471: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:02.640462: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:02.640462:   - 设备句柄: 2402617358784
2025-08-28 17:45:02.640462:   - FetchRecords返回值: 0
2025-08-28 17:45:02.640462:   - 报告数量: 0
2025-08-28 17:45:02.640462: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:02.641459:   - 发现标签数量: 0
2025-08-28 17:45:02.641459:   - 未发现任何RFID标签
2025-08-28 17:45:02.641459: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:02.641459: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:03.137855: 🔄 开始RFID轮询检查...
2025-08-28 17:45:03.137855: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:03.137855: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:03.138853: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:03.138853:   - 设备句柄: 2402617358784
2025-08-28 17:45:03.138853:   - FetchRecords返回值: 0
2025-08-28 17:45:03.138853:   - 报告数量: 0
2025-08-28 17:45:03.138853: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:03.139849:   - 发现标签数量: 0
2025-08-28 17:45:03.139849:   - 未发现任何RFID标签
2025-08-28 17:45:03.139849: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:03.139849: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:03.638201: 🔄 开始RFID轮询检查...
2025-08-28 17:45:03.638201: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:03.638201: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:03.639198: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:03.639198:   - 设备句柄: 2402617358784
2025-08-28 17:45:03.639198:   - FetchRecords返回值: 0
2025-08-28 17:45:03.639198:   - 报告数量: 0
2025-08-28 17:45:03.639198: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:03.639198:   - 发现标签数量: 0
2025-08-28 17:45:03.640195:   - 未发现任何RFID标签
2025-08-28 17:45:03.640195: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:03.640195: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:04.137550: 🔄 开始RFID轮询检查...
2025-08-28 17:45:04.137550: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:04.137550: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:04.140540: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:04.140540:   - 设备句柄: 2402617358784
2025-08-28 17:45:04.140540:   - FetchRecords返回值: 0
2025-08-28 17:45:04.140540:   - 报告数量: 0
2025-08-28 17:45:04.140540: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:04.141537:   - 发现标签数量: 0
2025-08-28 17:45:04.141537:   - 未发现任何RFID标签
2025-08-28 17:45:04.141537: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:04.141537: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:04.637895: 🔄 开始RFID轮询检查...
2025-08-28 17:45:04.637895: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:04.637895: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:04.638893: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:04.638893:   - 设备句柄: 2402617358784
2025-08-28 17:45:04.638893:   - FetchRecords返回值: 0
2025-08-28 17:45:04.638893:   - 报告数量: 0
2025-08-28 17:45:04.638893: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:04.639889:   - 发现标签数量: 0
2025-08-28 17:45:04.639889:   - 未发现任何RFID标签
2025-08-28 17:45:04.639889: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:04.639889: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:05.138241: 🔄 开始RFID轮询检查...
2025-08-28 17:45:05.138241: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:05.138241: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:05.139239: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:05.139239:   - 设备句柄: 2402617358784
2025-08-28 17:45:05.139239:   - FetchRecords返回值: 0
2025-08-28 17:45:05.139239:   - 报告数量: 0
2025-08-28 17:45:05.139239: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:05.140235:   - 发现标签数量: 0
2025-08-28 17:45:05.140235:   - 未发现任何RFID标签
2025-08-28 17:45:05.140235: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:05.140235: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:05.367483: 接收到数据: aa 00 64 80 00 00 06 d2
2025-08-28 17:45:05.368481: 🔍 接收到串口数据: aa 00 64 80 00 00 06 d2
2025-08-28 17:45:05.368481: 🔍 数据长度: 8 字节
2025-08-28 17:45:05.368481: 🔍 预定义命令列表:
2025-08-28 17:45:05.368481:   GateCommand.enterStart: aa 00 64 80 00 00 06 d2
2025-08-28 17:45:05.368481:   GateCommand.enterEnd: aa 00 65 80 00 00 07 2e
2025-08-28 17:45:05.368481:   GateCommand.exitStart: aa 00 c8 80 00 00 27 82
2025-08-28 17:45:05.369477:   GateCommand.exitEnd: aa 00 c9 80 00 00 26 7e
2025-08-28 17:45:05.369477:   GateCommand.reachPosition: aa 00 0a 80 00 00
2025-08-28 17:45:05.369477:   GateCommand.enterOpen: aa 00 02 01 00 00 48 72
2025-08-28 17:45:05.369477:   GateCommand.exitOpen: aa 00 01 01 00 00 48 36
2025-08-28 17:45:05.369477:   GateCommand.failSignal: aa 00 02 01 00 00 48 72
2025-08-28 17:45:05.369477:   GateCommand.tailgating: aa 00 0f 80 00 00
2025-08-28 17:45:05.369477:   GateCommand.doorHasPerson: aa 00 0b 80 00 00
2025-08-28 17:45:05.369477: ✅ 解析到闸机命令: GateCommand.enterStart
2025-08-28 17:45:05.370473: 解析到闸机命令: enter_start (进馆开始)
2025-08-28 17:45:05.370473: 收到闸机命令: enter_start (进馆开始)
2025-08-28 17:45:05.370473: 🚪 收到进馆开始命令，等待进馆到位信号...
2025-08-28 17:45:05.370473: 闸机状态变更: GateState.idle -> GateState.enterStarted
2025-08-28 17:45:05.370473: 闸机状态更新: GateState.idle -> GateState.enterStarted
2025-08-28 17:45:05.370473: 📊 流程状态：进馆流程已开始，等待到位信号
2025-08-28 17:45:05.371470: [channel_1] 收到闸机事件: state_changed
2025-08-28 17:45:05.371470: 📨 收到GateCoordinator事件: state_changed
2025-08-28 17:45:05.371470: 闸机状态变更: GateState.enterStarted
2025-08-28 17:45:05.371470: 🎨 处理状态变更UI: enterStarted
2025-08-28 17:45:05.371470: 未处理的状态变更UI: enterStarted
2025-08-28 17:45:05.371470: [channel_1] 收到闸机事件: enter_start
2025-08-28 17:45:05.371470: 📨 收到GateCoordinator事件: enter_start
2025-08-28 17:45:05.372467: 页面状态变更: SilencePageState.waitingEnter
2025-08-28 17:45:05.637604: 🔄 开始RFID轮询检查...
2025-08-28 17:45:05.638588: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:05.638588: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:05.638588: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:05.638588:   - 设备句柄: 2402617358784
2025-08-28 17:45:05.639584:   - FetchRecords返回值: 0
2025-08-28 17:45:05.639584:   - 报告数量: 0
2025-08-28 17:45:05.639584: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:05.639584:   - 发现标签数量: 0
2025-08-28 17:45:05.639584:   - 未发现任何RFID标签
2025-08-28 17:45:05.640581: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:05.640581: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:05.887291: 接收到数据: aa 00 0a 80 00 00 1a 3a
2025-08-28 17:45:05.888292: 🔍 接收到串口数据: aa 00 0a 80 00 00 1a 3a
2025-08-28 17:45:05.888292: 🔍 数据长度: 8 字节
2025-08-28 17:45:05.888292: 🔍 预定义命令列表:
2025-08-28 17:45:05.888292:   GateCommand.enterStart: aa 00 64 80 00 00 06 d2
2025-08-28 17:45:05.888292:   GateCommand.enterEnd: aa 00 65 80 00 00 07 2e
2025-08-28 17:45:05.888292:   GateCommand.exitStart: aa 00 c8 80 00 00 27 82
2025-08-28 17:45:05.888292:   GateCommand.exitEnd: aa 00 c9 80 00 00 26 7e
2025-08-28 17:45:05.889285:   GateCommand.reachPosition: aa 00 0a 80 00 00
2025-08-28 17:45:05.889285:   GateCommand.enterOpen: aa 00 02 01 00 00 48 72
2025-08-28 17:45:05.889285:   GateCommand.exitOpen: aa 00 01 01 00 00 48 36
2025-08-28 17:45:05.889285:   GateCommand.failSignal: aa 00 02 01 00 00 48 72
2025-08-28 17:45:05.889285:   GateCommand.tailgating: aa 00 0f 80 00 00
2025-08-28 17:45:05.889285:   GateCommand.doorHasPerson: aa 00 0b 80 00 00
2025-08-28 17:45:05.889285: ✅ 解析到闸机命令: GateCommand.reachPosition
2025-08-28 17:45:05.889285: 解析到闸机命令: position_reached (到达指定位置)
2025-08-28 17:45:05.890282: 收到闸机命令: position_reached (到达指定位置)
2025-08-28 17:45:05.890282: 📍 收到到位信号，当前状态: GateState.enterStarted
2025-08-28 17:45:05.890282: 📊 流程状态：进馆=true, 出馆=false
2025-08-28 17:45:05.890282: 📊 待处理认证：进馆=false, 出馆=false
2025-08-28 17:45:05.890282: 🚪 进馆到位信号，时间: 2025-08-28 17:45:05.889285
2025-08-28 17:45:05.890282: 闸机状态变更: GateState.enterStarted -> GateState.enterWaitingAuth
2025-08-28 17:45:05.890282: 闸机状态更新: GateState.enterStarted -> GateState.enterWaitingAuth
2025-08-28 17:45:05.891278: ⏰ 启动9秒约束计时器（9秒内必须完成认证/检查）
2025-08-28 17:45:05.891278: 🔐 启动进馆认证系统...
2025-08-28 17:45:05.891278: 闸机状态变更: GateState.enterWaitingAuth -> GateState.enterScanning
2025-08-28 17:45:05.891278: 闸机状态更新: GateState.enterWaitingAuth -> GateState.enterScanning
2025-08-28 17:45:05.891278: 认证监听已在运行中
2025-08-28 17:45:05.891278: ✅ 进馆认证系统已启动
2025-08-28 17:45:05.891278: [channel_1] 收到闸机事件: state_changed
2025-08-28 17:45:05.891278: 📨 收到GateCoordinator事件: state_changed
2025-08-28 17:45:05.891278: 闸机状态变更: GateState.enterWaitingAuth
2025-08-28 17:45:05.891278: 🎨 处理状态变更UI: enterWaitingAuth
2025-08-28 17:45:05.892275: 未处理的状态变更UI: enterWaitingAuth
2025-08-28 17:45:05.892275: [channel_1] 收到闸机事件: state_changed
2025-08-28 17:45:05.892275: 📨 收到GateCoordinator事件: state_changed
2025-08-28 17:45:05.892275: 闸机状态变更: GateState.enterScanning
2025-08-28 17:45:05.892275: 🎨 处理状态变更UI: enterScanning
2025-08-28 17:45:05.892275: 页面状态变更: SilencePageState.authenticating
2025-08-28 17:45:06.137464: 🔄 开始RFID轮询检查...
2025-08-28 17:45:06.137464: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:06.137464: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:06.139457: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:06.139457:   - 设备句柄: 2402617358784
2025-08-28 17:45:06.139457:   - FetchRecords返回值: 0
2025-08-28 17:45:06.139457:   - 报告数量: 0
2025-08-28 17:45:06.139457: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:06.140454:   - 发现标签数量: 0
2025-08-28 17:45:06.140454:   - 未发现任何RFID标签
2025-08-28 17:45:06.140454: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:06.140454: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:06.637809: 🔄 开始RFID轮询检查...
2025-08-28 17:45:06.637809: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:06.637809: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:06.638806: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:06.638806:   - 设备句柄: 2402617358784
2025-08-28 17:45:06.638806:   - FetchRecords返回值: 0
2025-08-28 17:45:06.638806:   - 报告数量: 0
2025-08-28 17:45:06.638806: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:06.639803:   - 发现标签数量: 0
2025-08-28 17:45:06.639803:   - 未发现任何RFID标签
2025-08-28 17:45:06.639803: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:06.639803: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:07.137158: 🔄 开始RFID轮询检查...
2025-08-28 17:45:07.137158: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:07.137158: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:07.140148: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:07.140148:   - 设备句柄: 2402617358784
2025-08-28 17:45:07.140148:   - FetchRecords返回值: 0
2025-08-28 17:45:07.140148:   - 报告数量: 0
2025-08-28 17:45:07.140148: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:07.141145:   - 发现标签数量: 0
2025-08-28 17:45:07.141145:   - 未发现任何RFID标签
2025-08-28 17:45:07.141145: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:07.141145: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:07.637504: 🔄 开始RFID轮询检查...
2025-08-28 17:45:07.637504: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:07.637504: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:07.640494: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:07.640494:   - 设备句柄: 2402617358784
2025-08-28 17:45:07.640494:   - FetchRecords返回值: 0
2025-08-28 17:45:07.640494:   - 报告数量: 0
2025-08-28 17:45:07.640494: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:07.641491:   - 发现标签数量: 0
2025-08-28 17:45:07.641491:   - 未发现任何RFID标签
2025-08-28 17:45:07.641491: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:07.641491: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:08.137372: 🔄 开始RFID轮询检查...
2025-08-28 17:45:08.137372: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:08.137372: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:08.140362: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:08.140362:   - 设备句柄: 2402617358784
2025-08-28 17:45:08.140362:   - FetchRecords返回值: 0
2025-08-28 17:45:08.140362:   - 报告数量: 0
2025-08-28 17:45:08.140362: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:08.141359:   - 发现标签数量: 0
2025-08-28 17:45:08.141359:   - 未发现任何RFID标签
2025-08-28 17:45:08.141359: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:08.141359: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:08.638239: 🔄 开始RFID轮询检查...
2025-08-28 17:45:08.638239: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:08.638239: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:08.639258: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:08.639258:   - 设备句柄: 2402617358784
2025-08-28 17:45:08.639258:   - FetchRecords返回值: 0
2025-08-28 17:45:08.639258:   - 报告数量: 0
2025-08-28 17:45:08.640233: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:08.640233:   - 发现标签数量: 0
2025-08-28 17:45:08.640233:   - 未发现任何RFID标签
2025-08-28 17:45:08.640233: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:08.640233: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:09.137612: 🔄 开始RFID轮询检查...
2025-08-28 17:45:09.137612: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:09.137612: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:09.140602: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:09.140602:   - 设备句柄: 2402617358784
2025-08-28 17:45:09.140602:   - FetchRecords返回值: 0
2025-08-28 17:45:09.140602:   - 报告数量: 0
2025-08-28 17:45:09.140602: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:09.141599:   - 发现标签数量: 0
2025-08-28 17:45:09.141599:   - 未发现任何RFID标签
2025-08-28 17:45:09.141599: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:09.141599: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:09.636961: 🔄 开始RFID轮询检查...
2025-08-28 17:45:09.636961: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:09.636961: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:09.639951: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:09.639951:   - 设备句柄: 2402617358784
2025-08-28 17:45:09.639951:   - FetchRecords返回值: 0
2025-08-28 17:45:09.639951:   - 报告数量: 0
2025-08-28 17:45:09.639951: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:09.640948:   - 发现标签数量: 0
2025-08-28 17:45:09.640948:   - 未发现任何RFID标签
2025-08-28 17:45:09.640948: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:09.640948: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:10.137307: 🔄 开始RFID轮询检查...
2025-08-28 17:45:10.137307: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:10.137307: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:10.140297: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:10.140297:   - 设备句柄: 2402617358784
2025-08-28 17:45:10.140297:   - FetchRecords返回值: 0
2025-08-28 17:45:10.140297:   - 报告数量: 0
2025-08-28 17:45:10.141294: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:10.141294:   - 发现标签数量: 0
2025-08-28 17:45:10.141294:   - 未发现任何RFID标签
2025-08-28 17:45:10.141294: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:10.141294: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:10.637652: 🔄 开始RFID轮询检查...
2025-08-28 17:45:10.637652: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:10.637652: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:10.639646: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:10.639646:   - 设备句柄: 2402617358784
2025-08-28 17:45:10.639646:   - FetchRecords返回值: 0
2025-08-28 17:45:10.639646:   - 报告数量: 0
2025-08-28 17:45:10.639646: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:10.640643:   - 发现标签数量: 0
2025-08-28 17:45:10.640643:   - 未发现任何RFID标签
2025-08-28 17:45:10.640643: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:10.640643: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:11.137998: 🔄 开始RFID轮询检查...
2025-08-28 17:45:11.137998: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:11.137998: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:11.138996: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:11.138996:   - 设备句柄: 2402617358784
2025-08-28 17:45:11.138996:   - FetchRecords返回值: 0
2025-08-28 17:45:11.138996:   - 报告数量: 0
2025-08-28 17:45:11.139992: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:11.139992:   - 发现标签数量: 0
2025-08-28 17:45:11.139992:   - 未发现任何RFID标签
2025-08-28 17:45:11.139992: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:11.139992: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:11.637347: 🔄 开始RFID轮询检查...
2025-08-28 17:45:11.637347: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:11.637347: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:11.640337: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:11.640337:   - 设备句柄: 2402617358784
2025-08-28 17:45:11.640337:   - FetchRecords返回值: 0
2025-08-28 17:45:11.640337:   - 报告数量: 0
2025-08-28 17:45:11.641334: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:11.641334:   - 发现标签数量: 0
2025-08-28 17:45:11.641334:   - 未发现任何RFID标签
2025-08-28 17:45:11.641334: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:11.641334: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:12.137229: 🔄 开始RFID轮询检查...
2025-08-28 17:45:12.137229: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:12.137229: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:12.140219: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:12.140219:   - 设备句柄: 2402617358784
2025-08-28 17:45:12.140219:   - FetchRecords返回值: 0
2025-08-28 17:45:12.140219:   - 报告数量: 0
2025-08-28 17:45:12.140219: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:12.141216:   - 发现标签数量: 0
2025-08-28 17:45:12.141216:   - 未发现任何RFID标签
2025-08-28 17:45:12.141216: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:12.141216: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:12.637575: 🔄 开始RFID轮询检查...
2025-08-28 17:45:12.637575: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:12.637575: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:12.640565: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:12.640565:   - 设备句柄: 2402617358784
2025-08-28 17:45:12.640565:   - FetchRecords返回值: 0
2025-08-28 17:45:12.640565:   - 报告数量: 0
2025-08-28 17:45:12.640565: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:12.640565:   - 发现标签数量: 0
2025-08-28 17:45:12.640565:   - 未发现任何RFID标签
2025-08-28 17:45:12.641562: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:12.641562: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:13.137920: 🔄 开始RFID轮询检查...
2025-08-28 17:45:13.137920: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:13.137920: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:13.138917: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:13.138917:   - 设备句柄: 2402617358784
2025-08-28 17:45:13.138917:   - FetchRecords返回值: 0
2025-08-28 17:45:13.138917:   - 报告数量: 0
2025-08-28 17:45:13.138917: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:13.139914:   - 发现标签数量: 0
2025-08-28 17:45:13.139914:   - 未发现任何RFID标签
2025-08-28 17:45:13.139914: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:13.139914: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:13.637269: 🔄 开始RFID轮询检查...
2025-08-28 17:45:13.637269: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:13.637269: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:13.640259: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:13.640259:   - 设备句柄: 2402617358784
2025-08-28 17:45:13.640259:   - FetchRecords返回值: 0
2025-08-28 17:45:13.640259:   - 报告数量: 0
2025-08-28 17:45:13.640259: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:13.641256:   - 发现标签数量: 0
2025-08-28 17:45:13.641256:   - 未发现任何RFID标签
2025-08-28 17:45:13.641256: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:13.641256: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:14.137615: 🔄 开始RFID轮询检查...
2025-08-28 17:45:14.137615: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:14.137615: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:14.140605: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:14.140605:   - 设备句柄: 2402617358784
2025-08-28 17:45:14.140605:   - FetchRecords返回值: 0
2025-08-28 17:45:14.140605:   - 报告数量: 0
2025-08-28 17:45:14.140605: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:14.141602:   - 发现标签数量: 0
2025-08-28 17:45:14.141602:   - 未发现任何RFID标签
2025-08-28 17:45:14.141602: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:14.141602: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:14.636964: 🔄 开始RFID轮询检查...
2025-08-28 17:45:14.636964: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:14.636964: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:14.639954: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:14.639954:   - 设备句柄: 2402617358784
2025-08-28 17:45:14.639954:   - FetchRecords返回值: 0
2025-08-28 17:45:14.639954:   - 报告数量: 0
2025-08-28 17:45:14.639954: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:14.640951:   - 发现标签数量: 0
2025-08-28 17:45:14.640951:   - 未发现任何RFID标签
2025-08-28 17:45:14.640951: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:14.640951: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:14.891643: ⏰ 9秒约束超时！认证/检查必须在9秒内完成
2025-08-28 17:45:14.891643: 📊 当前流程状态：进馆=true, 出馆=false
2025-08-28 17:45:14.891643: ⏰ 9秒约束超时，重置状态等待下一次到位信号
2025-08-28 17:45:14.891643: 停止所有认证方式监听
2025-08-28 17:45:14.891643: 停止读者证认证监听
2025-08-28 17:45:14.891643: 停止读卡器认证监听
2025-08-28 17:45:14.892641: 已移除读卡器状态监听器
2025-08-28 17:45:14.892641: 已移除标签数据监听器
2025-08-28 17:45:14.892641: 所有卡片监听器已移除
2025-08-28 17:45:14.892641: 没有活跃的读卡器连接需要暂停
2025-08-28 17:45:14.892641: 读卡器认证监听已停止（连接保持）
2025-08-28 17:45:14.892641: 读者证认证服务监听已停止
2025-08-28 17:45:14.893637: 多认证管理器状态变更: idle
2025-08-28 17:45:14.893637: 所有认证方式监听已停止
2025-08-28 17:45:15.137829: 🔄 开始RFID轮询检查...
2025-08-28 17:45:15.137829: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:15.137829: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:15.138827: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:15.138827:   - 设备句柄: 2402617358784
2025-08-28 17:45:15.138827:   - FetchRecords返回值: 0
2025-08-28 17:45:15.138827:   - 报告数量: 0
2025-08-28 17:45:15.139823: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:15.139823:   - 发现标签数量: 0
2025-08-28 17:45:15.139823:   - 未发现任何RFID标签
2025-08-28 17:45:15.139823: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:15.139823: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:15.637178: 🔄 开始RFID轮询检查...
2025-08-28 17:45:15.637178: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:15.637178: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:15.640168: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:15.640168:   - 设备句柄: 2402617358784
2025-08-28 17:45:15.640168:   - FetchRecords返回值: 0
2025-08-28 17:45:15.640168:   - 报告数量: 0
2025-08-28 17:45:15.640168: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:15.641165:   - 发现标签数量: 0
2025-08-28 17:45:15.641165:   - 未发现任何RFID标签
2025-08-28 17:45:15.641165: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:15.641165: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:16.137524: 🔄 开始RFID轮询检查...
2025-08-28 17:45:16.137524: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:16.137524: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:16.140514: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:16.140514:   - 设备句柄: 2402617358784
2025-08-28 17:45:16.140514:   - FetchRecords返回值: 0
2025-08-28 17:45:16.140514:   - 报告数量: 0
2025-08-28 17:45:16.141511: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:16.141511:   - 发现标签数量: 0
2025-08-28 17:45:16.141511:   - 未发现任何RFID标签
2025-08-28 17:45:16.141511: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:16.141511: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:16.387696: 接收到数据: aa 00 65 80 00 00 07 2e
2025-08-28 17:45:16.388694: 🔍 接收到串口数据: aa 00 65 80 00 00 07 2e
2025-08-28 17:45:16.388694: 🔍 数据长度: 8 字节
2025-08-28 17:45:16.388694: 🔍 预定义命令列表:
2025-08-28 17:45:16.388694:   GateCommand.enterStart: aa 00 64 80 00 00 06 d2
2025-08-28 17:45:16.388694:   GateCommand.enterEnd: aa 00 65 80 00 00 07 2e
2025-08-28 17:45:16.388694:   GateCommand.exitStart: aa 00 c8 80 00 00 27 82
2025-08-28 17:45:16.389690:   GateCommand.exitEnd: aa 00 c9 80 00 00 26 7e
2025-08-28 17:45:16.389690:   GateCommand.reachPosition: aa 00 0a 80 00 00
2025-08-28 17:45:16.389690:   GateCommand.enterOpen: aa 00 02 01 00 00 48 72
2025-08-28 17:45:16.389690:   GateCommand.exitOpen: aa 00 01 01 00 00 48 36
2025-08-28 17:45:16.389690:   GateCommand.failSignal: aa 00 02 01 00 00 48 72
2025-08-28 17:45:16.389690:   GateCommand.tailgating: aa 00 0f 80 00 00
2025-08-28 17:45:16.389690:   GateCommand.doorHasPerson: aa 00 0b 80 00 00
2025-08-28 17:45:16.389690: ✅ 解析到闸机命令: GateCommand.enterEnd
2025-08-28 17:45:16.389690: 解析到闸机命令: enter_end (进馆结束)
2025-08-28 17:45:16.390687: 收到闸机命令: enter_end (进馆结束)
2025-08-28 17:45:16.390687: 进馆流程结束
2025-08-28 17:45:16.390687: 闸机状态变更: GateState.enterScanning -> GateState.idle
2025-08-28 17:45:16.390687: 闸机状态更新: GateState.enterScanning -> GateState.idle
2025-08-28 17:45:16.390687: ⏰ 取消9秒约束计时器
2025-08-28 17:45:16.390687: 📊 流程状态已清除：进馆=false, 出馆=false
2025-08-28 17:45:16.390687: [channel_1] 收到闸机事件: state_changed
2025-08-28 17:45:16.390687: 📨 收到GateCoordinator事件: state_changed
2025-08-28 17:45:16.390687: 闸机状态变更: GateState.idle
2025-08-28 17:45:16.391683: 🎨 处理状态变更UI: idle
2025-08-28 17:45:16.391683: 页面状态变更: SilencePageState.welcome
2025-08-28 17:45:16.391683: [channel_1] 收到闸机事件: enter_end
2025-08-28 17:45:16.391683: 📨 收到GateCoordinator事件: enter_end
2025-08-28 17:45:16.391683: 页面状态变更: SilencePageState.welcome
2025-08-28 17:45:16.637869: 🔄 开始RFID轮询检查...
2025-08-28 17:45:16.637869: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:16.637869: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:16.638867: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:16.638867:   - 设备句柄: 2402617358784
2025-08-28 17:45:16.638867:   - FetchRecords返回值: 0
2025-08-28 17:45:16.638867:   - 报告数量: 0
2025-08-28 17:45:16.639863: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:16.639863:   - 发现标签数量: 0
2025-08-28 17:45:16.639863:   - 未发现任何RFID标签
2025-08-28 17:45:16.639863: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:16.639863: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:17.138215: 🔄 开始RFID轮询检查...
2025-08-28 17:45:17.138215: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:17.138215: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:17.139213: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:17.139213:   - 设备句柄: 2402617358784
2025-08-28 17:45:17.139213:   - FetchRecords返回值: 0
2025-08-28 17:45:17.139213:   - 报告数量: 0
2025-08-28 17:45:17.140209: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:17.140209:   - 发现标签数量: 0
2025-08-28 17:45:17.140209:   - 未发现任何RFID标签
2025-08-28 17:45:17.140209: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:17.140209: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:17.568331: 接收到数据: aa 00 64 80 00 00 06 d2
2025-08-28 17:45:17.568331: 🔍 接收到串口数据: aa 00 64 80 00 00 06 d2
2025-08-28 17:45:17.569333: 🔍 数据长度: 8 字节
2025-08-28 17:45:17.569333: 🔍 预定义命令列表:
2025-08-28 17:45:17.569333:   GateCommand.enterStart: aa 00 64 80 00 00 06 d2
2025-08-28 17:45:17.569333:   GateCommand.enterEnd: aa 00 65 80 00 00 07 2e
2025-08-28 17:45:17.569333:   GateCommand.exitStart: aa 00 c8 80 00 00 27 82
2025-08-28 17:45:17.569333:   GateCommand.exitEnd: aa 00 c9 80 00 00 26 7e
2025-08-28 17:45:17.570329:   GateCommand.reachPosition: aa 00 0a 80 00 00
2025-08-28 17:45:17.570329:   GateCommand.enterOpen: aa 00 02 01 00 00 48 72
2025-08-28 17:45:17.570329:   GateCommand.exitOpen: aa 00 01 01 00 00 48 36
2025-08-28 17:45:17.571322:   GateCommand.failSignal: aa 00 02 01 00 00 48 72
2025-08-28 17:45:17.571322:   GateCommand.tailgating: aa 00 0f 80 00 00
2025-08-28 17:45:17.571322:   GateCommand.doorHasPerson: aa 00 0b 80 00 00
2025-08-28 17:45:17.571322: ✅ 解析到闸机命令: GateCommand.enterStart
2025-08-28 17:45:17.572318: 解析到闸机命令: enter_start (进馆开始)
2025-08-28 17:45:17.572318: 收到闸机命令: enter_start (进馆开始)
2025-08-28 17:45:17.572318: 🚪 收到进馆开始命令，等待进馆到位信号...
2025-08-28 17:45:17.572318: 闸机状态变更: GateState.idle -> GateState.enterStarted
2025-08-28 17:45:17.572318: 闸机状态更新: GateState.idle -> GateState.enterStarted
2025-08-28 17:45:17.573315: 📊 流程状态：进馆流程已开始，等待到位信号
2025-08-28 17:45:17.573315: [channel_1] 收到闸机事件: state_changed
2025-08-28 17:45:17.573315: 📨 收到GateCoordinator事件: state_changed
2025-08-28 17:45:17.573315: 闸机状态变更: GateState.enterStarted
2025-08-28 17:45:17.573315: 🎨 处理状态变更UI: enterStarted
2025-08-28 17:45:17.573315: 未处理的状态变更UI: enterStarted
2025-08-28 17:45:17.574315: [channel_1] 收到闸机事件: enter_start
2025-08-28 17:45:17.574315: 📨 收到GateCoordinator事件: enter_start
2025-08-28 17:45:17.574315: 页面状态变更: SilencePageState.waitingEnter
2025-08-28 17:45:17.638100: 🔄 开始RFID轮询检查...
2025-08-28 17:45:17.638100: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:17.639101: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:17.639101: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:17.640096:   - 设备句柄: 2402617358784
2025-08-28 17:45:17.640096:   - FetchRecords返回值: 0
2025-08-28 17:45:17.641091:   - 报告数量: 0
2025-08-28 17:45:17.641091: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:17.641091:   - 发现标签数量: 0
2025-08-28 17:45:17.641091:   - 未发现任何RFID标签
2025-08-28 17:45:17.641091: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:17.642091: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:18.087672: 接收到数据: aa 00 0a 80 00 00 1a 3a
2025-08-28 17:45:18.088674: 🔍 接收到串口数据: aa 00 0a 80 00 00 1a 3a
2025-08-28 17:45:18.088674: 🔍 数据长度: 8 字节
2025-08-28 17:45:18.089666: 🔍 预定义命令列表:
2025-08-28 17:45:18.089666:   GateCommand.enterStart: aa 00 64 80 00 00 06 d2
2025-08-28 17:45:18.089666:   GateCommand.enterEnd: aa 00 65 80 00 00 07 2e
2025-08-28 17:45:18.089666:   GateCommand.exitStart: aa 00 c8 80 00 00 27 82
2025-08-28 17:45:18.089666:   GateCommand.exitEnd: aa 00 c9 80 00 00 26 7e
2025-08-28 17:45:18.090663:   GateCommand.reachPosition: aa 00 0a 80 00 00
2025-08-28 17:45:18.090663:   GateCommand.enterOpen: aa 00 02 01 00 00 48 72
2025-08-28 17:45:18.090663:   GateCommand.exitOpen: aa 00 01 01 00 00 48 36
2025-08-28 17:45:18.090663:   GateCommand.failSignal: aa 00 02 01 00 00 48 72
2025-08-28 17:45:18.090663:   GateCommand.tailgating: aa 00 0f 80 00 00
2025-08-28 17:45:18.090663:   GateCommand.doorHasPerson: aa 00 0b 80 00 00
2025-08-28 17:45:18.091659: ✅ 解析到闸机命令: GateCommand.reachPosition
2025-08-28 17:45:18.091659: 解析到闸机命令: position_reached (到达指定位置)
2025-08-28 17:45:18.091659: 收到闸机命令: position_reached (到达指定位置)
2025-08-28 17:45:18.091659: 📍 收到到位信号，当前状态: GateState.enterStarted
2025-08-28 17:45:18.092656: 📊 流程状态：进馆=true, 出馆=false
2025-08-28 17:45:18.092656: 📊 待处理认证：进馆=false, 出馆=false
2025-08-28 17:45:18.092656: 🚪 进馆到位信号，时间: 2025-08-28 17:45:18.091659
2025-08-28 17:45:18.092656: 闸机状态变更: GateState.enterStarted -> GateState.enterWaitingAuth
2025-08-28 17:45:18.092656: 闸机状态更新: GateState.enterStarted -> GateState.enterWaitingAuth
2025-08-28 17:45:18.092656: ⏰ 启动9秒约束计时器（9秒内必须完成认证/检查）
2025-08-28 17:45:18.092656: 🔐 启动进馆认证系统...
2025-08-28 17:45:18.092656: 闸机状态变更: GateState.enterWaitingAuth -> GateState.enterScanning
2025-08-28 17:45:18.092656: 闸机状态更新: GateState.enterWaitingAuth -> GateState.enterScanning
2025-08-28 17:45:18.093652: 多认证管理器状态变更: listening
2025-08-28 17:45:18.093652: 启动所有认证方式监听: [AuthMethod.readerCard]
2025-08-28 17:45:18.093652: 准备启动 1 个物理认证服务
2025-08-28 17:45:18.093652: 开始读卡器认证监听
2025-08-28 17:45:18.093652: 🔥 测试：跳过强制重新配置，保持现有连接
2025-08-28 17:45:18.093652: 已移除读卡器状态监听器
2025-08-28 17:45:18.093652: 已移除标签数据监听器
2025-08-28 17:45:18.093652: 所有卡片监听器已移除
2025-08-28 17:45:18.093652: 已添加读卡器状态监听器
2025-08-28 17:45:18.093652: 已添加标签数据监听器
2025-08-28 17:45:18.094649: 开始监听卡片数据 - 所有监听器已就绪
2025-08-28 17:45:18.094649: 读卡器认证监听启动成功
2025-08-28 17:45:18.094649: ✅ 进馆认证系统已启动
2025-08-28 17:45:18.094649: [channel_1] 收到闸机事件: state_changed
2025-08-28 17:45:18.094649: 📨 收到GateCoordinator事件: state_changed
2025-08-28 17:45:18.094649: 闸机状态变更: GateState.enterWaitingAuth
2025-08-28 17:45:18.094649: 🎨 处理状态变更UI: enterWaitingAuth
2025-08-28 17:45:18.094649: 未处理的状态变更UI: enterWaitingAuth
2025-08-28 17:45:18.094649: 读者证 认证服务启动成功
2025-08-28 17:45:18.095645: 所有认证服务启动完成，成功启动 1 个服务
2025-08-28 17:45:18.095645: 当前可用的认证方式: 读者证
2025-08-28 17:45:18.095645: [channel_1] 收到闸机事件: state_changed
2025-08-28 17:45:18.095645: 📨 收到GateCoordinator事件: state_changed
2025-08-28 17:45:18.095645: 闸机状态变更: GateState.enterScanning
2025-08-28 17:45:18.095645: 🎨 处理状态变更UI: enterScanning
2025-08-28 17:45:18.095645: 页面状态变更: SilencePageState.authenticating
2025-08-28 17:45:18.137507: 🔄 开始RFID轮询检查...
2025-08-28 17:45:18.137507: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:18.137507: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:18.140497: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:18.140497:   - 设备句柄: 2402617358784
2025-08-28 17:45:18.140497:   - FetchRecords返回值: 0
2025-08-28 17:45:18.140497:   - 报告数量: 0
2025-08-28 17:45:18.140497: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:18.141494:   - 发现标签数量: 0
2025-08-28 17:45:18.141494:   - 未发现任何RFID标签
2025-08-28 17:45:18.141494: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:18.141494: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:18.637852: 🔄 开始RFID轮询检查...
2025-08-28 17:45:18.637852: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:18.637852: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:18.638850: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:18.638850:   - 设备句柄: 2402617358784
2025-08-28 17:45:18.638850:   - FetchRecords返回值: 0
2025-08-28 17:45:18.638850:   - 报告数量: 0
2025-08-28 17:45:18.639846: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:18.639846:   - 发现标签数量: 0
2025-08-28 17:45:18.639846:   - 未发现任何RFID标签
2025-08-28 17:45:18.639846: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:18.639846: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:19.138198: 🔄 开始RFID轮询检查...
2025-08-28 17:45:19.138198: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:19.138198: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:19.139195: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:19.139195:   - 设备句柄: 2402617358784
2025-08-28 17:45:19.139195:   - FetchRecords返回值: 0
2025-08-28 17:45:19.139195:   - 报告数量: 0
2025-08-28 17:45:19.139195: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:19.139195:   - 发现标签数量: 0
2025-08-28 17:45:19.140192:   - 未发现任何RFID标签
2025-08-28 17:45:19.140192: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:19.140192: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:19.637547: 🔄 开始RFID轮询检查...
2025-08-28 17:45:19.637547: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:19.637547: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:19.640537: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:19.640537:   - 设备句柄: 2402617358784
2025-08-28 17:45:19.640537:   - FetchRecords返回值: 0
2025-08-28 17:45:19.640537:   - 报告数量: 0
2025-08-28 17:45:19.640537: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:19.641534:   - 发现标签数量: 0
2025-08-28 17:45:19.641534:   - 未发现任何RFID标签
2025-08-28 17:45:19.641534: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:19.641534: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:20.137893: 🔄 开始RFID轮询检查...
2025-08-28 17:45:20.137893: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:20.137893: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:20.138890: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:20.138890:   - 设备句柄: 2402617358784
2025-08-28 17:45:20.138890:   - FetchRecords返回值: 0
2025-08-28 17:45:20.138890:   - 报告数量: 0
2025-08-28 17:45:20.138890: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:20.138890:   - 发现标签数量: 0
2025-08-28 17:45:20.139886:   - 未发现任何RFID标签
2025-08-28 17:45:20.139886: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:20.139886: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:20.637242: 🔄 开始RFID轮询检查...
2025-08-28 17:45:20.637242: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:20.637242: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:20.640232: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:20.640232:   - 设备句柄: 2402617358784
2025-08-28 17:45:20.640232:   - FetchRecords返回值: 0
2025-08-28 17:45:20.640232:   - 报告数量: 0
2025-08-28 17:45:20.640232: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:20.641229:   - 发现标签数量: 0
2025-08-28 17:45:20.641229:   - 未发现任何RFID标签
2025-08-28 17:45:20.641229: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:20.641229: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:21.138111: 🔄 开始RFID轮询检查...
2025-08-28 17:45:21.138111: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:21.138111: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:21.139108: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:21.139108:   - 设备句柄: 2402617358784
2025-08-28 17:45:21.139108:   - FetchRecords返回值: 0
2025-08-28 17:45:21.139108:   - 报告数量: 0
2025-08-28 17:45:21.139108: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:21.139108:   - 发现标签数量: 0
2025-08-28 17:45:21.140105:   - 未发现任何RFID标签
2025-08-28 17:45:21.140105: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:21.140105: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:21.637460: 🔄 开始RFID轮询检查...
2025-08-28 17:45:21.637460: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:21.637460: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:21.640450: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:21.640450:   - 设备句柄: 2402617358784
2025-08-28 17:45:21.640450:   - FetchRecords返回值: 0
2025-08-28 17:45:21.640450:   - 报告数量: 0
2025-08-28 17:45:21.640450: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:21.641447:   - 发现标签数量: 0
2025-08-28 17:45:21.641447:   - 未发现任何RFID标签
2025-08-28 17:45:21.641447: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:21.641447: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:22.137806: 🔄 开始RFID轮询检查...
2025-08-28 17:45:22.137806: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:22.137806: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:22.138803: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:22.138803:   - 设备句柄: 2402617358784
2025-08-28 17:45:22.138803:   - FetchRecords返回值: 0
2025-08-28 17:45:22.138803:   - 报告数量: 0
2025-08-28 17:45:22.138803: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:22.139799:   - 发现标签数量: 0
2025-08-28 17:45:22.139799:   - 未发现任何RFID标签
2025-08-28 17:45:22.139799: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:22.139799: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:22.637155: 🔄 开始RFID轮询检查...
2025-08-28 17:45:22.637155: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:22.637155: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:22.640145: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:22.640145:   - 设备句柄: 2402617358784
2025-08-28 17:45:22.640145:   - FetchRecords返回值: 0
2025-08-28 17:45:22.640145:   - 报告数量: 0
2025-08-28 17:45:22.641143: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:22.641143:   - 发现标签数量: 0
2025-08-28 17:45:22.641143:   - 未发现任何RFID标签
2025-08-28 17:45:22.641143: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:22.641143: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:23.138498: 🔄 开始RFID轮询检查...
2025-08-28 17:45:23.138498: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:23.138498: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:23.139494: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:23.139494:   - 设备句柄: 2402617358784
2025-08-28 17:45:23.139494:   - FetchRecords返回值: 0
2025-08-28 17:45:23.139494:   - 报告数量: 0
2025-08-28 17:45:23.139494: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:23.140491:   - 发现标签数量: 0
2025-08-28 17:45:23.140491:   - 未发现任何RFID标签
2025-08-28 17:45:23.140491: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:23.140491: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:23.637846: 🔄 开始RFID轮询检查...
2025-08-28 17:45:23.637846: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:23.637846: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:23.638848: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:23.638848:   - 设备句柄: 2402617358784
2025-08-28 17:45:23.638848:   - FetchRecords返回值: 0
2025-08-28 17:45:23.639840:   - 报告数量: 0
2025-08-28 17:45:23.639840: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:23.639840:   - 发现标签数量: 0
2025-08-28 17:45:23.639840:   - 未发现任何RFID标签
2025-08-28 17:45:23.639840: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:23.639840: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:24.137804: 🔄 开始RFID轮询检查...
2025-08-28 17:45:24.137804: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:24.137804: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:24.138802: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:24.138802:   - 设备句柄: 2402617358784
2025-08-28 17:45:24.138802:   - FetchRecords返回值: 0
2025-08-28 17:45:24.138802:   - 报告数量: 0
2025-08-28 17:45:24.139798: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:24.139798:   - 发现标签数量: 0
2025-08-28 17:45:24.139798:   - 未发现任何RFID标签
2025-08-28 17:45:24.139798: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:24.139798: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:24.637153: 🔄 开始RFID轮询检查...
2025-08-28 17:45:24.637153: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:24.637153: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:24.640143: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:24.640143:   - 设备句柄: 2402617358784
2025-08-28 17:45:24.640143:   - FetchRecords返回值: 0
2025-08-28 17:45:24.640143:   - 报告数量: 0
2025-08-28 17:45:24.641140: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:24.641140:   - 发现标签数量: 0
2025-08-28 17:45:24.641140:   - 未发现任何RFID标签
2025-08-28 17:45:24.641140: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:24.641140: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:25.138495: 🔄 开始RFID轮询检查...
2025-08-28 17:45:25.138495: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:25.138495: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:25.141485: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:25.141485:   - 设备句柄: 2402617358784
2025-08-28 17:45:25.141485:   - FetchRecords返回值: 0
2025-08-28 17:45:25.141485:   - 报告数量: 0
2025-08-28 17:45:25.141485: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:25.141485:   - 发现标签数量: 0
2025-08-28 17:45:25.141485:   - 未发现任何RFID标签
2025-08-28 17:45:25.142482: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:25.142482: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:25.637845: 🔄 开始RFID轮询检查...
2025-08-28 17:45:25.637845: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:25.637845: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:25.638841: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:25.638841:   - 设备句柄: 2402617358784
2025-08-28 17:45:25.638841:   - FetchRecords返回值: 0
2025-08-28 17:45:25.638841:   - 报告数量: 0
2025-08-28 17:45:25.638841: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:25.639838:   - 发现标签数量: 0
2025-08-28 17:45:25.639838:   - 未发现任何RFID标签
2025-08-28 17:45:25.639838: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:25.639838: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:26.138190: 🔄 开始RFID轮询检查...
2025-08-28 17:45:26.138190: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:26.138190: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:26.139187: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:26.139187:   - 设备句柄: 2402617358784
2025-08-28 17:45:26.139187:   - FetchRecords返回值: 0
2025-08-28 17:45:26.139187:   - 报告数量: 0
2025-08-28 17:45:26.140184: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:26.140184:   - 发现标签数量: 0
2025-08-28 17:45:26.140184:   - 未发现任何RFID标签
2025-08-28 17:45:26.140184: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:26.140184: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:26.637539: 🔄 开始RFID轮询检查...
2025-08-28 17:45:26.637539: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:26.637539: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:26.640529: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:26.640529:   - 设备句柄: 2402617358784
2025-08-28 17:45:26.640529:   - FetchRecords返回值: 0
2025-08-28 17:45:26.640529:   - 报告数量: 0
2025-08-28 17:45:26.641526: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:26.641526:   - 发现标签数量: 0
2025-08-28 17:45:26.641526:   - 未发现任何RFID标签
2025-08-28 17:45:26.641526: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:26.641526: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:27.093547: ⏰ 9秒约束超时！认证/检查必须在9秒内完成
2025-08-28 17:45:27.093547: 📊 当前流程状态：进馆=true, 出馆=false
2025-08-28 17:45:27.093547: ⏰ 9秒约束超时，重置状态等待下一次到位信号
2025-08-28 17:45:27.093547: 停止所有认证方式监听
2025-08-28 17:45:27.094544: 停止读者证认证监听
2025-08-28 17:45:27.094544: 停止读卡器认证监听
2025-08-28 17:45:27.094544: 已移除读卡器状态监听器
2025-08-28 17:45:27.094544: 已移除标签数据监听器
2025-08-28 17:45:27.094544: 所有卡片监听器已移除
2025-08-28 17:45:27.094544: 没有活跃的读卡器连接需要暂停
2025-08-28 17:45:27.094544: 读卡器认证监听已停止（连接保持）
2025-08-28 17:45:27.094544: 读者证认证服务监听已停止
2025-08-28 17:45:27.095541: 多认证管理器状态变更: idle
2025-08-28 17:45:27.095541: 所有认证方式监听已停止
2025-08-28 17:45:27.138399: 🔄 开始RFID轮询检查...
2025-08-28 17:45:27.138399: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:27.138399: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:27.139396: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:27.139396:   - 设备句柄: 2402617358784
2025-08-28 17:45:27.139396:   - FetchRecords返回值: 0
2025-08-28 17:45:27.139396:   - 报告数量: 0
2025-08-28 17:45:27.139396: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:27.140393:   - 发现标签数量: 0
2025-08-28 17:45:27.140393:   - 未发现任何RFID标签
2025-08-28 17:45:27.140393: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:27.140393: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:27.637748: 🔄 开始RFID轮询检查...
2025-08-28 17:45:27.637748: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:27.637748: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:27.638746: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:27.638746:   - 设备句柄: 2402617358784
2025-08-28 17:45:27.638746:   - FetchRecords返回值: 0
2025-08-28 17:45:27.638746:   - 报告数量: 0
2025-08-28 17:45:27.638746: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:27.638746:   - 发现标签数量: 0
2025-08-28 17:45:27.639742:   - 未发现任何RFID标签
2025-08-28 17:45:27.639742: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:27.639742: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:28.138616: 🔄 开始RFID轮询检查...
2025-08-28 17:45:28.138616: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:28.138616: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:28.139613: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:28.139613:   - 设备句柄: 2402617358784
2025-08-28 17:45:28.139613:   - FetchRecords返回值: 0
2025-08-28 17:45:28.139613:   - 报告数量: 0
2025-08-28 17:45:28.140610: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:28.140610:   - 发现标签数量: 0
2025-08-28 17:45:28.140610:   - 未发现任何RFID标签
2025-08-28 17:45:28.140610: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:28.140610: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:28.589157: 接收到数据: aa 00 65 80 00 00 07 2e
2025-08-28 17:45:28.589157: 🔍 接收到串口数据: aa 00 65 80 00 00 07 2e
2025-08-28 17:45:28.589157: 🔍 数据长度: 8 字节
2025-08-28 17:45:28.589157: 🔍 预定义命令列表:
2025-08-28 17:45:28.589157:   GateCommand.enterStart: aa 00 64 80 00 00 06 d2
2025-08-28 17:45:28.589157:   GateCommand.enterEnd: aa 00 65 80 00 00 07 2e
2025-08-28 17:45:28.590153:   GateCommand.exitStart: aa 00 c8 80 00 00 27 82
2025-08-28 17:45:28.590153:   GateCommand.exitEnd: aa 00 c9 80 00 00 26 7e
2025-08-28 17:45:28.590153:   GateCommand.reachPosition: aa 00 0a 80 00 00
2025-08-28 17:45:28.590153:   GateCommand.enterOpen: aa 00 02 01 00 00 48 72
2025-08-28 17:45:28.590153:   GateCommand.exitOpen: aa 00 01 01 00 00 48 36
2025-08-28 17:45:28.590153:   GateCommand.failSignal: aa 00 02 01 00 00 48 72
2025-08-28 17:45:28.590153:   GateCommand.tailgating: aa 00 0f 80 00 00
2025-08-28 17:45:28.590153:   GateCommand.doorHasPerson: aa 00 0b 80 00 00
2025-08-28 17:45:28.590153: ✅ 解析到闸机命令: GateCommand.enterEnd
2025-08-28 17:45:28.590153: 解析到闸机命令: enter_end (进馆结束)
2025-08-28 17:45:28.591150: 收到闸机命令: enter_end (进馆结束)
2025-08-28 17:45:28.591150: 进馆流程结束
2025-08-28 17:45:28.591150: 闸机状态变更: GateState.enterScanning -> GateState.idle
2025-08-28 17:45:28.591150: 闸机状态更新: GateState.enterScanning -> GateState.idle
2025-08-28 17:45:28.591150: ⏰ 取消9秒约束计时器
2025-08-28 17:45:28.591150: 📊 流程状态已清除：进馆=false, 出馆=false
2025-08-28 17:45:28.591150: [channel_1] 收到闸机事件: state_changed
2025-08-28 17:45:28.592147: 📨 收到GateCoordinator事件: state_changed
2025-08-28 17:45:28.592147: 闸机状态变更: GateState.idle
2025-08-28 17:45:28.592147: 🎨 处理状态变更UI: idle
2025-08-28 17:45:28.592147: 页面状态变更: SilencePageState.welcome
2025-08-28 17:45:28.592147: [channel_1] 收到闸机事件: enter_end
2025-08-28 17:45:28.592147: 📨 收到GateCoordinator事件: enter_end
2025-08-28 17:45:28.592147: 页面状态变更: SilencePageState.welcome
2025-08-28 17:45:28.636998: 🔄 开始RFID轮询检查...
2025-08-28 17:45:28.636998: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:28.636998: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:28.639988: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:28.639988:   - 设备句柄: 2402617358784
2025-08-28 17:45:28.639988:   - FetchRecords返回值: 0
2025-08-28 17:45:28.639988:   - 报告数量: 0
2025-08-28 17:45:28.639988: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:28.640985:   - 发现标签数量: 0
2025-08-28 17:45:28.640985:   - 未发现任何RFID标签
2025-08-28 17:45:28.640985: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:28.640985: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:29.137344: 🔄 开始RFID轮询检查...
2025-08-28 17:45:29.137344: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:29.137344: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:29.140334: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:29.140334:   - 设备句柄: 2402617358784
2025-08-28 17:45:29.140334:   - FetchRecords返回值: 0
2025-08-28 17:45:29.140334:   - 报告数量: 0
2025-08-28 17:45:29.140334: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:29.141331:   - 发现标签数量: 0
2025-08-28 17:45:29.141331:   - 未发现任何RFID标签
2025-08-28 17:45:29.141331: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:29.141331: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:29.638687: 🔄 开始RFID轮询检查...
2025-08-28 17:45:29.639683: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:29.639683: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:29.640681: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:29.640681:   - 设备句柄: 2402617358784
2025-08-28 17:45:29.641677:   - FetchRecords返回值: 0
2025-08-28 17:45:29.641677:   - 报告数量: 0
2025-08-28 17:45:29.641677: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:29.641677:   - 发现标签数量: 0
2025-08-28 17:45:29.641677:   - 未发现任何RFID标签
2025-08-28 17:45:29.641677: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:29.642678: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:30.137603: 🔄 开始RFID轮询检查...
2025-08-28 17:45:30.137603: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:30.137603: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:30.140594: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:30.140594:   - 设备句柄: 2402617358784
2025-08-28 17:45:30.140594:   - FetchRecords返回值: 0
2025-08-28 17:45:30.141591:   - 报告数量: 0
2025-08-28 17:45:30.141591: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:30.141591:   - 发现标签数量: 0
2025-08-28 17:45:30.141591:   - 未发现任何RFID标签
2025-08-28 17:45:30.141591: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:30.141591: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:30.637949: 🔄 开始RFID轮询检查...
2025-08-28 17:45:30.637949: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:30.637949: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:30.638946: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:30.638946:   - 设备句柄: 2402617358784
2025-08-28 17:45:30.638946:   - FetchRecords返回值: 0
2025-08-28 17:45:30.638946:   - 报告数量: 0
2025-08-28 17:45:30.638946: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:30.638946:   - 发现标签数量: 0
2025-08-28 17:45:30.639944:   - 未发现任何RFID标签
2025-08-28 17:45:30.639944: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:30.639944: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:31.137301: 🔄 开始RFID轮询检查...
2025-08-28 17:45:31.138295: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:31.138295: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:31.140288: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:31.141287:   - 设备句柄: 2402617358784
2025-08-28 17:45:31.141287:   - FetchRecords返回值: 0
2025-08-28 17:45:31.141287:   - 报告数量: 0
2025-08-28 17:45:31.141287: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:31.141287:   - 发现标签数量: 0
2025-08-28 17:45:31.141287:   - 未发现任何RFID标签
2025-08-28 17:45:31.142281: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:31.142281: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:31.638641: 🔄 开始RFID轮询检查...
2025-08-28 17:45:31.638641: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:31.638641: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:31.639637: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:31.639637:   - 设备句柄: 2402617358784
2025-08-28 17:45:31.639637:   - FetchRecords返回值: 0
2025-08-28 17:45:31.639637:   - 报告数量: 0
2025-08-28 17:45:31.639637: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:31.640634:   - 发现标签数量: 0
2025-08-28 17:45:31.640634:   - 未发现任何RFID标签
2025-08-28 17:45:31.640634: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:31.640634: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:32.137989: 🔄 开始RFID轮询检查...
2025-08-28 17:45:32.137989: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:32.137989: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:32.138987: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:32.138987:   - 设备句柄: 2402617358784
2025-08-28 17:45:32.138987:   - FetchRecords返回值: 0
2025-08-28 17:45:32.138987:   - 报告数量: 0
2025-08-28 17:45:32.139983: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:32.139983:   - 发现标签数量: 0
2025-08-28 17:45:32.139983:   - 未发现任何RFID标签
2025-08-28 17:45:32.139983: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:32.139983: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:32.638336: 🔄 开始RFID轮询检查...
2025-08-28 17:45:32.638336: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:32.638336: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:32.639332: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:32.640330:   - 设备句柄: 2402617358784
2025-08-28 17:45:32.640330:   - FetchRecords返回值: 0
2025-08-28 17:45:32.640330:   - 报告数量: 0
2025-08-28 17:45:32.641326: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:32.641326:   - 发现标签数量: 0
2025-08-28 17:45:32.641326:   - 未发现任何RFID标签
2025-08-28 17:45:32.641326: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:32.642324: 🚫 LSGate未检测到任何RFID标签
2025-08-28 17:45:33.138216: 🔄 开始RFID轮询检查...
2025-08-28 17:45:33.138216: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-28 17:45:33.138216: ⚠️ tagList为空，场上无标签
2025-08-28 17:45:33.139213: 🔍 LSGate硬件扫描详情:
2025-08-28 17:45:33.139213:   - 设备句柄: 2402617358784
2025-08-28 17:45:33.139213:   - FetchRecords返回值: 0
2025-08-28 17:45:33.140210:   - 报告数量: 0
2025-08-28 17:45:33.140210: 📊 LSGate扫描结果汇总:
2025-08-28 17:45:33.140210:   - 发现标签数量: 0
2025-08-28 17:45:33.140210:   - 未发现任何RFID标签
2025-08-28 17:45:33.140210: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-28 17:45:33.140210: 🚫 LSGate未检测到任何RFID标签
