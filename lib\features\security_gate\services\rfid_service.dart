import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:seasetting/seasetting.dart' as seasetting;
import 'package:hardware/hardware.dart';

import '../models/book_info.dart';

import 'enhanced_rfid_service.dart';
import 'master_slave_extension.dart';

/// 🔥 新增：主从机配置类
class MasterSlaveConfig {
  final String channelId;
  final bool isMaster;
  final String? slaveAddress;
  final String? masterAddress;
  final int port;

  MasterSlaveConfig({
    required this.channelId,
    required this.isMaster,
    this.slaveAddress,
    this.masterAddress,
    required this.port,
  });
}

/// RFID扫描服务
/// 🔥 增强版：集成书籍信息获取功能
class RFIDService {
  static RFIDService? _instance;
  static RFIDService get instance => _instance ??= RFIDService._();
  RFIDService._();

  // 🔥 新增：增强RFID服务
  final EnhancedRFIDService _enhancedService = EnhancedRFIDService.instance;



  // 扫描状态
  bool _isScanning = false;
  bool _isInitialized = false;

  // 扫描结果
  final List<String> _scannedBarcodes = [];

  // 🚫 移除缓存：书籍信息需要实时获取，确保借阅状态准确性

  // 事件流
  final StreamController<String> _barcodeController =
      StreamController<String>.broadcast();
  Stream<String> get barcodeStream => _barcodeController.stream;

  final StreamController<int> _countController =
      StreamController<int>.broadcast();
  Stream<int> get countStream => _countController.stream;

  final StreamController<String> _errorController =
      StreamController<String>.broadcast();
  Stream<String> get errorStream => _errorController.stream;

  // 🔥 新增：书籍扫描结果流
  final StreamController<BookScanResult> _bookResultController =
      StreamController<BookScanResult>.broadcast();
  Stream<BookScanResult> get bookResultStream => _bookResultController.stream;


  
  /// 🔥 增强：初始化RFID服务（集成书籍信息获取）
  Future<void> initialize() async {
    if (_isInitialized) {
      debugPrint('RFID服务已经初始化');
      return;
    }

    try {
      debugPrint('开始初始化增强RFID服务...');

      // 🔥 初始化增强RFID服务（包含持续扫描启动）
      await _enhancedService.initialize();

      // 🔥 监听增强服务的书籍扫描结果
      _enhancedService.bookResultStream.listen((result) {
        _onBookScanResult(result);
      });

      // 🔥 监听增强服务的条码流
      _enhancedService.barcodeStream.listen((barcode) {
        _barcodeController.add(barcode);
      });

      // 🔥 监听增强服务的计数流
      _enhancedService.countStream.listen((count) {
        _countController.add(count);
      });

      _isInitialized = true;
      debugPrint('增强RFID服务初始化完成，持续扫描已启动');

      // 🔥 新增：主机模式下立即启动数据收集，让数据持续进入共享池
      await _startContinuousDataCollectionIfMaster();
    } catch (e) {
      final errorMsg = '增强RFID服务初始化失败: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
      rethrow;
    }
  }

  /// 🔥 强化：主机模式下启动持续数据收集（强化轮询机制）
  Future<void> _startContinuousDataCollectionIfMaster() async {
    try {
      // 🔥 关键修复：直接从数据库读取配置，不依赖扩展初始化状态
      final config = await _loadMasterSlaveConfigFromDatabase();
      if (config != null && !config.isMaster) {
        debugPrint('⚠️ 从机模式：跳过持续数据收集，等待主机数据同步');
        debugPrint('📋 从机配置: 通道=${config.channelId}, 主机地址=${config.masterAddress}');
        return;
      }

      // 主机模式：启动持续数据收集
      debugPrint('🚀 主机模式：启动持续数据收集，数据将持续进入共享池');
      debugPrint('🎯 关键修复：使用轮询机制确保标签持续被发现');

      // 🔥 关键修复：先清空RFID缓冲区，但保持tagList
      debugPrint('🧹 清空RFID缓冲区（保持tagList），确保数据收集正常工作...');
      await _enhancedService.clearScanBuffer();

      // 启动数据收集（包含轮询机制）
      debugPrint('🚀 启动增强数据收集（事件监听 + 轮询备用）...');
      await _enhancedService.startDataCollection();

      // 🔥 验证轮询机制是否正常工作
      await Future.delayed(const Duration(milliseconds: 1000));
      debugPrint('🔍 验证数据收集状态...');

      final tagProvider = Get.context?.read<HWTagProvider>();
      if (tagProvider != null) {
        final currentTags = tagProvider.tagList;
        debugPrint('📊 当前tagList: ${currentTags.length}个标签');
        if (currentTags.isNotEmpty) {
          debugPrint('✅ 发现现有标签，轮询机制应该能够处理');
        } else {
          debugPrint('⚠️ tagList为空，等待RFID硬件扫描到标签');
        }
      }

      debugPrint('✅ 主机持续数据收集已启动，共享池将持续接收RFID数据');
      debugPrint('🔄 轮询机制每500ms检查一次tagList，确保标签不会丢失');
    } catch (e) {
      debugPrint('❌ 启动持续数据收集失败: $e，将在出馆时重试');
      // 不抛出异常，允许系统继续运行
    }
  }

  /// 🔥 新增：从数据库加载主从机配置
  Future<MasterSlaveConfig?> _loadMasterSlaveConfigFromDatabase() async {
    try {
      // 🔥 使用与GateConfigService相同的逻辑读取配置
      final settingProvider = Get.context?.read<seasetting.SettingProvider>();

      if (settingProvider != null) {
        final configs = await settingProvider.getMasterSlaveConfigs();

        if (configs.isNotEmpty) {
          // 使用最新的配置（最后一个）
          final seasettingConfig = configs.last;

          // 转换为本地配置格式
          final config = MasterSlaveConfig(
            channelId: seasettingConfig.channelId,
            isMaster: seasettingConfig.isMaster,
            slaveAddress: seasettingConfig.slaveAddress,
            masterAddress: seasettingConfig.masterAddress,
            port: seasettingConfig.port,
          );

          debugPrint('📋 从数据库读取主从机配置: ${config.channelId}');
          debugPrint('📋 配置详情: ${config.isMaster ? "主机" : "从机"}模式');
          return config;
        } else {
          debugPrint('📋 数据库中没有找到主从机配置，按主机模式运行');
        }
      } else {
        debugPrint('📋 无法获取SettingProvider，按主机模式运行');
      }

      return null;
    } catch (e) {
      debugPrint('⚠️ 读取主从机配置失败: $e，按主机模式运行');
      return null;
    }
  }

  /// 🔥 新增：处理书籍扫描结果
  void _onBookScanResult(BookScanResult result) {
    // 转发书籍扫描结果（不再缓存，确保数据实时性）
    _bookResultController.add(result);

    // 简化日志输出
    if (result.status == BookScanStatus.success && result.bookInfo != null) {
      debugPrint('书籍: ${result.barcode} - ${result.bookInfo!.bookName}');
    } else if (result.status == BookScanStatus.notFound) {
      debugPrint('未找到: ${result.barcode}');
    }
  }

  /// 🔥 修改：开始数据收集（不启动硬件扫描）
  Future<void> startDataCollection() async {
    if (!_isInitialized) {
      throw Exception('RFID服务未初始化');
    }

    // 🔥 新增：检查从机模式
    final masterSlaveExtension = MasterSlaveExtension.instance;
    if (masterSlaveExtension.isEnabled && !masterSlaveExtension.isMaster) {
      debugPrint('⚠️ 从机模式：跳过本地RFID数据收集，等待主机数据同步');
      _isScanning = true; // 标记为收集状态，但不启动实际收集
      return;
    }

    if (_isScanning) {
      debugPrint('🔄 主机RFID数据收集已在进行中，重置防重复机制');
      // 🔥 重要：主机模式下重置防重复机制，让轮询重新发现标签
      await _enhancedService.startDataCollection(); // 这会重置防重复机制
      return;
    }

    try {
      debugPrint('开始RFID数据收集...');

      // 清空之前的扫描结果
      _scannedBarcodes.clear();

      // 🔥 使用增强RFID服务开始数据收集
      await _enhancedService.startDataCollection();

      _isScanning = true;

      debugPrint('RFID数据收集已启动');
    } catch (e) {
      final errorMsg = '启动RFID数据收集失败: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
      rethrow;
    }
  }

  /// 🔥 保留原方法以兼容现有代码
  @Deprecated('请使用startDataCollection()方法')
  Future<void> startScanning() async {
    await startDataCollection();
  }
  
  /// 🔥 修改：停止数据收集（不停止硬件扫描）
  Future<List<String>> stopDataCollection() async {
    if (!_isScanning) {
      debugPrint('RFID数据收集未在进行中');
      return List.from(_scannedBarcodes);
    }

    // 🔥 新增：检查从机模式
    final masterSlaveExtension = MasterSlaveExtension.instance;
    if (masterSlaveExtension.isEnabled && !masterSlaveExtension.isMaster) {
      debugPrint('⚠️ 从机模式：停止数据收集，返回空结果（数据来自主机）');
      _isScanning = false;
      return []; // 从机模式返回空列表，数据由主从机同步机制处理
    }

    try {
      debugPrint('停止RFID数据收集...');

      // 🔥 使用增强RFID服务停止数据收集
      final result = await _enhancedService.stopDataCollection();

      _isScanning = false;

      debugPrint('RFID数据收集已停止，共收集到${result.length}个条码');

      return result;
    } catch (e) {
      final errorMsg = '停止RFID数据收集失败: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
      return List<String>.from(_scannedBarcodes);
    }
  }

  /// 🔥 保留原方法以兼容现有代码
  @Deprecated('请使用stopDataCollection()方法')
  Future<List<String>> stopScanning() async {
    return await stopDataCollection();
  }

  /// 🔥 新增：清空扫描缓冲区
  Future<void> clearScanBuffer() async {
    try {
      await _enhancedService.clearScanBuffer();
    } catch (e) {
      debugPrint('清空扫描缓冲区失败: $e');
    }
  }

  /// 🔥 新增：重置已处理条码集合（供主从机扩展调用）
  void resetProcessedBarcodes() {
    try {
      _enhancedService.resetProcessedBarcodes();
    } catch (e) {
      debugPrint('重置已处理条码集合失败: $e');
    }
  }


  
  /// 获取当前扫描结果
  List<String> getCurrentScanResult() {
    return List<String>.from(_scannedBarcodes);
  }

  /// 🔥 新增：获取所有已扫描书籍的详细信息（实时获取）
  Future<List<BookInfo>> getAllScannedBooksInfo() async {
    return await _enhancedService.getAllScannedBooksInfo();
  }

  /// 🔥 新增：获取单个条码的书籍信息（实时获取，不缓存）
  Future<BookInfo?> getSingleBookInfoRealTime(String barcode) async {
    if (!_isInitialized) {
      throw Exception('RFID服务未初始化');
    }

    debugPrint('实时获取单个书籍信息: $barcode');

    // 直接使用SIP2服务获取书籍信息
    return await _enhancedService.getSingleBookInfoRealTime(barcode);
  }

  /// 🚫 缓存已移除：书籍信息需要实时获取
  @Deprecated('缓存已移除，请使用getAllScannedBooksInfo()获取实时数据')
  Map<String, BookInfo> get bookInfoCache => {};

  /// 获取扫描数量
  int get scannedCount => _scannedBarcodes.length;
  
  /// 是否正在扫描
  bool get isScanning => _isScanning;
  
  /// 是否已初始化
  bool get isInitialized => _isInitialized;
  
  /// 🔥 增强：清空扫描结果
  void clearScanResult() {
    _scannedBarcodes.clear();
    _enhancedService.clearScanResult();
    _countController.add(0);
  }
  
  /// 手动添加条码（用于测试）
  void addBarcode(String barcode) {
    if (!_scannedBarcodes.contains(barcode)) {
      _scannedBarcodes.add(barcode);
      _barcodeController.add(barcode);
      _countController.add(_scannedBarcodes.length);
      debugPrint('手动添加条码: $barcode');
    }
  }
  

  
  /// 获取服务状态
  Map<String, dynamic> getStatus() {
    return {
      'initialized': _isInitialized,
      'scanning': _isScanning,
      'scanned_count': _scannedBarcodes.length,
      'scanned_barcodes': List.from(_scannedBarcodes),
      'service_name': 'RFIDService',
      'version': '1.0.0',
    };
  }
  
  /// 🔥 增强：测试RFID设备连接
  Future<bool> testConnection() async {
    try {
      // 🔥 使用增强RFID服务测试连接
      return await _enhancedService.testConnection();
    } catch (e) {
      debugPrint('增强RFID设备连接测试失败: $e');
      return false;
    }
  }
  
  /// 🔥 增强：重置服务
  Future<void> reset() async {
    debugPrint('重置增强RFID服务');

    if (_isScanning) {
      await stopScanning();
    }

    _scannedBarcodes.clear();
    await _enhancedService.reset();
    _countController.add(0);
  }
  
  /// 🔥 增强：释放资源
  void dispose() {
    debugPrint('释放增强RFID服务资源');

    _isScanning = false;
    _isInitialized = false;

    _barcodeController.close();
    _countController.close();
    _errorController.close();
    _bookResultController.close();

    _enhancedService.dispose();

    debugPrint('增强RFID服务已释放');
  }
}
