# 根据标签UID查询条码

## OpenAPI Specification

```yaml
openapi: 3.0.1
info:
  title: ''
  description: ''
  version: 1.0.0
paths:
  /tunano/rfdc/tag/v1/rfdc/tag/booktag/LibraryCode/{libraryCode}/Uid/{Uid}:
    get:
      summary: 根据标签UID查询条码
      deprecated: false
      description: ''
      tags:
        - 设备接口
      parameters:
        - name: libraryCode
          in: path
          description: 馆代码
          required: true
          example: ''
          schema:
            type: string
        - name: Uid
          in: path
          description: 标签UID/TID
          required: true
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                  requestObject:
                    type: string
                  operation:
                    type: string
                  errorCode:
                    type: integer
                    description: 0成功,其他失败
                  message:
                    type: string
                    description: 提示信息
                  result:
                    type: object
                    properties:
                      libraryCode:
                        type: string
                        description: 馆代码
                      uid:
                        type: string
                        description: UID/TID
                      barcode:
                        type: string
                        description: 条码号
                      barcodeRaw:
                        type: 'null'
                        description: EPC
                      userRaw:
                        type: 'null'
                        description: UserData
                      updated:
                        type: string
                        description: 更新时间
                      created:
                        type: string
                        description: 创建时间
                    required:
                      - libraryCode
                      - uid
                      - barcode
                      - barcodeRaw
                      - userRaw
                      - updated
                      - created
                    x-apifox-orders:
                      - libraryCode
                      - uid
                      - barcode
                      - barcodeRaw
                      - userRaw
                      - updated
                      - created
                required:
                  - id
                  - requestObject
                  - operation
                  - errorCode
                  - message
                  - result
                x-apifox-orders:
                  - id
                  - requestObject
                  - operation
                  - errorCode
                  - message
                  - result
              example:
                id: '2024332210064729201095'
                requestObject: rfdc.tag
                operation: getbooktagbyuid
                errorCode: 0
                message: 查询成功
                result:
                  libraryCode: CN-518000-HHLIB
                  uid: '12345767676'
                  barcode: '12345678901'
                  barcodeRaw: null
                  userRaw: null
                  updated: '2024-05-30 12:00:11'
                  created: '2024-05-30 12:00:11'
          headers: {}
          x-apifox-name: 成功
      security: []
      x-apifox-folder: 设备接口
      x-apifox-status: released
      x-run-in-apifox: https://app.apifox.com/web/project/2805692/apis/api-186703751-run
components:
  schemas: {}
  securitySchemes: {}
servers:
  - url: http://************:9000
    description: 开发环境
security: []

```