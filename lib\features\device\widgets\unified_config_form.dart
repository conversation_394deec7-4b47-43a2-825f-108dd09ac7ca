import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/utils/window_util.dart';
import '../../../core/widgets/custom_button.dart';
import '../view_models/device_view_model.dart';

/// 统一API配置表单
/// 用于修改所有API相关的配置项
class UnifiedConfigForm extends StatefulWidget {
  const UnifiedConfigForm({Key? key}) : super(key: key);

  @override
  State<UnifiedConfigForm> createState() => _UnifiedConfigFormState();
}

class _UnifiedConfigFormState extends State<UnifiedConfigForm> {
  final _formKey = GlobalKey<FormState>();
  final _authBaseUrlController = TextEditingController();  // 认证相关接口baseurl
  final _bookBaseUrlController = TextEditingController();  // 图书相关接口baseurl
  final _libraryCodeController = TextEditingController();
  final _deviceMacController = TextEditingController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final viewModel = context.read<DeviceViewModel>();
      _authBaseUrlController.text = viewModel.authBaseUrl;
      _bookBaseUrlController.text = viewModel.bookBaseUrl;
      _libraryCodeController.text = viewModel.libraryCode;
      _deviceMacController.text = viewModel.deviceMac;
    });
  }

  @override
  void dispose() {
    _authBaseUrlController.dispose();
    _bookBaseUrlController.dispose();
    _libraryCodeController.dispose();
    _deviceMacController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<DeviceViewModel>(
      builder: (context, viewModel, child) {
        return Card(
          margin: EdgeInsets.all(16.p),
          child: Padding(
            padding: EdgeInsets.all(16.p),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 标题
                  Text(
                    '统一API配置',
                    style: TextStyle(
                      fontSize: 20.p,
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF333333),
                    ),
                  ),
                  SizedBox(height: 16.p),
                  
                  // 基础URL配置
                  _buildConfigSection(
                    title: '基础配置',
                    children: [
                      // 认证接口地址
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildTextField(
                            controller: _authBaseUrlController,
                            label: '认证接口地址',
                            hint: 'http://172.16.0.118:9000',
                            validator: (value) {
                              if (value?.isEmpty ?? true) {
                                return '认证接口地址不能为空';
                              }
                              if (!value!.startsWith('http')) {
                                return '接口地址必须以http开头';
                              }
                              return null;
                            },
                          ),
                          Padding(
                            padding: EdgeInsets.only(left: 12.p, top: 4.p),
                            child: Text(
                              '用于读者认证、上传状态、设备注册',
                              style: TextStyle(
                                fontSize: 12.p,
                                color: const Color(0xFF666666),
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 12.p),
                      // 图书接口地址
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildTextField(
                            controller: _bookBaseUrlController,
                            label: '图书接口地址',
                            hint: 'http://166.111.120.166:9000',
                            validator: (value) {
                              if (value?.isEmpty ?? true) {
                                return '图书接口地址不能为空';
                              }
                              if (!value!.startsWith('http')) {
                                return '接口地址必须以http开头';
                              }
                              return null;
                            },
                          ),
                          Padding(
                            padding: EdgeInsets.only(left: 12.p, top: 4.p),
                            child: Text(
                              '用于白名单检查、UID转条码、获取图书信息',
                              style: TextStyle(
                                fontSize: 12.p,
                                color: const Color(0xFF666666),
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 12.p),
                      _buildTextField(
                        controller: _libraryCodeController,
                        label: '图书馆代码',
                        hint: 'CN-518000-HHLIB',
                        validator: (value) {
                          if (value?.isEmpty ?? true) {
                            return '图书馆代码不能为空';
                          }
                          return null;
                        },
                      ),
                      SizedBox(height: 12.p),
                      _buildTextField(
                        controller: _deviceMacController,
                        label: '设备MAC地址',
                        hint: 'FF-FF-FF-FF-FF-FF',
                        validator: (value) {
                          if (value?.isEmpty ?? true) {
                            return '设备MAC地址不能为空';
                          }
                          return null;
                        },
                      ),
                    ],
                  ),
                  
                  SizedBox(height: 24.p),
                  
                  // 操作按钮
                  Row(
                    children: [
                      Expanded(
                        child: CustomButton.filled(
                          text: '保存配置',
                          onTap: _saveConfig,
                          disabled: viewModel.isLoading,
                        ),
                      ),
                      SizedBox(width: 12.p),
                      Expanded(
                        child: CustomButton.outline(
                          text: '重置默认',
                          onTap: () => _showResetDialog(viewModel),
                          disabled: viewModel.isLoading,
                        ),
                      ),
                    ],
                  ),
                  
                  SizedBox(height: 16.p),
                  
                  // 测试连接按钮
                  SizedBox(
                    width: double.infinity,
                    child: CustomButton.outline(
                      text: viewModel.isLoading ? '测试中...' : '测试连接',
                      onTap: viewModel.isLoading ? null : () {
                        viewModel.testConnection();
                      },
                      disabled: viewModel.isLoading,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildConfigSection({
    required String title,
    required List<Widget> children,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 16.p,
            fontWeight: FontWeight.w600,
            color: const Color(0xFF495057),
          ),
        ),
        SizedBox(height: 8.p),
        Container(
          padding: EdgeInsets.all(16.p),
          decoration: BoxDecoration(
            color: const Color(0xFFF8F9FA),
            borderRadius: BorderRadius.circular(8.p),
            border: Border.all(color: const Color(0xFFE9ECEF)),
          ),
          child: Column(
            children: children,
          ),
        ),
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.p),
        ),
        contentPadding: EdgeInsets.symmetric(
          horizontal: 12.p,
          vertical: 8.p,
        ),
      ),
      validator: validator,
    );
  }

  void _saveConfig() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final viewModel = context.read<DeviceViewModel>();

    // 保存认证接口URL
    if (_authBaseUrlController.text != viewModel.authBaseUrl) {
      await viewModel.updateAuthBaseUrl(_authBaseUrlController.text);
    }

    // 保存图书接口URL
    if (_bookBaseUrlController.text != viewModel.bookBaseUrl) {
      await viewModel.updateBookBaseUrl(_bookBaseUrlController.text);
    }
    
    // 保存图书馆代码
    if (_libraryCodeController.text != viewModel.libraryCode) {
      await viewModel.updateLibraryCode(_libraryCodeController.text);
    }
    
    // 保存设备MAC地址
    if (_deviceMacController.text != viewModel.deviceMac) {
      await viewModel.updateDeviceMac(_deviceMacController.text);
    }
  }

  void _showResetDialog(DeviceViewModel viewModel) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('重置配置'),
        content: const Text('确定要重置为默认配置吗？这将覆盖所有当前设置。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              viewModel.resetToDefault();
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
}
