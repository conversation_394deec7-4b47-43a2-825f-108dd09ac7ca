import 'package:flutter/foundation.dart';
import '../../security_gate/models/gate_state.dart';

/// 闸机上下文服务
/// 负责感知当前闸机状态，自动判断进出类型
class GateContextService {
  static GateContextService? _instance;
  static GateContextService get instance => _instance ??= GateContextService._();
  GateContextService._();

  // 当前闸机状态
  GateState _currentState = GateState.idle;
  
  // 状态监听器
  final List<Function(GateState)> _stateListeners = [];

  /// 更新闸机状态
  void updateGateState(GateState newState) {
    if (_currentState != newState) {
      final oldState = _currentState;
      _currentState = newState;
      
      debugPrint('闸机状态更新: $oldState -> $newState');
      
      // 通知所有监听器
      for (final listener in _stateListeners) {
        try {
          listener(newState);
        } catch (e) {
          debugPrint('闸机状态监听器执行失败: $e');
        }
      }
    }
  }

  /// 获取当前闸机状态
  GateState get currentState => _currentState;

  /// 判断当前是否为进馆流程
  bool get isEnterFlow {
    return _currentState.isEnterState;
  }

  /// 判断当前是否为出馆流程
  bool get isExitFlow {
    return _currentState.isExitState;
  }

  /// 获取认证类型（1=进馆, 2=出馆）
  int get authType {
    if (isEnterFlow) {
      return 1; // 进馆
    } else if (isExitFlow) {
      return 2; // 出馆
    } else {
      // 默认情况，可能需要根据具体业务逻辑调整
      debugPrint('警告：无法确定认证类型，当前状态: $_currentState');
      return 1; // 默认为进馆
    }
  }

  /// 获取认证类型描述
  String get authTypeDescription {
    switch (authType) {
      case 1:
        return '进馆';
      case 2:
        return '出馆';
      default:
        return '未知';
    }
  }

  /// 添加状态监听器
  void addStateListener(Function(GateState) listener) {
    _stateListeners.add(listener);
  }

  /// 移除状态监听器
  void removeStateListener(Function(GateState) listener) {
    _stateListeners.remove(listener);
  }

  /// 清除所有监听器
  void clearListeners() {
    _stateListeners.clear();
  }

  /// 检查是否可以进行认证
  bool canAuthenticate() {
    switch (_currentState) {
      case GateState.enterStarted:
      case GateState.enterScanning:
      case GateState.exitStarted:
        return true;
      default:
        return false;
    }
  }

  /// 获取当前状态的认证提示信息
  String getAuthPrompt() {
    switch (_currentState) {
      case GateState.enterStarted:
      case GateState.enterScanning:
        return '请进行身份认证以进入';
      case GateState.exitStarted:
        return '请进行身份认证以离开';
      case GateState.idle:
        return '系统待机中';
      case GateState.enterOpening:
        return '认证成功，请通过';
      case GateState.exitScanning:
        return '正在扫描随身物品';
      case GateState.exitChecking:
        return '正在检查书籍状态';
      case GateState.error:
        return '系统异常，请联系管理员';
      default:
        return '请等待系统准备就绪';
    }
  }

  /// 检查是否需要获取用户详细信息
  bool shouldFetchUserInfo() {
    // 进馆流程通常需要显示用户信息
    if (isEnterFlow) {
      return true;
    }
    
    // 出馆流程可能不需要详细用户信息，只需要验证身份
    if (isExitFlow) {
      return false;
    }
    
    // 其他情况默认获取
    return true;
  }

  /// 获取适合当前状态的超时时间
  Duration getAuthTimeout() {
    switch (_currentState) {
      case GateState.enterStarted:
      case GateState.enterScanning:
        return const Duration(seconds: 30); // 进馆认证超时
      case GateState.exitStarted:
        return const Duration(seconds: 15); // 出馆认证超时（可以更短）
      default:
        return const Duration(seconds: 10); // 默认超时
    }
  }

  /// 重置服务状态
  void reset() {
    _currentState = GateState.idle;
    debugPrint('闸机上下文服务已重置');
  }

  /// 获取服务状态
  Map<String, dynamic> getStatus() {
    return {
      'current_state': _currentState.name,
      'state_display_name': _currentState.displayName,
      'is_enter_flow': isEnterFlow,
      'is_exit_flow': isExitFlow,
      'auth_type': authType,
      'auth_type_description': authTypeDescription,
      'can_authenticate': canAuthenticate(),
      'should_fetch_user_info': shouldFetchUserInfo(),
      'listeners_count': _stateListeners.length,
      'service_name': 'GateContextService',
      'version': '1.0.0',
    };
  }
}
