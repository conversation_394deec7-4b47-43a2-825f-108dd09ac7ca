/// 设备管理模块
/// 
/// 该模块提供设备注册和状态上传功能，包括：
/// - DeviceRegisterModel: 设备注册数据模型
/// - DeviceStatusModel: 设备状态数据模型
/// - DeviceApiService: 设备API服务
/// - DeviceConfigManager: 设备配置管理器
/// - DeviceViewModel: 设备业务逻辑
/// - DevicePage: 设备管理页面

// 导出模型
export 'models/device_model.dart';

// 导出服务
export 'services/device_api_service.dart';
export 'services/device_config_manager.dart';
export 'services/unified_api_config_manager.dart';

// 导出视图模型
export 'view_models/device_view_model.dart';

// 导出页面
export 'views/device_page.dart';
export 'views/device_view.dart';

// 导出组件
export 'widgets/api_config_panel.dart';
export 'widgets/device_register_form.dart';
export 'widgets/device_status_form.dart';
export 'widgets/reader_auth_form.dart';
export 'widgets/unified_config_form.dart';
