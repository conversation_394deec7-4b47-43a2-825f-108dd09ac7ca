2025-08-26 16:35:51.551440: ⏰ 10秒数据收集完成，开始书籍检查... 时间: 2025-08-26 16:35:51.551440
2025-08-26 16:35:51.551440: 🛑 停止出馆数据收集，收集到UID数量: 0
2025-08-26 16:35:51.551440: 🔧 停止时间: 2025-08-26 16:35:51.551440
2025-08-26 16:35:51.552437: 📡 停止从共享池收集数据...
2025-08-26 16:35:51.552437: 🔧 保持RFID扫描运行，只停止从共享池收集数据
2025-08-26 16:35:51.552437: 📡 开始从RFID服务收集UID数据...
2025-08-26 16:35:51.552437: 📊 从HWTagProvider获取UID，标签数量: 2
2025-08-26 16:35:51.552437: 📡 收集到UID: E004015304F3DD22 (条码: null)
2025-08-26 16:35:51.552437: 📡 收集到UID: E004015305F68508 (条码: null)
2025-08-26 16:35:51.552437: 📡 从RFID服务收集到UID数量: 2
2025-08-26 16:35:51.552437: 📋 UID列表: E004015304F3DD22, E004015305F68508
2025-08-26 16:35:51.552437: 📊 数据收集完成，UID列表: [E004015304F3DD22, E004015305F68508]
2025-08-26 16:35:51.553434: 🔍 开始三步书籍检查，UID数量: 2
2025-08-26 16:35:51.553434: 闸机状态变更: GateState.exitScanning -> GateState.exitChecking
2025-08-26 16:35:51.553434: 闸机状态更新: GateState.exitScanning -> GateState.exitChecking
2025-08-26 16:35:51.553434: 🚀 开始完整的三步书籍检查流程，UID数量: 2
2025-08-26 16:35:51.553434: 🔍 第一步：检查白名单，UID数量: 2
2025-08-26 16:35:51.553434: 🔍 输入UID列表: [E004015304F3DD22, E004015305F68508]
2025-08-26 16:35:51.553434: [channel_1] 收到闸机事件: state_changed
2025-08-26 16:35:51.553434: 📨 收到GateCoordinator事件: state_changed
2025-08-26 16:35:51.553434: 闸机状态变更: GateState.exitChecking
2025-08-26 16:35:51.554430: 🎨 处理状态变更UI: exitChecking
2025-08-26 16:35:51.554430: 未处理的状态变更UI: exitChecking
2025-08-26 16:35:51.554430: 
2025-08-26 16:35:51.554430: 📤 ========== 白名单检查接口 ==========
2025-08-26 16:35:51.554430: 📤 接口名称: 查询图书是否在白名单
2025-08-26 16:35:51.555427: 📤 请求URL: http://166.111.121.100:9000/tunano/ldc/white/tag/whitelist/contain
2025-08-26 16:35:51.555427: 📤 请求方法: POST
2025-08-26 16:35:51.555427: 📤 请求头: Content-Type: application/json
2025-08-26 16:35:51.555427: 📤 请求参数: {"Taglist":[{"Tid":"E004015304F3DD22"},{"Tid":"E004015305F68508"}]}
2025-08-26 16:35:51.555427: 📤 请求参数解析:
2025-08-26 16:35:51.555427: 📤   - Taglist: 标签列表，包含2个UID
2025-08-26 16:35:51.555427: 📤     [0] Tid: E004015304F3DD22
2025-08-26 16:35:51.555427: 📤     [1] Tid: E004015305F68508
2025-08-26 16:35:51.555427: 📤 =====================================
2025-08-26 16:35:51.555427: 
2025-08-26 16:35:51.562404: 
2025-08-26 16:35:51.563402: 📥 ========== 白名单检查响应 ==========
2025-08-26 16:35:51.563402: 📥 响应状态码: 200
2025-08-26 16:35:51.563402: 📥 响应原始数据: {"errorcode":-1,"message":"白名单不包含标签E004015304F3DD22,E004015305F68508, 耗时0ms","result":null}
2025-08-26 16:35:51.563402: 
2025-08-26 16:35:51.563402: 📥 响应数据解析:
2025-08-26 16:35:51.563402: 📥   - errorcode: -1 (0=在白名单内, 其他=不在白名单内)
2025-08-26 16:35:51.564397: 📥   - message: 白名单不包含标签E004015304F3DD22,E004015305F68508, 耗时0ms
2025-08-26 16:35:51.564397: 📥   - result: null
2025-08-26 16:35:51.564397: 🔍 解析白名单错误消息: 白名单不包含标签E004015304F3DD22,E004015305F68508, 耗时0ms
2025-08-26 16:35:51.564397: 🔍 方法1提取到的UID: E004015304F3DD22
2025-08-26 16:35:51.564397: ⚠️ 白名单检查结果: 有书籍不在白名单内
2025-08-26 16:35:51.564397: ⚠️ 不在白名单的UID字符串: E004015304F3DD22
2025-08-26 16:35:51.564397: ⚠️ 返回值: E004015304F3DD22
2025-08-26 16:35:51.565394: 📥 =====================================
2025-08-26 16:35:51.565394: 
2025-08-26 16:35:51.565394: 🔍 第二步：UID转条码，UID数量: 1
2025-08-26 16:35:51.565394: 🔍 输入UID字符串: E004015304F3DD22
2025-08-26 16:35:51.565394: 🔍 解析后UID列表: [E004015304F3DD22]
2025-08-26 16:35:51.565394: 
2025-08-26 16:35:51.566391: 📤 ========== UID转条码接口 (1/1) ==========
2025-08-26 16:35:51.566391: 📤 接口名称: 根据标签UID查询条码
2025-08-26 16:35:51.566391: 📤 请求URL: http://166.111.121.100:9000/tunano/rfdc/tag/v1/rfdc/tag/booktag/LibraryCode/CN-100084-QHDXLIB/Uid/E004015304F3DD22
2025-08-26 16:35:51.566391: 📤 请求方法: GET
2025-08-26 16:35:51.566391: 📤 路径参数:
2025-08-26 16:35:51.566391: 📤   - libraryCode: 从URL中提取的馆代码
2025-08-26 16:35:51.566391: 📤   - Uid: E004015304F3DD22 (标签UID/TID)
2025-08-26 16:35:51.566391: 📤 =====================================
2025-08-26 16:35:51.566391: 
2025-08-26 16:35:51.585329: 
2025-08-26 16:35:51.585329: 📥 ========== UID转条码响应 (1/1) ==========
2025-08-26 16:35:51.585329: 📥 UID: E004015304F3DD22
2025-08-26 16:35:51.585329: 📥 响应状态码: 200
2025-08-26 16:35:51.586325: 📥 响应原始数据: {"id":"2025352616084348048846","requestObject":"rfdc.tag","operation":"getbooktagbyuid","errorCode":0,"message":"查询成功","result":{"libraryCode":"CN-100084-QHDXLIB","uid":"E004015304F3DD22","barcode":"C3695488J","barcodeRaw":null,"userRaw":null,"updated":"2025-03-06 12:25:09","created":"2025-03-06 12:25:09"}}
2025-08-26 16:35:51.586325: 
2025-08-26 16:35:51.586325: 📥 响应数据解析:
2025-08-26 16:35:51.586325: 📥   - id: 2025352616084348048846
2025-08-26 16:35:51.586325: 📥   - requestObject: rfdc.tag
2025-08-26 16:35:51.586325: 📥   - operation: getbooktagbyuid
2025-08-26 16:35:51.586325: 📥   - errorCode: 0 (0=成功, 其他=失败)
2025-08-26 16:35:51.587321: 📥   - message: 查询成功
2025-08-26 16:35:51.587321: 📥   - result:
2025-08-26 16:35:51.587321: 📥     * libraryCode: CN-100084-QHDXLIB
2025-08-26 16:35:51.587321: 📥     * uid: E004015304F3DD22
2025-08-26 16:35:51.587321: 📥     * barcode: C3695488J
2025-08-26 16:35:51.587321: 📥     * updated: 2025-03-06 12:25:09
2025-08-26 16:35:51.587321: 📥     * created: 2025-03-06 12:25:09
2025-08-26 16:35:51.588318: ✅ UID转条码成功: E004015304F3DD22 → C3695488J
2025-08-26 16:35:51.588318: 📥 =====================================
2025-08-26 16:35:51.588318: 
2025-08-26 16:35:51.588318: ✅ 第二步完成，获得条码数量: 1
2025-08-26 16:35:51.588318: ✅ 获得的条码列表: [C3695488J]
2025-08-26 16:35:51.588318: 🔍 第三步：获取图书信息，条码数量: 1
2025-08-26 16:35:51.588318: 🔍 输入条码列表: [C3695488J]
2025-08-26 16:35:51.589315: 
2025-08-26 16:35:51.589315: 📤 ========== 获取图书信息接口 (1/1) ==========
2025-08-26 16:35:51.589315: 📤 接口名称: 获取图书信息GET
2025-08-26 16:35:51.589315: 📤 请求URL: http://166.111.121.100:9000/tunano/cdc/acs/v1/cdc/acs/queryBookInfo?LibraryCode=CN-100084-QHDXLIB&Mac=FF-FF-FF-FF-FF-FF&BookSn=C3695488J
2025-08-26 16:35:51.589315: 📤 请求方法: GET
2025-08-26 16:35:51.589315: 📤 查询参数:
2025-08-26 16:35:51.589315: 📤   - barcode: C3695488J (图书条码)
2025-08-26 16:35:51.590311: 📤 =====================================
2025-08-26 16:35:51.590311: 
2025-08-26 16:35:51.774700: 
2025-08-26 16:35:51.774700: 📥 ========== 图书信息响应 (1/1) ==========
2025-08-26 16:35:51.774700: 📥 条码: C3695488J
2025-08-26 16:35:51.774700: 📥 响应状态码: 200
2025-08-26 16:35:51.774700: 📥 响应原始数据: {"id":"2025352616084352015786","requestObject":"cdc.acs","operation":"querybookinfo","errorCode":0,"message":"查询成功","result":{"bookSn":"C3695488J","bookTitle":"生命科学中的非决定论争论 . ＝ The indeterminism debate in the life sciences","author":null,"publisher":null,"isbn":null,"callNumber":null,"keywords":null,"permanentLocation":"主馆 - mn125-lib","currentLocation":"主馆","currentShelfLocation":null,"circulationStatus":1,"circulationStatusValue":"03","price":0.0,"page":0,"circulationType":0,"needReturnDate":null,"needPickUpDate":null,"borrowedDate":null,"borrowFlag":false,"borrowPastronSn":null,"bookingFlag":false,"bookingPatronSnList":[],"message":"查询成功"}}
2025-08-26 16:35:51.775697: 
2025-08-26 16:35:51.775697: 📥 响应数据解析:
2025-08-26 16:35:51.775697: 📥   - errorCode: 0 (0=成功, 其他=失败)
2025-08-26 16:35:51.775697: 📥   - message: 查询成功
2025-08-26 16:35:51.775697: 📥   - result:
2025-08-26 16:35:51.775697: 📥     * bookSn: C3695488J (图书条码)
2025-08-26 16:35:51.775697: 📥     * bookTitle: 生命科学中的非决定论争论 . ＝ The indeterminism debate in the life sciences (书名)
2025-08-26 16:35:51.775697: 📥     * author: null (作者)
2025-08-26 16:35:51.775697: 📥     * isbn: null (ISBN)
2025-08-26 16:35:51.775697: 📥     * callNumber: null (索书号)
2025-08-26 16:35:51.776693: 📥     * publisher: null (出版社)
2025-08-26 16:35:51.776693: 📥     * borrowFlag: false (借阅标志)
2025-08-26 16:35:51.776693: 📥     * bookingFlag: false (预约标志)
2025-08-26 16:35:51.776693: 📥     * circulationStatus: 1 (流通状态)
2025-08-26 16:35:51.776693: 📥     * message: 查询成功 (消息)
2025-08-26 16:35:51.776693: ✅ 图书信息获取成功: C3695488J → 生命科学中的非决定论争论 . ＝ The indeterminism debate in the life sciences
2025-08-26 16:35:51.776693: 📥 =====================================
2025-08-26 16:35:51.776693: 
2025-08-26 16:35:51.776693: ✅ 第三步完成，获得图书信息数量: 1
2025-08-26 16:35:51.777690: ✅ 获得的图书信息列表:
2025-08-26 16:35:51.777690: ✅   [1] C3695488J - 生命科学中的非决定论争论 . ＝ The indeterminism debate in the life sciences (未知作者)
2025-08-26 16:35:51.777690: ✅ 三步检查完成: 检测到 2 本书籍，其中 1 本不在白名单内，请通过
2025-08-26 16:35:51.777690: ✅ 三步检查完成: 检测到 2 本书籍，其中 1 本不在白名单内，请通过
2025-08-26 16:35:51.777690: 📊 检查结果: 允许通过=true, 不在白名单书籍数量=1
2025-08-26 16:35:51.777690: ✅ 允许出馆: 检测到 2 本书籍，其中 1 本不在白名单内，请通过
2025-08-26 16:35:51.777690: ✅ 命令顺序正确：出馆流程中且最后命令是到位信号
2025-08-26 16:35:51.777690: 📤 正在发送成功信号到闸机：AA 00 01 01 00 00 48 36
2025-08-26 16:35:51.777690: 📤 准备发送闸机命令: success_signal
2025-08-26 16:35:51.778692: 📤 成功信号已发送（异步）
2025-08-26 16:35:51.778692: 闸机状态变更: GateState.exitChecking -> GateState.exitOver
2025-08-26 16:35:51.778692: 闸机状态更新: GateState.exitChecking -> GateState.exitOver
2025-08-26 16:35:51.779687: 发送原始命令数据: aa 00 01 01 00 00 48 36
2025-08-26 16:35:51.779687: 发送原始数据: aa 00 01 01 00 00 48 36
2025-08-26 16:35:51.779687: 发送数据成功: aa 00 01 01 00 00 48 36
2025-08-26 16:35:51.779687: [channel_1] 收到闸机事件: state_changed
2025-08-26 16:35:51.780681: 📨 收到GateCoordinator事件: state_changed
2025-08-26 16:35:51.780681: 闸机状态变更: GateState.exitOver
2025-08-26 16:35:51.780681: 🎨 处理状态变更UI: exitOver
2025-08-26 16:35:51.780681: 未处理的状态变更UI: exitOver
2025-08-26 16:35:51.781678: 发送数据成功: aa 00 01 01 00 00 48 36
2025-08-26 16:35:51.781678: 闸机命令发送成功: success_signal
2025-08-26 16:35:51.781678: ✅ 闸机命令 success_signal 发送成功
2025-08-26 16:35:51.781678: [channel_1] 收到闸机事件: exit_allowed
2025-08-26 16:35:51.781678: 📨 收到GateCoordinator事件: exit_allowed
2025-08-26 16:35:51.781678: Error:type 'Null' is not a subtype of type 'String' in type cast
Stack：#0      new BookInfo.fromJson (package:a3g/features/security_gate/models/book_info.dart:29)
#1      SilencePageViewModel._handleGateEvent.<anonymous closure> (package:a3g/features/security_gate/viewmodels/silence_page_viewmodel.dart:298)
#2      MappedListIterable.elementAt (dart:_internal/iterable.dart:415)
#3      ListIterator.moveNext (dart:_internal/iterable.dart:344)
#4      new _GrowableList._ofEfficientLengthIterable (dart:core-patch/growable_array.dart:189)
#5      new _GrowableList.of (dart:core-patch/growable_array.dart:150)
#6      new List.of (dart:core-patch/array_patch.dart:47)
#7      SetBase.toList (dart:collection/set.dart:119)
#8      SilencePageViewModel._handleGateEvent (package:a3g/features/security_gate/viewmodels/silence_page_viewmodel.dart:298)
#9      _rootRunUnary (dart:async/zone.dart:1407)
#10     _CustomZone.runUnary (dart:async/zone.dart:1308)
#11     _CustomZone.runUnaryGuarded (dart:async/zone.dart:1217)
#12     _BufferingStreamSubscription._sendData (dart:async/stream_impl.dart:339)
#13     _DelayedData.perform (dart:async/stream_impl.dart:515)
#14     _PendingEvents.handleNext (dart:async/stream_impl.dart:620)
#15     _PendingEvents.schedule.<anonymous closure> (dart:async/stream_impl.dart:591)
#16     _rootRun (dart:async/zone.dart:1391)
#17     _CustomZone.run (dart:async/zone.dart:1301)
#18     _CustomZone.runGuarded (dart:async/zone.dart:1209)
#19     _CustomZone.bindCallbackGuarded.<anonymous closure> (dart:async/zone.dart:1249)
#20     _rootRun (dart:async/zone.dart:1399)
#21     _CustomZone.run (dart:async/zone.dart:1301)
#22     _CustomZone.runGuarded (dart:async/zone.dart:1209)
#23     _CustomZone.bindCallbackGuarded.<anonymous closure> (dart:async/zone.dart:1249)
#24     _microtaskLoop (dart:async/schedule_microtask.dart:40)
#25     _startMicrotaskLoop (dart:async/schedule_microtask.dart:49)

2025-08-26 16:35:51.806595: 接收到数据: aa 00 01 81 00 00 49 de
2025-08-26 16:35:51.807591: 🔍 接收到串口数据: aa 00 01 81 00 00 49 de
2025-08-26 16:35:51.807591: 🔍 数据长度: 8 字节
2025-08-26 16:35:51.807591: 🔍 预定义命令列表:
2025-08-26 16:35:51.807591:   GateCommand.enterStart: aa 00 64 80 00 00 06 d2
2025-08-26 16:35:51.807591:   GateCommand.enterEnd: aa 00 65 80 00 00 07 2e
2025-08-26 16:35:51.807591:   GateCommand.exitStart: aa 00 c8 80 00 00 27 82
2025-08-26 16:35:51.807591:   GateCommand.exitEnd: aa 00 c9 80 00 00 26 7e
2025-08-26 16:35:51.808588:   GateCommand.reachPosition: aa 00 0a 80 00 00
2025-08-26 16:35:51.808588:   GateCommand.enterOpen: aa 00 02 01 00 00 48 72
2025-08-26 16:35:51.808588:   GateCommand.exitOpen: aa 00 01 01 00 00 48 36
2025-08-26 16:35:51.808588:   GateCommand.failSignal: aa 00 02 01 00 00 48 72
2025-08-26 16:35:51.808588:   GateCommand.tailgating: aa 00 0f 80 00 00
2025-08-26 16:35:51.808588:   GateCommand.doorHasPerson: aa 00 0b 80 00 00
2025-08-26 16:35:51.808588: ❌ 未识别的闸机命令 - 数据不匹配任何预定义命令
2025-08-26 16:35:51.808588: ❌ 接收数据: aa 00 01 81 00 00 49 de
2025-08-26 16:35:51.919221: 🔄 开始RFID轮询检查...
2025-08-26 16:35:51.919221: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:35:51.919221: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:35:51.919221: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:35:51.919221: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:35:51.920218: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:35:51.920218: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:35:51.923211: 🔍 LSGate硬件扫描详情:
2025-08-26 16:35:51.923211:   - 设备句柄: 2424588958144
2025-08-26 16:35:51.923211:   - FetchRecords返回值: 0
2025-08-26 16:35:51.924217:   - 报告数量: 5
2025-08-26 16:35:51.924217:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:35:51.924217:   - 原始数据: 01 00 19 08 1A 10 24 35 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:35:51.924217:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16