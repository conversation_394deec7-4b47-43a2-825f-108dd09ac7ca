import 'dart:math';
import 'package:flutter/material.dart';
import 'package:a3g/core/utils/window_util.dart';
import '../models/silence_page_state.dart';

/// 等待到位界面内容组件
class WaitingContent extends StatefulWidget {
  final UIContentData data;
  
  const WaitingContent({
    Key? key,
    required this.data,
  }) : super(key: key);

  @override
  State<WaitingContent> createState() => _WaitingContentState();
}

class _WaitingContentState extends State<WaitingContent>
    with TickerProviderStateMixin {
  
  late AnimationController _pulseController;
  late AnimationController _rotationController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _rotationAnimation;
  
  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimations();
  }
  
  void _initializeAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    
    _rotationController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 2 * pi,
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.linear,
    ));
  }
  
  void _startAnimations() {
    _pulseController.repeat(reverse: true);
    _rotationController.repeat();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _rotationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 等待动画图标
          AnimatedBuilder(
            animation: Listenable.merge([_pulseAnimation, _rotationAnimation]),
            builder: (context, child) {
              return Transform.scale(
                scale: _pulseAnimation.value,
                child: Transform.rotate(
                  angle: _rotationAnimation.value,
                  child: Container(
                    width: 120.p,
                    height: 120.p,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.blueGrey.withOpacity(0.1),
                      border: Border.all(
                        color: Colors.blueGrey,
                        width: 2,
                      ),
                    ),
                    child: Icon(
                      Icons.directions_walk,
                      size: 60.p,
                      color: Colors.blueGrey,
                    ),
                  ),
                ),
              );
            },
          ),

          SizedBox(height: 40.p),

          // 标题
          Text(
            widget.data.title ?? '等待到位',
            style: TextStyle(
              color: Colors.white,
              fontSize: 32.p,
              fontWeight: FontWeight.w500,
            ),
          ),

          SizedBox(height: 20.p),

          // 提示信息
          Text(
            widget.data.message ?? '请进入闸机通道，等待到位信号',
            style: TextStyle(
              color: Colors.white.withOpacity(0.8),
              fontSize: 24.p,
            ),
            textAlign: TextAlign.center,
          ),

          SizedBox(height: 40.p),

          // 等待指示器
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(3, (index) {
              return AnimatedBuilder(
                animation: _pulseController,
                builder: (context, child) {
                  final delay = index * 0.3;
                  final animationValue = (_pulseController.value + delay) % 1.0;
                  final opacity = (sin(animationValue * 2 * pi) + 1) / 2;

                  return Container(
                    margin: EdgeInsets.symmetric(horizontal: 8.p),
                    width: 12.p,
                    height: 12.p,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.blueGrey.withOpacity(opacity),
                    ),
                  );
                },
              );
            }),
          ),
        ],
      ),
    );
  }
}
