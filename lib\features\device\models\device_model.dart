/// 设备注册模型
class DeviceRegisterModel {
  final String deviceMac;
  final String? deviceName;
  final String? doorCode;
  final String? doorName;
  final String? areaCode;
  final String? areaName;
  final String? libId;
  final String? libName;

  DeviceRegisterModel({
    required this.deviceMac,
    this.deviceName,
    this.doorCode,
    this.doorName,
    this.areaCode,
    this.areaName,
    this.libId,
    this.libName,
  });

  Map<String, dynamic> toJson() {
    return {
      'deviceMac': deviceMac,
      'deviceName': deviceName,
      'doorCode': doorCode,
      'doorName': doorName,
      'areaCode': areaCode,
      'areaName': areaName,
      'libId': libId,
      'libName': libName,
    };
  }

  factory DeviceRegisterModel.fromJson(Map<String, dynamic> json) {
    return DeviceRegisterModel(
      deviceMac: json['deviceMac'] ?? '',
      deviceName: json['deviceName'],
      doorCode: json['doorCode'],
      doorName: json['doorName'],
      areaCode: json['areaCode'],
      areaName: json['areaName'],
      libId: json['libId'],
      libName: json['libName'],
    );
  }

  DeviceRegisterModel copyWith({
    String? deviceMac,
    String? deviceName,
    String? doorCode,
    String? doorName,
    String? areaCode,
    String? areaName,
    String? libId,
    String? libName,
  }) {
    return DeviceRegisterModel(
      deviceMac: deviceMac ?? this.deviceMac,
      deviceName: deviceName ?? this.deviceName,
      doorCode: doorCode ?? this.doorCode,
      doorName: doorName ?? this.doorName,
      areaCode: areaCode ?? this.areaCode,
      areaName: areaName ?? this.areaName,
      libId: libId ?? this.libId,
      libName: libName ?? this.libName,
    );
  }
}

/// 设备状态上传模型
class DeviceStatusModel {
  final String deviceMac;
  final int status; // 1=在线, 2=故障, 3=离线

  DeviceStatusModel({
    required this.deviceMac,
    required this.status,
  });

  Map<String, dynamic> toJson() {
    return {
      'deviceMac': deviceMac,
      'status': status,
    };
  }

  factory DeviceStatusModel.fromJson(Map<String, dynamic> json) {
    return DeviceStatusModel(
      deviceMac: json['deviceMac'] ?? '',
      status: json['status'] ?? 1,
    );
  }

  DeviceStatusModel copyWith({
    String? deviceMac,
    int? status,
  }) {
    return DeviceStatusModel(
      deviceMac: deviceMac ?? this.deviceMac,
      status: status ?? this.status,
    );
  }
}

/// API响应模型
class ApiResponseModel {
  final int errorCode;
  final String message;
  final dynamic data;

  ApiResponseModel({
    required this.errorCode,
    required this.message,
    this.data,
  });

  factory ApiResponseModel.fromJson(Map<String, dynamic> json) {
    return ApiResponseModel(
      errorCode: json['errorCode'] ?? -1,
      message: json['message'] ?? '',
      data: json['data'],
    );
  }

  bool get isSuccess => errorCode == 0;
}

/// 设备状态枚举
enum DeviceStatus {
  online(1, '在线'),
  fault(2, '故障'),
  offline(3, '离线');

  const DeviceStatus(this.value, this.label);
  final int value;
  final String label;

  static DeviceStatus fromValue(int value) {
    return DeviceStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => DeviceStatus.online,
    );
  }
}

/// 读者认证模型
class ReaderAuthModel {
  final String deviceMac;
  final String? patronSn;    // 读者证号
  final String? cardSn;      // 物理卡号
  final int type;            // 1=进, 2=出

  ReaderAuthModel({
    required this.deviceMac,
    this.patronSn,
    this.cardSn,
    required this.type,
  });

  Map<String, dynamic> toJson() {
    return {
      'deviceMac': deviceMac,
      'patronSn': patronSn,
      'cardSn': cardSn,
      'type': type,
    };
  }

  factory ReaderAuthModel.fromJson(Map<String, dynamic> json) {
    return ReaderAuthModel(
      deviceMac: json['deviceMac'] ?? '',
      patronSn: json['patronSn'],
      cardSn: json['cardSn'],
      type: json['type'] ?? 1,
    );
  }

  ReaderAuthModel copyWith({
    String? deviceMac,
    String? patronSn,
    String? cardSn,
    int? type,
  }) {
    return ReaderAuthModel(
      deviceMac: deviceMac ?? this.deviceMac,
      patronSn: patronSn ?? this.patronSn,
      cardSn: cardSn ?? this.cardSn,
      type: type ?? this.type,
    );
  }

  /// 验证数据有效性
  bool get isValid {
    // 设备MAC必填
    if (deviceMac.isEmpty) return false;

    // patronSn和cardSn至少有一个不为空
    if ((patronSn == null || patronSn!.isEmpty) &&
        (cardSn == null || cardSn!.isEmpty)) {
      return false;
    }

    // type必须是1或2
    if (type != 1 && type != 2) return false;

    return true;
  }

  /// 获取认证标识符（优先使用patronSn）
  String get identifier {
    if (patronSn != null && patronSn!.isNotEmpty) {
      return patronSn!;
    }
    return cardSn ?? '';
  }
}

/// 通行类型枚举
enum PassType {
  enter(1, '进'),
  exit(2, '出');

  const PassType(this.value, this.label);
  final int value;
  final String label;

  static PassType fromValue(int value) {
    return PassType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => PassType.enter,
    );
  }
}
