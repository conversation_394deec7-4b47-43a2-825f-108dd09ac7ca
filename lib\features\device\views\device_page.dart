import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../view_models/device_view_model.dart';
import 'device_view.dart';

class DevicePage extends StatelessWidget {
  const DevicePage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => DeviceViewModel(),
      child: const DeviceView(),
    );
  }
}
