import 'package:flutter/material.dart';

import '../models/book_info.dart';
import '../services/enhanced_rfid_service.dart';

/// 🔥 增强的闸机扫描界面组件
/// 显示书籍详细信息和借阅状态
class EnhancedGateScanningWidget extends StatelessWidget {
  final List<String> scannedBarcodes;
  final Map<String, BookInfo> booksInfo;
  final bool isScanning;
  final String? statusMessage;
  
  const EnhancedGateScanningWidget({
    Key? key,
    required this.scannedBarcodes,
    required this.booksInfo,
    this.isScanning = false,
    this.statusMessage,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 扫描状态指示
        _buildScanningIndicator(context),
        
        // 统计信息
        _buildStatistics(context),
        
        // 书籍列表
        Expanded(
          child: _buildBooksList(context),
        ),
      ],
    );
  }
  
  /// 构建扫描状态指示器
  Widget _buildScanningIndicator(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isScanning ? Colors.blue.shade50 : Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          if (isScanning) ...[
            SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
              ),
            ),
            SizedBox(width: 12),
          ] else
            Icon(
              Icons.check_circle,
              color: Colors.green,
              size: 20,
            ),
          SizedBox(width: 8),
          Expanded(
            child: Text(
              statusMessage ?? (isScanning ? '正在扫描书籍...' : '扫描完成'),
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: isScanning ? Colors.blue.shade700 : Colors.green.shade700,
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  /// 构建统计信息
  Widget _buildStatistics(BuildContext context) {
    final totalBooks = scannedBarcodes.length;
    final loadedBooks = booksInfo.length;
    final borrowedBooks = booksInfo.values.where((book) => book.isBorrowed).length;
    final unborrowedBooks = booksInfo.values.where((book) => !book.isBorrowed).length;
    
    return Container(
      padding: EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem('总计', totalBooks.toString(), Colors.blue),
          _buildStatItem('已加载', loadedBooks.toString(), Colors.orange),
          _buildStatItem('已借', borrowedBooks.toString(), Colors.green),
          _buildStatItem('未借', unborrowedBooks.toString(), Colors.red),
        ],
      ),
    );
  }
  
  /// 构建统计项
  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }
  
  /// 构建书籍列表
  Widget _buildBooksList(BuildContext context) {
    if (scannedBarcodes.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.book_outlined,
              size: 64,
              color: Colors.grey.shade400,
            ),
            SizedBox(height: 16),
            Text(
              isScanning ? '等待扫描书籍...' : '未扫描到书籍',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      );
    }
    
    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: 16),
      itemCount: scannedBarcodes.length,
      itemBuilder: (context, index) {
        final barcode = scannedBarcodes[index];
        final bookInfo = booksInfo[barcode];
        
        return _buildBookItem(context, barcode, bookInfo);
      },
    );
  }
  
  /// 构建书籍项
  Widget _buildBookItem(BuildContext context, String barcode, BookInfo? bookInfo) {
    return Card(
      margin: EdgeInsets.only(bottom: 8),
      elevation: 2,
      child: ListTile(
        contentPadding: EdgeInsets.all(16),
        leading: _buildBookStatusIcon(bookInfo),
        title: Text(
          bookInfo?.bookName ?? barcode,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 4),
            Text('条码: $barcode'),
            if (bookInfo != null) ...[
              if (bookInfo.author?.isNotEmpty == true)
                Text('作者: ${bookInfo.author}'),
              Text(
                '状态: ${bookInfo.borrowInfo}',
                style: TextStyle(
                  color: bookInfo.isBorrowed ? Colors.green : Colors.red,
                  fontWeight: FontWeight.w500,
                ),
              ),
              if (bookInfo.overdueInfo != null)
                Text(
                  bookInfo.overdueInfo!,
                  style: TextStyle(
                    color: Colors.orange,
                    fontWeight: FontWeight.w500,
                  ),
                ),
            ] else
              Text(
                '正在获取书籍信息...',
                style: TextStyle(
                  color: Colors.orange,
                  fontStyle: FontStyle.italic,
                ),
              ),
          ],
        ),
        trailing: _buildBookTrailing(bookInfo),
        onTap: () => _showBookDetails(context, barcode, bookInfo),
      ),
    );
  }
  
  /// 构建书籍状态图标
  Widget _buildBookStatusIcon(BookInfo? bookInfo) {
    if (bookInfo == null) {
      return Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: Colors.orange.shade100,
          shape: BoxShape.circle,
        ),
        child: Icon(
          Icons.hourglass_empty,
          color: Colors.orange,
          size: 20,
        ),
      );
    }
    
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: bookInfo.isBorrowed ? Colors.green.shade100 : Colors.red.shade100,
        shape: BoxShape.circle,
      ),
      child: Icon(
        bookInfo.isBorrowed ? Icons.check_circle : Icons.warning,
        color: bookInfo.isBorrowed ? Colors.green : Colors.red,
        size: 20,
      ),
    );
  }
  
  /// 构建书籍尾部组件
  Widget? _buildBookTrailing(BookInfo? bookInfo) {
    if (bookInfo == null) {
      return SizedBox(
        width: 20,
        height: 20,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(Colors.orange),
        ),
      );
    }
    
    if (bookInfo.isOverdue) {
      return Icon(
        Icons.schedule,
        color: Colors.red,
        size: 24,
      );
    }
    
    return null;
  }
  
  /// 显示书籍详情
  void _showBookDetails(BuildContext context, String barcode, BookInfo? bookInfo) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('书籍详情'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildDetailRow('条码', barcode),
              if (bookInfo != null) ...[
                Divider(),
                _buildDetailRow('书名', bookInfo.bookName),
                if (bookInfo.author?.isNotEmpty == true)
                  _buildDetailRow('作者', bookInfo.author!),
                if (bookInfo.isbn?.isNotEmpty == true)
                  _buildDetailRow('ISBN', bookInfo.isbn!),
                Divider(),
                _buildDetailRow('借阅状态', bookInfo.borrowStatusText),
                if (bookInfo.isBorrowed) ...[
                  if (bookInfo.borrowerName?.isNotEmpty == true)
                    _buildDetailRow('借阅者', bookInfo.borrowerName!),
                  if (bookInfo.borrowDate != null)
                    _buildDetailRow('借阅日期', _formatDate(bookInfo.borrowDate!)),
                  if (bookInfo.returnDate != null)
                    _buildDetailRow('应还日期', _formatDate(bookInfo.returnDate!)),
                  if (bookInfo.overdueInfo != null)
                    _buildDetailRow('逾期信息', bookInfo.overdueInfo!, Colors.red),
                ],
              ] else ...[
                Divider(),
                Text(
                  '正在获取书籍信息...',
                  style: TextStyle(
                    color: Colors.orange,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('关闭'),
          ),
        ],
      ),
    );
  }
  
  /// 构建详情行
  Widget _buildDetailRow(String label, String value, [Color? valueColor]) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey.shade700,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                color: valueColor ?? Colors.black87,
                fontWeight: valueColor != null ? FontWeight.w500 : null,
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  /// 格式化日期
  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }
}
