import 'package:flutter/material.dart';
import 'package:a3g/core/utils/window_util.dart';
import '../models/silence_page_state.dart';

/// 错误界面内容组件
class ErrorContent extends StatefulWidget {
  final UIContentData data;
  
  const ErrorContent({
    Key? key,
    required this.data,
  }) : super(key: key);

  @override
  State<ErrorContent> createState() => _ErrorContentState();
}

class _ErrorContentState extends State<ErrorContent>
    with TickerProviderStateMixin {
  
  late AnimationController _shakeController;
  late AnimationController _pulseController;
  late Animation<double> _shakeAnimation;
  late Animation<double> _pulseAnimation;
  
  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimations();
  }
  
  void _initializeAnimations() {
    _shakeController = AnimationController(
      duration: Duration(milliseconds: 500),
      vsync: this,
    );
    
    _pulseController = AnimationController(
      duration: Duration(seconds: 1),
      vsync: this,
    );
    
    _shakeAnimation = Tween<double>(
      begin: -10.0,
      end: 10.0,
    ).animate(CurvedAnimation(
      parent: _shakeController,
      curve: Curves.elasticIn,
    ));
    
    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
  }
  
  void _startAnimations() {
    _shakeController.forward().then((_) {
      _shakeController.reverse();
    });
    _pulseController.repeat(reverse: true);
  }
  
  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // 错误图标动画
        AnimatedBuilder(
          animation: _shakeAnimation,
          builder: (context, child) {
            return Transform.translate(
              offset: Offset(_shakeAnimation.value, 0),
              child: AnimatedBuilder(
                animation: _pulseAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _pulseAnimation.value,
                    child: Container(
                      width: 200.p,
                      height: 200.p,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.red.withOpacity(0.2),
                        border: Border.all(
                          color: Colors.red,
                          width: 4,
                        ),
                      ),
                      child: Icon(
                        Icons.error_outline,
                        size: 100.p,
                        color: Colors.red,
                      ),
                    ),
                  );
                },
              ),
            );
          },
        ),
        
        SizedBox(height: 40.p),
        
        // 错误标题
        Text(
          widget.data.title ?? '系统异常',
          style: TextStyle(
            color: Colors.red,
            fontSize: 48.p,
            fontWeight: FontWeight.bold,
          ),
        ),
        
        SizedBox(height: 30.p),
        
        // 错误消息
        Container(
          padding: EdgeInsets.symmetric(horizontal: 40.p, vertical: 25.p),
          decoration: BoxDecoration(
            color: Colors.red.withOpacity(0.1),
            borderRadius: BorderRadius.circular(20.p),
            border: Border.all(
              color: Colors.red.withOpacity(0.3),
              width: 2,
            ),
          ),
          child: Column(
            children: [
              Icon(
                Icons.warning,
                color: Colors.red,
                size: 40.p,
              ),
              SizedBox(height: 15.p),
              Text(
                widget.data.message ?? '系统发生未知错误',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 24.p,
                  fontWeight: FontWeight.w500,
                  height: 1.3,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        
        SizedBox(height: 40.p),
        
        // 恢复提示
        Container(
          padding: EdgeInsets.all(20.p),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.1),
            borderRadius: BorderRadius.circular(15.p),
          ),
          child: Column(
            children: [
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.refresh,
                    color: Colors.white.withOpacity(0.8),
                    size: 24.p,
                  ),
                  SizedBox(width: 10.p),
                  Text(
                    '系统将自动恢复',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.8),
                      fontSize: 18.p,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 10.p),
              Text(
                '请稍候或联系管理员',
                style: TextStyle(
                  color: Colors.white.withOpacity(0.6),
                  fontSize: 16.p,
                ),
              ),
            ],
          ),
        ),
        
        SizedBox(height: 30.p),
        
        // 错误代码（如果有）
        if (widget.data.extraData != null && 
            widget.data.extraData!.containsKey('error_code'))
          Container(
            padding: EdgeInsets.symmetric(horizontal: 20.p, vertical: 10.p),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.3),
              borderRadius: BorderRadius.circular(10.p),
            ),
            child: Text(
              '错误代码: ${widget.data.extraData!['error_code']}',
              style: TextStyle(
                color: Colors.white.withOpacity(0.7),
                fontSize: 14.p,
                fontFamily: 'monospace',
              ),
            ),
          ),
      ],
    );
  }
  
  @override
  void dispose() {
    _shakeController.dispose();
    _pulseController.dispose();
    super.dispose();
  }
}
