import 'dart:math' as math;
import 'package:a3g/core/utils/window_util.dart';
import 'package:flutter/material.dart';

import '../../../shared/utils/asset_util.dart';

/// 雷达扫描状态枚举
enum RadarScanState {
  scanning,    // 正在检测
  detected,    // 检测到未借图书
}

class RadarScanTipCard extends StatefulWidget {
  final int number;
  final RadarScanState state;

  const RadarScanTipCard({
    super.key,
    required this.number,
    this.state = RadarScanState.detected,
  });

  @override
  State<RadarScanTipCard> createState() => _RadarScanTipCardState();
}

class _RadarScanTipCardState extends State<RadarScanTipCard>
    with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimations();
  }

  void _initializeAnimations() {
    _rotationController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 2 * 3.14159, // 2π for full rotation
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.linear,
    ));
  }

  void _startAnimations() {
    _rotationController.repeat();
  }

  @override
  Widget build(BuildContext context) {
    // 根据状态确定标题文本
    final titleText = widget.state == RadarScanState.scanning
        ? '正在检测'
        : '检测到未借图书数量';

    // 根据状态确定是否显示警告卡片
    final showWarningCard = widget.state == RadarScanState.detected;

    return Container(
      width: 700.p,
      height: 600.p,
      padding: EdgeInsets.all(40.p),
      decoration: BoxDecoration(
          image: DecorationImage(
        image: AssetImage(AssetUtil.fullPath('tip_card_bg2')),
      )),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            titleText,
            style: TextStyle(
                fontSize: 40.p,
                fontWeight: FontWeight.w500,
                color: const Color(0xFF242424)),
          ),
          SizedBox(height: 36.p),

          // 雷达扫描区域 - 静态底图 + 旋转扫描效果 + 静态文字
          SizedBox(
            width: 280.p,
            height: 280.p,
            child: Stack(
              alignment: Alignment.center,
              children: [
                // 静态的雷达底图
                Container(
                  width: 280.p,
                  height: 280.p,
                  decoration: BoxDecoration(
                    image: DecorationImage(
                      image: AssetImage(AssetUtil.fullPath('radar_scan_bg')),
                    ),
                  ),
                ),

                // 环绕轨道的扫描效果
                // AnimatedBuilder(
                //   animation: _rotationAnimation,
                //   builder: (context, child) {
                //     // 计算扫描效果在40px半径圆环上的位置
                //     final radius = 40.p; // 环绕半径
                //     final angle = _rotationAnimation.value;
                //     final offsetX = radius * math.cos(angle);
                //     final offsetY = radius * math.sin(angle);

                //     return Transform.translate(
                //       offset: Offset(offsetX, offsetY),
                //       child: Container(
                //         width: 60.p, // 扫描效果的大小
                //         height: 60.p,
                //         decoration: BoxDecoration(
                //           image: DecorationImage(
                //             image: AssetImage(AssetUtil.fullPath('scanning_effect')),
                //             fit: BoxFit.contain,
                //           ),
                //         ),
                //       ),
                //     );
                //   },
                // ),

                // 静态的数字文字
                Center(
                  child: Text(
                    widget.number.toString(),
                    style: TextStyle(
                      fontSize: 96.p,
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF9527C8),
                    ),
                  ),
                ),
              ],
            ),
          ),

          SizedBox(height: 36.p),

          // 根据状态决定是否显示警告卡片
          if (showWarningCard)
            const GradientTipCard(
              text: '检测到您携带有未借阅的图书 请先办理借阅再出馆~',
            ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _rotationController.dispose();
    super.dispose();
  }
}



class GradientTipCard extends StatelessWidget {
  final String? text;
  final TextStyle? textStyle;
  final EdgeInsetsGeometry? padding;
  final Widget? child;
  final MainAxisAlignment? mainAxisAlignment;
  final CrossAxisAlignment? crossAxisAlignment;
  
  const GradientTipCard({
    Key? key,
    this.text,
    this.textStyle,
    this.padding,
    this.child,
    this.mainAxisAlignment,
    this.crossAxisAlignment,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 410,
      height: 100,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
          colors: [
            Color(0xFF8E3577), // #8E3577
            Color(0xFF364C88), // #364C88
          ],
          stops: [0.0, 1.0],
        ),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(0),     // 0px
          topRight: Radius.circular(30),   // 30px
          bottomLeft: Radius.circular(30), // 30px
          bottomRight: Radius.circular(30), // 30px
        ),
      ),
      padding: padding ?? const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      child: child ?? (text != null ? _buildDefaultContent() : const SizedBox()),
    );
  }
  
  /// 构建默认文本内容
  Widget _buildDefaultContent() {
    return Center(
      child: Text(
        text!,
        style: textStyle ?? const TextStyle(
          color: Colors.white,
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
}