import 'dart:async';
import 'package:flutter/foundation.dart';
import '../../device/services/device_api_service.dart';
import '../../device/models/device_model.dart';
import '../../device/services/device_config_manager.dart';
import '../models/auth_result.dart';

/// 门径认证服务
/// 负责将新的读者认证接口集成到现有认证流程中
class DoorAuthService {
  static DoorAuthService? _instance;
  static DoorAuthService get instance => _instance ??= DoorAuthService._();
  DoorAuthService._();

  final DeviceApiService _apiService = DeviceApiService.instance;
  final DeviceConfigManager _configManager = DeviceConfigManager.instance;
  
  // 设备MAC地址缓存
  String? _deviceMac;
  
  /// 初始化服务
  Future<void> initialize() async {
    try {
      debugPrint('初始化门径认证服务...');

      // 初始化API服务
      await _apiService.initialize();

      // 获取设备MAC地址
      await _loadDeviceMac();

      debugPrint('门径认证服务初始化完成');
    } catch (e) {
      debugPrint('门径认证服务初始化失败: $e');
      rethrow;
    }
  }
  
  /// 加载设备MAC地址
  Future<void> _loadDeviceMac() async {
    try {
      // 尝试从配置中获取设备MAC
      final config = await _configManager.getAllConfig();
      _deviceMac = config['device_mac'] as String?;
      
      // 如果没有配置，使用默认值
      if (_deviceMac == null || _deviceMac!.isEmpty) {
        _deviceMac = await _generateDefaultMac();
        debugPrint('使用默认设备MAC: $_deviceMac');
      } else {
        debugPrint('加载设备MAC: $_deviceMac');
      }
    } catch (e) {
      debugPrint('加载设备MAC失败: $e');
      _deviceMac = await _generateDefaultMac();
    }
  }
  
  /// 生成默认MAC地址
  Future<String> _generateDefaultMac() async {
    // 可以从系统信息、配置文件或硬件信息获取
    // 这里先使用一个默认值，实际项目中应该获取真实的MAC地址
    return 'FFFFFFFF';
  }
  
  /// 设置设备MAC地址
  Future<void> setDeviceMac(String mac) async {
    _deviceMac = mac;
    
    // 保存到配置文件
    try {
      final config = await _configManager.getAllConfig();
      config['device_mac'] = mac;
      await _configManager.setBaseUrl(config['base_url'] as String? ?? 'http://172.16.0.118:9000');
      debugPrint('设备MAC地址已更新: $mac');
    } catch (e) {
      debugPrint('保存设备MAC地址失败: $e');
    }
  }
  
  /// 获取当前设备MAC地址
  String? get deviceMac => _deviceMac;
  
  /// 执行读者认证
  /// [identifier] 读者证号或物理卡号
  /// [method] 认证方式
  /// [isEnterFlow] 是否为进馆流程（true=进馆，false=出馆）
  Future<AuthResult> authenticate({
    required String identifier,
    required AuthMethod method,
    required bool isEnterFlow,
  }) async {
    try {
      debugPrint('开始门径认证: identifier=$identifier, method=$method, isEnter=$isEnterFlow');
      
      // 检查设备MAC
      if (_deviceMac == null || _deviceMac!.isEmpty) {
        await _loadDeviceMac();
      }
      
      if (_deviceMac == null || _deviceMac!.isEmpty) {
        return AuthResult(
          method: method,
          status: AuthStatus.failureError,
          errorMessage: '设备MAC地址未配置',
          timestamp: DateTime.now(),
        );
      }
      
      // 构建认证请求
      final authData = ReaderAuthModel(
        deviceMac: _deviceMac!,
        patronSn: _isPatronSn(identifier) ? identifier : null,
        cardSn: _isPatronSn(identifier) ? null : identifier,
        type: isEnterFlow ? 1 : 2, // 1=进馆, 2=出馆
      );
      
      debugPrint('发送认证请求: ${authData.toJson()}');
      
      // 调用认证接口
      final response = await _apiService.authenticateReader(authData);
      
      debugPrint('认证响应: errorCode=${response.errorCode}, message=${response.message}');
      
      // 处理响应结果
      if (response.isSuccess) {
        // 认证成功
        return AuthResult(
          method: method,
          status: AuthStatus.success,
          userId: identifier,
          userName: _extractUserName(identifier), // 从标识符提取用户名
          timestamp: DateTime.now(),
        );
      } else {
        // 认证失败
        return AuthResult(
          method: method,
          status: AuthStatus.failureNoMatch,
          errorMessage: response.message.isNotEmpty ? response.message : '认证失败',
          timestamp: DateTime.now(),
        );
      }
      
    } catch (e) {
      debugPrint('门径认证异常: $e');
      
      String errorMessage = '认证服务异常';
      if (e.toString().contains('timeout')) {
        errorMessage = '认证服务响应超时，请重试';
      } else if (e.toString().contains('connection')) {
        errorMessage = '无法连接到认证服务器';
      } else if (e.toString().contains('404')) {
        errorMessage = '认证服务不可用';
      } else if (e.toString().contains('500')) {
        errorMessage = '认证服务器内部错误';
      }
      
      return AuthResult(
        method: method,
        status: AuthStatus.failureError,
        errorMessage: errorMessage,
        timestamp: DateTime.now(),
      );
    }
  }
  
  /// 判断标识符是否为读者证号（简单规则，可根据实际情况调整）
  bool _isPatronSn(String identifier) {
    // 这里可以根据实际的读者证号规则来判断
    // 例如：读者证号通常是纯数字，物理卡号可能包含字母
    return RegExp(r'^\d+$').hasMatch(identifier);
  }
  
  /// 从标识符提取用户名（由于新接口不返回用户信息，这里做简单处理）
  String _extractUserName(String identifier) {
    // 新接口不返回用户详细信息，这里返回一个通用名称
    // 如果需要真实姓名，可以考虑保留SIP2作为补充查询
    return '认证用户';
  }
  
  /// 测试认证服务连接
  Future<bool> testConnection() async {
    try {
      return await _apiService.testConnection();
    } catch (e) {
      debugPrint('测试认证服务连接失败: $e');
      return false;
    }
  }
  
  /// 获取服务状态
  Map<String, dynamic> getStatus() {
    return {
      'device_mac': _deviceMac,
      'api_service_status': _apiService.getStatus(),
      'service_name': 'DoorAuthService',
      'version': '1.0.0',
    };
  }
}
