import 'package:flutter/material.dart';

/// 闸机状态枚举（按照文档要求）
enum GateState {
  idle,                 // 空闲
  enterStarted,         // 进馆开始（等待到位信号）
  enterWaitingAuth,     // 进馆到位，等待认证
  enterScanning,        // 进馆扫描中（认证中）
  enterOpening,         // 进馆开门中
  enterOver,            // 进馆结束
  exitStarted,          // 出馆开始（等待到位信号）
  exitWaitingAuth,      // 出馆到位，等待认证
  exitScanning,         // 出馆RFID扫描中
  exitChecking,         // 出馆书籍检查中
  exitOver,             // 出馆结束
  error                 // 异常状态
}

/// 闸机状态扩展
extension GateStateExtension on GateState {
  /// 获取状态显示名称
  String get displayName {
    switch (this) {
      case GateState.idle:
        return '系统待机中';
      case GateState.enterStarted:
        return '等待进入到位';
      case GateState.enterWaitingAuth:
        return '请进行身份认证';
      case GateState.enterScanning:
        return '正在认证...';
      case GateState.enterOpening:
        return '认证成功，请通过';
      case GateState.enterOver:
        return '进馆完成';
      case GateState.exitStarted:
        return '等待出馆到位';
      case GateState.exitWaitingAuth:
        return '正在扫描随身物品';
      case GateState.exitScanning:
        return '扫描中，请稍候...';
      case GateState.exitChecking:
        return '正在检查书籍状态';
      case GateState.exitOver:
        return '出馆完成';
      case GateState.error:
        return '系统异常';
    }
  }
  
  /// 获取状态指示器颜色
  Color get indicatorColor {
    switch (this) {
      case GateState.idle:
        return Colors.green;
      case GateState.enterStarted:
      case GateState.enterWaitingAuth:
      case GateState.enterScanning:
        return Colors.blue;
      case GateState.enterOpening:
      case GateState.enterOver:
        return Colors.lightBlue;
      case GateState.exitStarted:
      case GateState.exitWaitingAuth:
      case GateState.exitScanning:
        return Colors.orange;
      case GateState.exitChecking:
      case GateState.exitOver:
        return Colors.deepOrange;
      case GateState.error:
        return Colors.red;
    }
  }
  
  /// 获取状态图标
  IconData get icon {
    switch (this) {
      case GateState.idle:
        return Icons.security;
      case GateState.enterStarted:
      case GateState.enterWaitingAuth:
      case GateState.enterScanning:
        return Icons.login;
      case GateState.enterOpening:
      case GateState.enterOver:
        return Icons.check_circle;
      case GateState.exitStarted:
      case GateState.exitWaitingAuth:
      case GateState.exitScanning:
        return Icons.logout;
      case GateState.exitChecking:
      case GateState.exitOver:
        return Icons.book_outlined;
      case GateState.error:
        return Icons.error;
    }
  }
  
  /// 是否为进馆相关状态
  bool get isEnterState {
    return [
      GateState.enterStarted,
      GateState.enterWaitingAuth,
      GateState.enterScanning,
      GateState.enterOpening,
      GateState.enterOver,
    ].contains(this);
  }

  /// 是否为出馆相关状态
  bool get isExitState {
    return [
      GateState.exitStarted,
      GateState.exitWaitingAuth,
      GateState.exitScanning,
      GateState.exitChecking,
      GateState.exitOver,
    ].contains(this);
  }

  /// 是否为活跃状态（非空闲和完成状态）
  bool get isActive {
    return ![
      GateState.idle,
      GateState.enterOver,
      GateState.exitOver,
    ].contains(this);
  }
}
