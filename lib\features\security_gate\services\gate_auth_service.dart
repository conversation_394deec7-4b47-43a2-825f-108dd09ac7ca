import 'dart:async';
import 'package:flutter/foundation.dart';
import '../../auth/services/multi_auth_manager.dart';
import '../../auth/models/auth_result.dart';

/// 闸机专用认证服务
/// 提供无UI的认证功能，专门为安全闸机场景设计
class GateAuthService {
  static GateAuthService? _instance;
  static GateAuthService get instance => _instance ??= GateAuthService._();
  GateAuthService._();

  // 认证管理器
  MultiAuthManager? _authManager;
  
  // 事件订阅
  StreamSubscription<AuthResult>? _authSubscription;
  
  // 状态管理
  bool _isRunning = false;
  bool _isInitialized = false;
  String? _errorMessage;
  
  // 回调函数
  Function(AuthResult)? _onAuthResult;
  Function(String)? _onError;
  Function(MultiAuthState)? _onStateChanged;
  
  // 配置参数（与MultiAuthManager初始化的认证方式保持一致）
  List<AuthMethod> _enabledMethods = [
    AuthMethod.face,              // 人脸识别
    AuthMethod.readerCard,        // 读者证
    AuthMethod.wechatScanQRCode,  // 微信扫码
  ];
  
  // 超时控制
  Timer? _timeoutTimer;
  static const int _defaultTimeoutSeconds = 30;

  /// 获取服务状态
  bool get isRunning => _isRunning;
  bool get isInitialized => _isInitialized;
  String? get errorMessage => _errorMessage;
  List<AuthMethod> get enabledMethods => List.unmodifiable(_enabledMethods);

  /// 初始化闸机认证服务
  Future<void> initialize({
    List<AuthMethod>? enabledMethods,
  }) async {
    if (_isInitialized) {
      debugPrint('闸机认证服务已经初始化');
      return;
    }

    try {
      debugPrint('开始初始化闸机认证服务...');
      
      // 设置启用的认证方式
      if (enabledMethods != null && enabledMethods.isNotEmpty) {
        _enabledMethods = enabledMethods;
      }
      
      // 获取多认证管理器实例
      _authManager = MultiAuthManager.instance;
      
      _isInitialized = true;
      _clearError();
      
      debugPrint('闸机认证服务初始化完成，启用认证方式: ${_enabledMethods.map(_getAuthMethodName).join('、')}');
    } catch (e) {
      _setError('闸机认证服务初始化失败: $e');
      debugPrint('闸机认证服务初始化失败: $e');
      rethrow;
    }
  }

  /// 启动闸机认证（无UI模式）
  Future<void> startGateAuth({
    required Function(AuthResult) onResult,
    Function(String)? onError,
    Function(MultiAuthState)? onStateChanged,
    List<AuthMethod>? enabledMethods,
    int timeoutSeconds = _defaultTimeoutSeconds,
  }) async {
    if (!_isInitialized) {
      await initialize(enabledMethods: enabledMethods);
    }

    if (_isRunning) {
      debugPrint('闸机认证服务已在运行中');
      return;
    }

    try {
      debugPrint('启动闸机认证服务...');

      // 保存回调函数
      _onAuthResult = onResult;
      _onError = onError;
      _onStateChanged = onStateChanged;

      // 更新启用的认证方式
      if (enabledMethods != null && enabledMethods.isNotEmpty) {
        _enabledMethods = enabledMethods;
      }

      // 监听认证结果
      _authSubscription = _authManager!.authResultStream.listen(
        _handleAuthResult,
        onError: (error) {
          debugPrint('认证结果流错误: $error');
          _handleError('认证系统错误: $error');
        },
      );

      // 监听认证状态变化
      _authManager!.addListener(_handleStateChange);

      // 重要：需要先初始化MultiAuthManager，然后启动监听
      try {
        // 检查MultiAuthManager的状态
        debugPrint('MultiAuthManager当前状态: ${_authManager!.state}');

        // idle状态表示已初始化但未运行，这是正常的
        // 只有在error状态时才需要重新初始化
        if (_authManager!.state == MultiAuthState.error) {
          debugPrint('MultiAuthManager处于错误状态，需要重新初始化');
          throw Exception('MultiAuthManager处于错误状态，请重新初始化');
        }

        // 尝试启动监听
        debugPrint('开始启动MultiAuthManager监听...');
        await _authManager!.startListening();
        debugPrint('MultiAuthManager启动监听成功');
      } catch (e) {
        debugPrint('启动认证监听失败: $e');
        _handleError('启动认证监听失败: $e');
        rethrow;
      }

      _isRunning = true;
      _clearError();

      // 设置超时定时器
      _setTimeoutTimer(timeoutSeconds);

      debugPrint('闸机认证服务启动成功（无UI模式），超时时间: $timeoutSeconds秒');
    } catch (e) {
      _setError('启动闸机认证服务失败: $e');
      debugPrint('启动闸机认证服务失败: $e');
      rethrow;
    }
  }

  /// 停止闸机认证
  Future<void> stopGateAuth() async {
    if (!_isRunning) {
      debugPrint('闸机认证服务未在运行');
      return;
    }

    try {
      debugPrint('停止闸机认证服务...');
      
      // 取消超时定时器
      _cancelTimeoutTimer();
      
      // 取消事件订阅
      await _authSubscription?.cancel();
      _authSubscription = null;
      
      // 移除状态监听
      _authManager?.removeListener(_handleStateChange);
      
      // 停止认证监听
      await _authManager?.stopListening();
      
      _isRunning = false;
      _clearError();
      
      // 清空回调函数
      _onAuthResult = null;
      _onError = null;
      _onStateChanged = null;
      
      debugPrint('闸机认证服务已停止');
    } catch (e) {
      _setError('停止闸机认证服务失败: $e');
      debugPrint('停止闸机认证服务失败: $e');
    }
  }

  /// 重置认证服务
  Future<void> reset() async {
    debugPrint('重置闸机认证服务');
    
    if (_isRunning) {
      await stopGateAuth();
    }
    
    _isInitialized = false;
    _authManager = null;
    _clearError();
  }

  /// 处理认证结果
  void _handleAuthResult(AuthResult result) {
    debugPrint('收到认证结果: ${result.status}, 用户: ${result.userName}');
    
    // 取消超时定时器
    _cancelTimeoutTimer();
    
    // 调用回调函数
    _onAuthResult?.call(result);
    
    // 如果认证成功或失败，可以选择自动停止服务
    // 这里保持服务运行，由外部控制停止时机
  }

  /// 处理状态变化
  void _handleStateChange() {
    if (_authManager != null) {
      final state = _authManager!.state;
      debugPrint('认证状态变化: $state');
      _onStateChanged?.call(state);
    }
  }

  /// 处理错误
  void _handleError(String errorMessage) {
    _setError(errorMessage);
    _onError?.call(errorMessage);
  }

  /// 设置超时定时器
  void _setTimeoutTimer(int seconds) {
    _cancelTimeoutTimer();
    _timeoutTimer = Timer(Duration(seconds: seconds), () {
      debugPrint('闸机认证超时');
      _handleAuthResult(AuthResult(
        method: AuthMethod.face, // 默认方式
        status: AuthStatus.failureTimeout,
        errorMessage: '认证超时，请重试',
        timestamp: DateTime.now(),
      ));
    });
  }

  /// 取消超时定时器
  void _cancelTimeoutTimer() {
    _timeoutTimer?.cancel();
    _timeoutTimer = null;
  }

  /// 设置错误信息
  void _setError(String error) {
    _errorMessage = error;
  }

  /// 清除错误信息
  void _clearError() {
    _errorMessage = null;
  }

  /// 获取认证方式显示名称
  String _getAuthMethodName(AuthMethod method) {
    switch (method) {
      case AuthMethod.face:
        return '人脸识别';
      case AuthMethod.readerCard:
        return '读者证';
      case AuthMethod.qrCode:
        return '二维码';
      case AuthMethod.idCard:
        return '身份证';
      default:
        return method.toString();
    }
  }

  /// 获取服务状态信息
  Map<String, dynamic> getStatus() {
    return {
      'initialized': _isInitialized,
      'running': _isRunning,
      'enabled_methods': _enabledMethods.map(_getAuthMethodName).toList(),
      'auth_manager_state': _authManager?.state.toString(),
      'error_message': _errorMessage,
      'timeout_active': _timeoutTimer?.isActive ?? false,
    };
  }

  /// 释放资源
  void dispose() {
    debugPrint('释放闸机认证服务资源');
    
    stopGateAuth();
    _authManager = null;
    _isInitialized = false;
    
    debugPrint('闸机认证服务资源已释放');
  }
}
