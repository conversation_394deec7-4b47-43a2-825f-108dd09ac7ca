import 'dart:async';
import 'package:a3g/core/utils/window_util.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:get/get.dart';
import 'package:seasetting/seasetting.dart';

import '../../admin/views/admin_page2.dart';
import '../../adminLogin/views/admin_login_page.dart';
import '../config/gate_dev_config.dart';
import '../models/gate_command.dart';

import '../../../shared/utils/asset_util.dart';
import '../viewmodels/silence_page_viewmodel.dart';
import '../models/silence_page_state.dart';
import '../widgets/welcome_content.dart';
import '../widgets/waiting_content.dart';
import '../widgets/auth_content.dart';
import '../widgets/scanning_content.dart';
import '../widgets/book_check_content.dart';
import '../widgets/result_content.dart';
import '../widgets/not_in_whitelist_books_content.dart';
import '../widgets/error_content.dart';
import '../../auth/services/multi_auth_manager.dart';


class SilencePage extends StatefulWidget {
  const SilencePage({super.key});

  @override
  State<SilencePage> createState() => _SilencePageState();
}

class _SilencePageState extends State<SilencePage> {
  late SilencePageViewModel _viewModel;

  // 调试模式控制
  bool _debugMode = false;
  int _debugClickCount = 0;

  // 时钟点击控制
  int _clockClickCount = 0;
  Timer? _clockClickTimer;

  @override
  void initState() {
    super.initState();
    _initializeViewModel();
  }

  /// 初始化ViewModel
  void _initializeViewModel() async {
    _viewModel = SilencePageViewModel();
    try {
      // 首先初始化MultiAuthManager
      debugPrint('开始初始化MultiAuthManager...');
      final multiAuthManager = MultiAuthManager.instance;
      await multiAuthManager.initialize(context);
      debugPrint('MultiAuthManager初始化完成');

      // 然后初始化ViewModel
      await _viewModel.initialize();
    } catch (e) {
      debugPrint('初始化失败: $e');
    }
  }

  /// 处理调试模式点击
  void _handleDebugClick() {
    _debugClickCount++;

    // 连续点击3次进入调试模式
    if (_debugClickCount >= 5) {
      _debugClickCount = 0;
      Get.to(() => VerifyAccountPage(VerifyAccountType.local));
    }

    // 2秒后重置计数
    Future.delayed(Duration(seconds: 2), () {
      _debugClickCount = 0;
    });
  }

  /// 处理时钟点击
  void _handleClockClick() {
    setState(() {
      _clockClickCount++;
    });

    // 重置计时器
    _clockClickTimer?.cancel();
    _clockClickTimer = Timer(const Duration(seconds: 2), () {
      setState(() {
        _clockClickCount = 0;
      });
    });

    // 连续点击5次进入验证页面
    if (_clockClickCount >= 5) {
      _clockClickCount = 0;
      _clockClickTimer?.cancel();

      debugPrint('时钟连续点击5次，进入验证页面');

      // 进入本地验证页面
      Get.to(() => AdminPage2());
    }
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _viewModel,
      child: Consumer<SilencePageViewModel>(
        builder: (context, viewModel, child) {
          return Scaffold(
            body: Stack(
              children: [
                Container(
                  padding: EdgeInsets.all(30.p),
                  decoration: BoxDecoration(
                    image: DecorationImage(
                      image: AssetImage(AssetUtil.fullPath('tsinghua_bg')),
                      fit: BoxFit.cover,
                    ),
                  ),
                  child: Column(
                    children: [
                      _buildHeader(),
                      SizedBox(height: 243.p),
                      Expanded(
                        child: _buildDynamicContent(viewModel),
                      ),
                    ],
                  ),
                ),


                // 开发环境：最小模拟入口（与调试面板无关）
                if (GateDevConfig.allowShow) const _MiniSimulationOverlay(),
              ],
            ),
          );
        },
      ),
    );
  }

  /// 构建Header
  Widget _buildHeader() {
    return Row(
      children: [
        GestureDetector(
          onTap: _handleDebugClick,
          child: Image.asset(
            AssetUtil.fullPath('tsinghua_sm_logo'),
            width: 259.p,
            height: 50.p,
          ),
        ),
        Spacer(),

        SizedBox(width: 16.p),

        GestureDetector(
          onTap: _handleClockClick,
          child: StreamBuilder<DateTime>(
            stream: Stream.periodic(Duration(seconds: 1), (_) => DateTime.now()),
            builder: (context, snapshot) {
              final now = snapshot.data ?? DateTime.now();
              return Container(
                padding: EdgeInsets.symmetric(horizontal: 20.p, vertical: 10.p),
                decoration: BoxDecoration(
                  color: const Color.fromRGBO(255, 255, 255, 0.3),
                  borderRadius: BorderRadius.circular(100.p),
                ),
                child: Row(
                  children: [
                    Image.asset(
                      AssetUtil.fullPath('clock_icon'),
                      width: 26.p,
                      height: 26.p,
                    ),
                    Text(
                      '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}',
                      style: TextStyle(
                        fontSize: 26,
                        fontWeight: FontWeight.w400,
                        color: Colors.white,
                      ),
                    ),
                    SizedBox(width: 18.p),
                    Text(
                      '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}:${now.second.toString().padLeft(2, '0')}',
                      style: TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ],
    );
    
  }

    /// 构建动态内容
  Widget _buildDynamicContent(SilencePageViewModel viewModel) {
    switch (viewModel.currentPageState) {
      case SilencePageState.welcome:
        return WelcomeContent();
      case SilencePageState.waitingEnter:
      case SilencePageState.waitingExit:
        // 🔥 新增：等待到位状态显示等待内容
        return WaitingContent(data: viewModel.contentData);
      case SilencePageState.authenticating:
        return AuthContent();
      case SilencePageState.rfidScanning:
        return ScanningContent(data: viewModel.contentData);
      case SilencePageState.bookChecking:
        return BookCheckContent(data: viewModel.contentData);
      case SilencePageState.authSuccess:
      case SilencePageState.authFailed:
      case SilencePageState.exitAllowed:
        return ResultContent(data: viewModel.contentData);
      case SilencePageState.exitBlocked:
        return NotInWhitelistBooksContent(data: viewModel.contentData);
      case SilencePageState.error:
        return const SizedBox();
    }}


}
/// 🔥 更新：完整的闸机命令测试面板
class _MiniSimulationOverlay extends StatelessWidget {
  const _MiniSimulationOverlay({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final vm = context.read<SilencePageViewModel>();
    return Positioned(
      bottom: 20.p,
      right: 20.p,
      child: Container(
        padding: EdgeInsets.all(12.p),
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.8),
          borderRadius: BorderRadius.circular(8.p),
          border: Border.all(color: Colors.white24),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题
            Text(
              '闸机命令测试',
              style: TextStyle(
                color: Colors.white,
                fontSize: 14.p,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8.p),

            // 进馆流程
            Text(
              '进馆流程: (开始→到位→结束)',
              style: TextStyle(color: Colors.lightBlue, fontSize: 12.p),
            ),
            SizedBox(height: 4.p),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                _btn(context, '进馆开始', () => vm.simulateSerialCommand(GateCommand.enterStart), Colors.blue),
                SizedBox(width: 6.p),
                _btn(context, '进馆到位', () => vm.simulateSerialCommand(GateCommand.positionReached), Colors.blue),
                SizedBox(width: 6.p),
                _btn(context, '进馆结束', () => vm.simulateSerialCommand(GateCommand.enterEnd), Colors.blue),
              ],
            ),
            SizedBox(height: 8.p),

            // 出馆流程
            Text(
              '出馆流程: (开始→到位→结束)',
              style: TextStyle(color: Colors.lightGreen, fontSize: 12.p),
            ),
            SizedBox(height: 4.p),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                _btn(context, '出馆开始', () => vm.simulateSerialCommand(GateCommand.exitStart), Colors.green),
                SizedBox(width: 6.p),
                _btn(context, '出馆到位', () => vm.simulateSerialCommand(GateCommand.positionReached), Colors.green),
                SizedBox(width: 6.p),
                _btn(context, '出馆结束', () => vm.simulateSerialCommand(GateCommand.exitEnd), Colors.green),
              ],
            ),
            SizedBox(height: 8.p),

            // 发送命令
            Text(
              '发送命令:',
              style: TextStyle(color: Colors.orange, fontSize: 12.p),
            ),
            SizedBox(height: 4.p),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                _btn(context, '成功信号', () => vm.sendGateCommand('success_signal'), Colors.orange),
                SizedBox(width: 6.p),
                _btn(context, '失败信号', () => vm.sendGateCommand('failure_signal'), Colors.red),
                SizedBox(width: 6.p),
                _btn(context, '心跳', () => vm.simulateSerialCommand(GateCommand.heartbeat), Colors.purple),
              ],
            ),
            SizedBox(height: 8.p),

            // 控制命令
            Text(
              '控制命令:',
              style: TextStyle(color: Colors.yellow, fontSize: 12.p),
            ),
            SizedBox(height: 4.p),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                _btn(context, '进馆开门', () => vm.sendGateCommand('enter_door_open'), Colors.indigo),
                SizedBox(width: 6.p),
                _btn(context, '出馆开门', () => vm.sendGateCommand('exit_door_open'), Colors.teal),
                SizedBox(width: 6.p),
                _btn(context, '关闭门', () => vm.sendGateCommand('close_door'), Colors.grey),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _btn(BuildContext context, String text, VoidCallback onPressed, Color color) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: color.withOpacity(0.8),
        padding: EdgeInsets.symmetric(horizontal: 8.p, vertical: 4.p),
        minimumSize: Size(60.p, 28.p),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 10.p,
          color: Colors.white,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}
