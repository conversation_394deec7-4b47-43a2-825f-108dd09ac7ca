import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'shared_scan_pool_service.dart';


/// 通道处理队列服务 - 管理列表2
/// 每个通道独立维护，收集时间窗口内的条码
/// 🔥 集成现有GateCoordinator的状态管理
class ChannelQueueService {
  final String channelId;
  
  // 列表2：通道专用处理队列（自动去重）
  final Set<String> _processQueue = <String>{};
  
  // 处理队列变化流
  final StreamController<Set<String>> _queueController = 
      StreamController<Set<String>>.broadcast();
  
  // 错误流
  final StreamController<String> _errorController = 
      StreamController<String>.broadcast();
  
  // 收集定时器
  Timer? _collectTimer;
  
  // 是否正在收集
  bool _isCollecting = false;

  // 🔥 新增：时间窗口过滤
  DateTime? _timeWindowStart;
  bool _useTimeFilter = false;

  ChannelQueueService(this.channelId);

  /// 获取处理队列变化流
  Stream<Set<String>> get queueStream => _queueController.stream;
  
  /// 获取错误流
  Stream<String> get errorStream => _errorController.stream;
  
  /// 获取当前队列大小
  int get queueSize => _processQueue.length;
  
  /// 是否正在收集数据
  bool get isCollecting => _isCollecting;
  
  /// 获取当前处理队列快照
  Set<String> getProcessQueue() {
    return Set<String>.from(_processQueue);
  }

  /// 清空处理队列
  /// 通道开始出馆流程时调用
  void clearQueue() {
    try {
      debugPrint('[$channelId] 清空处理队列，当前大小: ${_processQueue.length}');
      _processQueue.clear();
      
      // 🔥 重置时间过滤器
      _timeWindowStart = null;
      _useTimeFilter = false;
      
      _notifyQueueChange();
      debugPrint('[$channelId] 处理队列已清空');
    } catch (e) {
      final errorMsg = '[$channelId] 清空处理队列失败: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
    }
  }

  /// 🔥 新增：设置时间窗口过滤器
  void setTimeWindowFilter(DateTime startTime) {
    _timeWindowStart = startTime;
    _useTimeFilter = true;
    debugPrint('[$channelId] 启用时间窗口过滤: $startTime');
  }

  /// 🔥 新增：禁用时间窗口过滤器
  void disableTimeFilter() {
    _useTimeFilter = false;
    _timeWindowStart = null;
    debugPrint('[$channelId] 禁用时间窗口过滤');
  }

  /// 开始收集数据
  /// 在"出馆开始"时调用，开始定时从共享池收集数据
  void startCollecting({Duration interval = const Duration(milliseconds: 100)}) {
    if (_isCollecting) {
      debugPrint('[$channelId] 已在收集数据中');
      return;
    }
    
    try {
      debugPrint('[$channelId] 开始收集数据，间隔: ${interval.inMilliseconds}ms');
      _isCollecting = true;
      
      _collectTimer = Timer.periodic(interval, (timer) {
        _collectFromSharedPool();
      });
      
      // 立即执行一次收集
      _collectFromSharedPool();
      
    } catch (e) {
      final errorMsg = '[$channelId] 开始收集数据失败: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
      _isCollecting = false;
    }
  }

  /// 停止收集数据
  /// 在"到达指定位置"时调用
  void stopCollecting() {
    if (!_isCollecting) {
      debugPrint('[$channelId] 未在收集数据');
      return;
    }
    
    try {
      debugPrint('[$channelId] 停止收集数据，最终队列大小: ${_processQueue.length}');
      _isCollecting = false;
      _collectTimer?.cancel();
      _collectTimer = null;
      
      // 最后执行一次收集，确保数据完整
      _collectFromSharedPool();
      
    } catch (e) {
      final errorMsg = '[$channelId] 停止收集数据失败: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
    }
  }

  /// 🔥 增强：从共享池收集数据到处理队列（带时间过滤）
  void _collectFromSharedPool() {
    try {
      final sharedPool = SharedScanPoolService.instance.getCurrentPool();
      final sizeBefore = _processQueue.length;
      
      Set<String> filteredBarcodes;
      
      if (_useTimeFilter && _timeWindowStart != null) {
        // 🔥 应用时间窗口过滤（备用策略）
        // 注意：这里我们假设条码是在时间窗口开始后扫描的
        // 实际实现中可能需要更复杂的时间戳管理
        filteredBarcodes = _applyTimeWindowFilter(sharedPool);
        
        if (filteredBarcodes.length < sharedPool.length) {
          final filteredCount = sharedPool.length - filteredBarcodes.length;
          debugPrint('⚠️ [$channelId] 时间窗口过滤：排除${filteredCount}个历史条码');
        }
      } else {
        // 正常模式：使用所有条码
        filteredBarcodes = sharedPool;
      }
      
      // 将过滤后的数据添加到处理队列（Set自动去重）
      _processQueue.addAll(filteredBarcodes);
      
      final newCount = _processQueue.length - sizeBefore;
      if (newCount > 0) {
        final filterInfo = _useTimeFilter ? ' (已过滤)' : '';
        debugPrint('[$channelId] 从共享池收集到${newCount}个新条码，总计: ${_processQueue.length}$filterInfo');
        _notifyQueueChange();
      }
    } catch (e) {
      final errorMsg = '[$channelId] 从共享池收集数据失败: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
    }
  }

  /// 🔥 增强：应用时间窗口过滤（基于真实时间戳）
  Set<String> _applyTimeWindowFilter(Set<String> barcodes) {
    if (!_useTimeFilter || _timeWindowStart == null) {
      return barcodes;
    }

    // 🔥 使用SharedScanPoolService的时间戳管理
    final sharedPool = SharedScanPoolService.instance;
    final filteredBarcodes = <String>{};
    
    for (final barcode in barcodes) {
      final timestamp = sharedPool.getBarcodeTimestamp(barcode);
      
      if (timestamp == null) {
        // 没有时间戳的条码，认为是历史数据，跳过
        continue;
      }
      
      if (timestamp.isAfter(_timeWindowStart!)) {
        // 在时间窗口开始后扫描的条码，保留
        filteredBarcodes.add(barcode);
      }
    }
    
    final filteredCount = barcodes.length - filteredBarcodes.length;
    if (filteredCount > 0) {
      debugPrint('[$channelId] 时间窗口过滤：排除${filteredCount}个历史条码，保留${filteredBarcodes.length}个新条码');
    }
    
    return filteredBarcodes;
  }

  /// 手动收集一次数据
  /// 可在需要时主动调用
  void collectOnce() {
    _collectFromSharedPool();
  }

  /// 添加单个条码到处理队列
  /// 一般不直接使用，主要通过收集机制添加
  void addBarcode(String barcode) {
    if (barcode.isEmpty) return;
    
    try {
      final sizeBefore = _processQueue.length;
      _processQueue.add(barcode);
      
      if (_processQueue.length > sizeBefore) {
        debugPrint('[$channelId] 直接添加条码: $barcode');
        _notifyQueueChange();
      }
    } catch (e) {
      final errorMsg = '[$channelId] 添加条码失败: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
    }
  }

  /// 批量添加条码到处理队列
  void addBarcodes(List<String> barcodes) {
    if (barcodes.isEmpty) return;
    
    try {
      final sizeBefore = _processQueue.length;
      _processQueue.addAll(barcodes.where((b) => b.isNotEmpty));
      
      if (_processQueue.length > sizeBefore) {
        debugPrint('[$channelId] 批量添加${barcodes.length}个条码，实际新增${_processQueue.length - sizeBefore}个');
        _notifyQueueChange();
      }
    } catch (e) {
      final errorMsg = '[$channelId] 批量添加条码失败: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
    }
  }

  /// 检查条码是否在处理队列中
  bool containsBarcode(String barcode) {
    return _processQueue.contains(barcode);
  }

  /// 移除指定条码
  bool removeBarcode(String barcode) {
    try {
      final removed = _processQueue.remove(barcode);
      if (removed) {
        debugPrint('[$channelId] 从处理队列移除条码: $barcode');
        _notifyQueueChange();
      }
      return removed;
    } catch (e) {
      final errorMsg = '[$channelId] 移除条码失败: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
      return false;
    }
  }

  /// 获取处理队列的JSON表示
  String toJson() {
    try {
      return jsonEncode({
        'channel_id': channelId,
        'queue': _processQueue.toList(),
        'is_collecting': _isCollecting,
        'timestamp': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      debugPrint('[$channelId] 序列化处理队列失败: $e');
      return '{"channel_id":"$channelId","queue":[],"is_collecting":false}';
    }
  }

  /// 通知处理队列变化
  void _notifyQueueChange() {
    if (!_queueController.isClosed) {
      _queueController.add(Set<String>.from(_processQueue));
    }
  }

  /// 获取处理队列统计信息
  Map<String, dynamic> getStatistics() {
    return {
      'channel_id': channelId,
      'queue_size': _processQueue.length,
      'is_collecting': _isCollecting,
      'use_time_filter': _useTimeFilter,
      'time_window_start': _timeWindowStart?.toIso8601String(),
      'queue_content': _processQueue.toList(),
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// 清理资源
  void dispose() {
    stopCollecting();
    _processQueue.clear();
    _queueController.close();
    _errorController.close();
  }
}
