import 'package:flutter/foundation.dart';
import 'package:sea_socket/sea_socket.dart';
import 'package:seasetting/seasetting.dart';
import 'door_lock_handler.dart';

/// 门锁开门功能测试类
class DoorLockTest {
  /// 测试门锁开门处理器
  static Future<void> testDoorLockHandler() async {
    debugPrint('开始测试门锁开门处理器...');
    
    try {
      // 创建测试用的读者信息
      final testReaderInfo = Sip2PatronInfoData(
        PersonName: '测试用户',
        PatronIdentifier: 'TEST001',
        ValidPatron: 'Y',
        ValidPatronPassword: 'Y',
      );
      
      // 创建门锁处理器
      final handler = DoorLockHandler();
      
      debugPrint('测试用户信息: ${testReaderInfo.PersonName} (${testReaderInfo.PatronIdentifier})');
      debugPrint('处理器优先级: ${handler.priority}');
      
      // 执行处理器
      final result = await handler.handle(testReaderInfo, '测试认证');
      
      debugPrint('处理器执行结果: $result');
      debugPrint('门锁开门处理器测试完成');
      
    } catch (e) {
      debugPrint('门锁开门处理器测试失败: $e');
    }
  }
  
  /// 测试门锁配置管理器初始化
  static Future<void> testConfigManagerInitialization() async {
    debugPrint('开始测试门锁配置管理器初始化...');

    try {
      // 测试初始化
      await DoorRelayConfigManager.initialize();
      debugPrint('门锁配置管理器初始化成功');

      // 测试获取配置
      final configs = await DoorRelayConfigManager.getAllConfigs();
      debugPrint('获取到 ${configs.length} 个门锁配置');

      for (int i = 0; i < configs.length; i++) {
        final config = configs[i];
        debugPrint('配置 ${i + 1}: ${config.name} (${config.comPort})');
      }

      debugPrint('门锁配置管理器测试完成');

    } catch (e) {
      debugPrint('门锁配置管理器测试失败: $e');
    }
  }

  /// 测试配置获取
  static Future<void> testConfigRetrieval() async {
    debugPrint('开始测试配置获取...');

    try {
      final handler = DoorLockHandler();

      // 使用反射或者创建一个测试方法来访问私有方法
      // 这里我们直接调用处理器来间接测试配置获取
      final testReaderInfo = Sip2PatronInfoData(
        PersonName: '配置测试用户',
        PatronIdentifier: 'CONFIG_TEST',
        ValidPatron: 'Y',
        ValidPatronPassword: 'Y',
      );

      await handler.handle(testReaderInfo, '配置测试');

      debugPrint('配置获取测试完成');

    } catch (e) {
      debugPrint('配置获取测试失败: $e');
    }
  }
}
