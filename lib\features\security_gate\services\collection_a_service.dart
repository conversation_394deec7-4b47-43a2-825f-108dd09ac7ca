import 'dart:async';
import 'dart:collection';
import 'package:flutter/foundation.dart';

/// 🔥 新架构：集合A管理服务基类
/// 
/// 职责：
/// 1. 维护各自的业务数据集合（集合A）
/// 2. 从共享池或主机同步数据到集合A
/// 3. 提供集合变化监听
/// 4. 与书籍信息查询解耦
abstract class CollectionAService {
  /// 集合A：存储当前业务流程中的条码
  final Set<String> _collectionA = <String>{};
  
  /// 集合变化通知流
  final StreamController<List<String>> _collectionChangeController =
      StreamController<List<String>>.broadcast();
  
  /// 同步状态
  bool _isSyncing = false;
  
  /// 最后同步时间
  DateTime? _lastSyncTime;

  /// 获取集合变化流
  Stream<List<String>> get collectionChangeStream => 
      _collectionChangeController.stream;
  
  /// 获取当前集合（只读）
  UnmodifiableSetView<String> get currentCollection => 
      UnmodifiableSetView(_collectionA);
  
  /// 获取集合大小
  int get collectionSize => _collectionA.length;
  
  /// 是否正在同步
  bool get isSyncing => _isSyncing;
  
  /// 最后同步时间
  DateTime? get lastSyncTime => _lastSyncTime;

  /// 抽象方法：从数据源同步数据到集合A
  /// 主机：从共享池同步
  /// 从机：从主机同步
  Future<List<String>> syncFromSource();

  /// 执行同步操作
  Future<void> performSync() async {
    if (_isSyncing) {
      debugPrint('⚠️ 同步已在进行中，跳过重复同步');
      return;
    }

    try {
      _isSyncing = true;
      debugPrint('🔄 开始同步数据到集合A...');
      
      final newData = await syncFromSource();
      final sizeBefore = _collectionA.length;
      
      // 添加新数据到集合A
      _collectionA.addAll(newData);
      
      final sizeAfter = _collectionA.length;
      final newCount = sizeAfter - sizeBefore;
      
      _lastSyncTime = DateTime.now();
      
      debugPrint('✅ 同步完成: 新增${newCount}个条码，总计${sizeAfter}个');
      
      // 如果有新数据，通知监听器
      if (newCount > 0) {
        _notifyCollectionChange();
      }
      
    } catch (e) {
      debugPrint('❌ 同步失败: $e');
      rethrow;
    } finally {
      _isSyncing = false;
    }
  }

  /// 清空集合A
  void clearCollection() {
    final sizeBefore = _collectionA.length;
    _collectionA.clear();
    _lastSyncTime = null;
    
    debugPrint('🧹 集合A已清空: ${sizeBefore} -> 0');
    
    if (sizeBefore > 0) {
      _notifyCollectionChange();
    }
  }

  /// 手动添加条码到集合A（用于测试或特殊场景）
  void addBarcode(String barcode) {
    if (barcode.isEmpty) return;
    
    final sizeBefore = _collectionA.length;
    _collectionA.add(barcode);
    
    if (_collectionA.length > sizeBefore) {
      debugPrint('➕ 手动添加条码到集合A: $barcode');
      _notifyCollectionChange();
    }
  }

  /// 检查条码是否在集合A中
  bool containsBarcode(String barcode) {
    return _collectionA.contains(barcode);
  }

  /// 通知集合变化
  void _notifyCollectionChange() {
    final currentList = _collectionA.toList();
    _collectionChangeController.add(currentList);
    debugPrint('📢 集合A变化通知: ${currentList.length}个条码');
  }

  /// 获取集合A的详细状态信息
  Map<String, dynamic> getStatusInfo() {
    return {
      'collection_size': _collectionA.length,
      'is_syncing': _isSyncing,
      'last_sync_time': _lastSyncTime?.toIso8601String(),
      'barcodes': _collectionA.toList(),
    };
  }

  /// 释放资源
  void dispose() {
    _collectionChangeController.close();
    _collectionA.clear();
    debugPrint('🗑️ CollectionAService已释放');
  }
}
