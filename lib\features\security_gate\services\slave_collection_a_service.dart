import 'dart:async';
import 'package:flutter/foundation.dart';
import 'collection_a_service.dart';
import 'master_slave_extension.dart';

/// 🔥 从机集合A服务
/// 
/// 职责：
/// 1. 从主机同步数据到从机集合A
/// 2. 在出馆开始等特定时机触发同步
/// 3. 为从机业务逻辑提供数据集合
class SlaveCollectionAService extends CollectionAService {
  static SlaveCollectionAService? _instance;
  
  /// 单例模式
  static SlaveCollectionAService get instance {
    _instance ??= SlaveCollectionAService._internal();
    return _instance!;
  }
  
  SlaveCollectionAService._internal();

  /// 主从机扩展服务引用
  MasterSlaveExtension get _masterSlaveExtension => MasterSlaveExtension.instance;

  @override
  Future<List<String>> syncFromSource() async {
    try {
      debugPrint('🔄 从机从主机同步数据...');
      
      // 检查主从机扩展是否启用
      if (!_masterSlaveExtension.isEnabled || _masterSlaveExtension.isMaster) {
        debugPrint('⚠️ 主从机扩展未启用或当前为主机模式');
        return [];
      }
      
      // 从主机请求数据
      final masterData = await _requestDataFromMaster();
      
      debugPrint('📊 主机返回数据: ${masterData.length}个条码');
      if (masterData.isNotEmpty) {
        debugPrint('📋 条码列表: ${masterData.take(5).join(', ')}${masterData.length > 5 ? '...' : ''}');
      }
      
      return masterData;
      
    } catch (e) {
      debugPrint('❌ 从机同步失败: $e');
      rethrow;
    }
  }

  /// 🔥 从主机请求数据
  Future<List<String>> _requestDataFromMaster() async {
    try {
      // 使用主从机扩展的出馆开始处理功能
      final data = await _masterSlaveExtension.handleExitStart();
      return data;
    } catch (e) {
      debugPrint('❌ 从主机请求数据失败: $e');
      return [];
    }
  }

  /// 🔥 出馆开始时的同步逻辑
  Future<void> syncOnExitStart() async {
    try {
      debugPrint('🚪 从机出馆开始：请求主机清空并同步数据到集合A');

      // 🔥 关键：先请求主机清空共享池
      debugPrint('📤 请求主机清空共享池...');
      final clearSuccess = await _requestMasterClearPool();
      if (!clearSuccess) {
        debugPrint('⚠️ 请求主机清空失败，继续执行同步');
      }

      // 清空从机集合A，确保数据隔离
      clearCollection();

      // 立即从主机同步数据（无需额外等待）
      await performSync();

      debugPrint('✅ 从机出馆开始同步完成');

    } catch (e) {
      debugPrint('❌ 从机出馆开始同步失败: $e');
      rethrow;
    }
  }

  /// 🔥 请求主机清空共享池
  Future<bool> _requestMasterClearPool() async {
    try {
      // 检查主从机扩展是否启用
      if (!_masterSlaveExtension.isEnabled || _masterSlaveExtension.isMaster) {
        debugPrint('⚠️ 主从机扩展未启用或当前为主机模式');
        return false;
      }

      // 🔥 使用主从机扩展的内置清空请求功能
      debugPrint('📤 向主机发送清空请求...');

      // 使用MasterSlaveExtension的handleExitStart方法
      // 这个方法内部会先请求清空，然后获取数据
      try {
        // 调用主从机扩展的出馆开始处理，它会自动处理清空请求
        await _masterSlaveExtension.handleExitStart();
        debugPrint('✅ 主机清空请求通过MasterSlaveExtension成功');
        return true;
      } catch (e) {
        debugPrint('❌ 通过MasterSlaveExtension请求清空失败: $e');
        return false;
      }

    } catch (e) {
      debugPrint('❌ 请求主机清空失败: $e');
      return false;
    }
  }

  /// 🔥 手动触发同步（用于测试或调试）
  Future<void> manualSync() async {
    try {
      debugPrint('🔧 从机手动同步...');
      await performSync();
    } catch (e) {
      debugPrint('❌ 从机手动同步失败: $e');
      rethrow;
    }
  }

  /// 🔥 获取从机特有的状态信息
  Map<String, dynamic> getSlaveStatusInfo() {
    final baseInfo = getStatusInfo();
    
    return {
      ...baseInfo,
      'service_type': 'slave',
      'master_slave_enabled': _masterSlaveExtension.isEnabled,
      'is_master': _masterSlaveExtension.isMaster,
      'channel_id': _masterSlaveExtension.channelId,
      'sync_source': 'master_host',
    };
  }

  /// 🔥 启动持续数据获取（从机特有功能）
  Timer? _continuousDataTimer;
  
  /// 启动持续数据获取模式
  void startContinuousDataFetch({Duration interval = const Duration(milliseconds: 500)}) {
    stopContinuousDataFetch(); // 先停止之前的定时器
    
    _continuousDataTimer = Timer.periodic(interval, (timer) async {
      try {
        await performSync();
      } catch (e) {
        debugPrint('❌ 持续数据获取失败: $e');
      }
    });
    
    debugPrint('🚀 从机持续数据获取模式已启动 (间隔: ${interval.inMilliseconds}ms)');
  }
  
  /// 停止持续数据获取模式
  void stopContinuousDataFetch() {
    _continuousDataTimer?.cancel();
    _continuousDataTimer = null;
    debugPrint('⏹️ 从机持续数据获取模式已停止');
  }

  /// 🔥 检查与主机的连接状态
  bool checkMasterConnection() {
    try {
      final isConnected = _masterSlaveExtension.isEnabled && 
                         !_masterSlaveExtension.isMaster;
      
      debugPrint('🔍 主机连接检查: ${isConnected ? '✅ 已连接' : '❌ 未连接'}');
      return isConnected;
    } catch (e) {
      debugPrint('❌ 检查主机连接失败: $e');
      return false;
    }
  }

  /// 🔥 重新连接主机
  Future<bool> reconnectToMaster() async {
    try {
      debugPrint('🔄 尝试重新连接主机...');
      
      // 这里可以添加重连逻辑
      // 目前依赖主从机扩展的自动重连机制
      
      final isConnected = checkMasterConnection();
      if (isConnected) {
        debugPrint('✅ 重新连接主机成功');
        // 重连成功后立即同步一次数据
        await performSync();
      } else {
        debugPrint('❌ 重新连接主机失败');
      }
      
      return isConnected;
    } catch (e) {
      debugPrint('❌ 重新连接主机异常: $e');
      return false;
    }
  }

  @override
  void dispose() {
    stopContinuousDataFetch();
    super.dispose();
    debugPrint('🗑️ SlaveCollectionAService已释放');
  }
}
