import 'package:flutter/material.dart';
import 'package:a3g/core/utils/window_util.dart';
import '../models/silence_page_state.dart';
import 'tip_card.dart';

/// 结果提示界面内容组件
class ResultContent extends StatefulWidget {
  final UIContentData data;
  
  const ResultContent({
    Key? key,
    required this.data,
  }) : super(key: key);

  @override
  State<ResultContent> createState() => _ResultContentState();
}

class _ResultContentState extends State<ResultContent>
    with TickerProviderStateMixin {
  
  late AnimationController _scaleController;
  late AnimationController _fadeController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;
  
  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimations();
  }
  
  void _initializeAnimations() {
    _scaleController = AnimationController(
      duration: Duration(milliseconds: 600),
      vsync: this,
    );
    
    _fadeController = AnimationController(
      duration: Duration(milliseconds: 400),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));
  }
  
  void _startAnimations() {
    _fadeController.forward();
    _scaleController.forward();
  }
  
  @override
  Widget build(BuildContext context) {
    final isSuccess = widget.data.isSuccess ?? false;
    final userName = widget.data.userName;

    return AnimatedBuilder(
      animation: Listenable.merge([_scaleAnimation, _fadeAnimation]),
      builder: (context, child) {
        return Opacity(
          opacity: _fadeAnimation.value,
          child: Transform.scale(
            scale: _scaleAnimation.value,
            child: Center(
              child: TipCard(
                child: _buildCardContent(isSuccess, userName),
              ),
            ),
          ),
        );
      },
    );
  }

  /// 构建卡片内容
  Widget _buildCardContent(bool isSuccess, String? userName) {
    // 检查是否为出馆检查通过（message包含"欢迎再次光临"）
    final isExitAllowed = widget.data.message?.contains('欢迎再次光临') == true;

    if (isExitAllowed) {
      // 出馆检查通过：只显示欢迎信息
      return Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            widget.data.message ?? '',
            style: TextStyle(
              color: const Color(0xFF242424),
              fontSize: 28.p,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      );
    }

    // 其他情况（进馆认证成功/失败）：显示完整内容
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // 结果图标
        Container(
          width: 120.p,
          height: 120.p,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: isSuccess
                ? Colors.green.withOpacity(0.2)
                : Colors.red.withOpacity(0.2),
            border: Border.all(
              color: isSuccess ? Colors.green : Colors.red,
              width: 3,
            ),
          ),
          child: Icon(
            isSuccess ? Icons.check_circle : Icons.error,
            size: 60.p,
            color: isSuccess ? Colors.green : Colors.red,
          ),
        ),

        SizedBox(height: 20.p),

        // 标题
        Text(
          widget.data.title ?? (isSuccess ? '成功' : '失败'),
          style: TextStyle(
            color: isSuccess ? Colors.green : Colors.red,
            fontSize: 32.p,
            fontWeight: FontWeight.bold,
          ),
        ),

        SizedBox(height: 15.p),

        // 用户名显示（认证成功时）
        if (isSuccess && userName != null) ...[
          Text(
            userName,
            style: TextStyle(
              color: const Color(0xFF242424),
              fontSize: 28.p,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 10.p),
        ],

        // 消息内容
        Text(
          widget.data.message ?? '',
          style: TextStyle(
            color: const Color(0xFF242424),
            fontSize: 20.p,
            fontWeight: FontWeight.w400,
            height: 1.3,
          ),
          textAlign: TextAlign.center,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),

        SizedBox(height: 20.p),

        // 额外信息显示
        if (isSuccess) _buildSuccessIndicator(),
      ],
    );
  }
  
  /// 构建成功指示器
  Widget _buildSuccessIndicator() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.arrow_forward,
          color: Colors.green,
          size: 24.p,
        ),
        SizedBox(width: 10.p),
        Text(
          '请通过',
          style: TextStyle(
            color: Colors.green,
            fontSize: 18.p,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }
  
  @override
  void dispose() {
    _scaleController.dispose();
    _fadeController.dispose();
    super.dispose();
  }
}
