# 获取图书信息GET

## OpenAPI Specification

```yaml
openapi: 3.0.1
info:
  title: ''
  description: ''
  version: 1.0.0
paths:
  /tunano/cdc/acs/v1/cdc/acs/queryBookInfo:
    get:
      summary: 获取图书信息GET
      deprecated: false
      description: ''
      tags:
        - ACS
      parameters:
        - name: LibraryCode
          in: query
          description: 馆代码
          required: true
          example: CN-518000-HHLIB
          schema:
            type: string
        - name: Mac
          in: query
          description: 设备ID
          required: true
          example: FF-FF-FF-FF-FF-FF
          schema:
            type: string
        - name: BookSn
          in: query
          description: 图书条码号
          required: true
          example: '0000651'
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                  requestObject:
                    type: string
                  operation:
                    type: string
                  errorCode:
                    type: integer
                    description: 0成功, 其他失败
                  message:
                    type: string
                    description: 提示信息
                  result:
                    type: object
                    properties:
                      borrowFlag:
                        type: boolean
                        description: 是否在借
                      bookingFlag:
                        type: boolean
                        description: 是否预借
                      bookSn:
                        type: string
                        description: 条码号
                      bookTitle:
                        type: string
                        description: 书名
                      author:
                        type: 'null'
                        description: 作者
                      isbn:
                        type: string
                        description: ISBN
                      callNumber:
                        type: string
                        description: 索书号
                      publisher:
                        type: 'null'
                        description: 出版社
                      needReturnDate:
                        type: 'null'
                        description: 应归还时间
                      borrowPastronSn:
                        type: 'null'
                        description: 借出读者证号
                      circulationStatus:
                        type: integer
                        description: 图书状态
                      message:
                        type: string
                        description: 提示信息
                    required:
                      - borrowFlag
                      - bookingFlag
                      - bookSn
                      - bookTitle
                      - author
                      - isbn
                      - callNumber
                      - publisher
                      - needReturnDate
                      - borrowPastronSn
                      - circulationStatus
                      - message
                    x-apifox-orders:
                      - borrowFlag
                      - bookingFlag
                      - bookSn
                      - bookTitle
                      - author
                      - isbn
                      - callNumber
                      - publisher
                      - needReturnDate
                      - borrowPastronSn
                      - circulationStatus
                      - message
                required:
                  - id
                  - requestObject
                  - operation
                  - errorCode
                  - message
                  - result
                x-apifox-orders:
                  - id
                  - requestObject
                  - operation
                  - errorCode
                  - message
                  - result
              example:
                id: '2023062618065868351801'
                requestObject: cdc.acs
                operation: querybookinfo
                errorCode: 0
                message: 查询成功
                result:
                  borrowFlag: false
                  bookingFlag: false
                  bookSn: '0000651'
                  bookTitle: 论语:儒者的诤言
                  author: null
                  isbn: 7-108-00394-5
                  callNumber: '0000651'
                  price: 0
                  publisher: null
                  page: 0
                  keywords: null
                  circulationType: 0
                  permanentLocation: 总分馆书库
                  currentLocation: 总分馆书籍中转（不留书）
                  currentShelfLocation: null
                  needReturnDate: null
                  needPickUpDate: null
                  borrowedDate: null
                  borrowPastronSn: null
                  bookingPatronSnList: []
                  circulationStatus: 1
                  circulationStatusValue: '1'
                  message: 查询成功
          headers: {}
          x-apifox-name: 成功
      security: []
      x-apifox-folder: ACS
      x-apifox-status: released
      x-run-in-apifox: https://app.apifox.com/web/project/2805692/apis/api-91250811-run
components:
  schemas: {}
  securitySchemes: {}
servers:
  - url: http://************:9000
    description: 开发环境
security: []

```