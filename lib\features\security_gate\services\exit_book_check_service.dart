import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../../device/services/unified_api_config_manager.dart';

/// 出馆书籍检查服务 - 处理新的三步API检查流程
/// 使用统一API配置管理器，所有配置从配置文件读取
class ExitBookCheckService {
  static ExitBookCheckService? _instance;
  static ExitBookCheckService get instance => _instance ??= ExitBookCheckService._();
  ExitBookCheckService._();

  // 统一配置管理器
  final _configManager = UnifiedApiConfigManager.instance;

  /// 第一步：查询图书是否在白名单
  /// 输入：UID列表
  /// 输出：不在白名单的UID字符串（逗号分隔）
  Future<String> checkWhitelist(List<String> uids) async {
    try {
      debugPrint('🔍 第一步：检查白名单，UID数量: ${uids.length}');
      debugPrint('🔍 输入UID列表: $uids');

      final url = await _configManager.getBookWhitelistUrl();
      final requestBody = {
        'Taglist': uids.map((uid) => {'Tid': uid}).toList(),
      };

      debugPrint('');
      debugPrint('📤 ========== 白名单检查接口 ==========');
      debugPrint('📤 接口名称: 查询图书是否在白名单');
      debugPrint('📤 请求URL: $url');
      debugPrint('📤 请求方法: POST');
      debugPrint('📤 请求头: Content-Type: application/json');
      debugPrint('📤 请求参数: ${jsonEncode(requestBody)}');
      debugPrint('📤 请求参数解析:');
      debugPrint('📤   - Taglist: 标签列表，包含${uids.length}个UID');
      for (int i = 0; i < uids.length; i++) {
        debugPrint('📤     [$i] Tid: ${uids[i]}');
      }
      debugPrint('📤 =====================================');
      debugPrint('');

      final response = await http.post(
        Uri.parse(url),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(requestBody),
      );

      debugPrint('');
      debugPrint('📥 ========== 白名单检查响应 ==========');
      debugPrint('📥 响应状态码: ${response.statusCode}');
      debugPrint('📥 响应原始数据: ${response.body}');
      debugPrint('');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        debugPrint('📥 响应数据解析:');
        debugPrint('📥   - errorcode: ${data['errorcode']} (0=在白名单内, 其他=不在白名单内)');
        debugPrint('📥   - message: ${data['message']}');
        debugPrint('📥   - result: ${data['result']}');

        if (data['errorcode'] == 0) {
          // 全部在白名单内
          debugPrint('✅ 白名单检查结果: 所有书籍都在白名单内');
          debugPrint('✅ 返回值: 空字符串 (表示没有不在白名单的UID)');
          debugPrint('📥 =====================================');
          debugPrint('');
          return '';
        } else {
          // 有书籍不在白名单内，从message中提取UID
          final message = data['message'] as String;
          final notInWhitelistUids = _extractUidsFromMessage(message);
          debugPrint('⚠️ 白名单检查结果: 有书籍不在白名单内');
          debugPrint('⚠️ 不在白名单的UID字符串: $notInWhitelistUids');
          debugPrint('⚠️ 返回值: $notInWhitelistUids');
          debugPrint('📥 =====================================');
          debugPrint('');
          return notInWhitelistUids;
        }
      } else {
        debugPrint('❌ HTTP请求失败: ${response.statusCode}');
        debugPrint('❌ 错误响应: ${response.body}');
        debugPrint('📥 =====================================');
        debugPrint('');
        throw Exception('白名单检查请求失败: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('❌ 白名单检查异常: $e');
      debugPrint('📥 =====================================');
      debugPrint('');
      rethrow;
    }
  }

  /// 从错误消息中提取UID
  String _extractUidsFromMessage(String message) {
    // 消息格式: "白名单不包含标签E004015304F3DD22,E004015305F68508, 耗时0ms"
    // 需要提取所有不在白名单的UID

    debugPrint('🔍 解析白名单错误消息: $message');

    // 方法1：提取"标签"和"耗时"之间的所有内容
    final regex1 = RegExp(r'标签([^,]+(?:,[^,]+)*),?\s*耗时');
    final match1 = regex1.firstMatch(message);
    if (match1 != null) {
      final uidsString = match1.group(1)!.trim();
      debugPrint('🔍 方法1提取到的UID字符串: $uidsString');

      // 验证提取的内容是否包含有效的UID格式
      final uids = uidsString.split(',').map((uid) => uid.trim()).where((uid) => uid.isNotEmpty).toList();
      if (uids.isNotEmpty && uids.every((uid) => uid.length > 10)) { // UID通常比较长
        final result = uids.join(',');
        debugPrint('🔍 方法1验证通过，提取到${uids.length}个UID: $result');
        return result;
      }
    }

    // 方法2：尝试更宽松的匹配，提取"标签"后面到"耗时"前面的内容
    final regex2 = RegExp(r'标签([^耗]+)耗时');
    final match2 = regex2.firstMatch(message);
    if (match2 != null) {
      final uidsString = match2.group(1)!.trim().replaceAll(RegExp(r',\s*$'), ''); // 移除末尾的逗号和空格
      debugPrint('🔍 方法2提取到的UID字符串: $uidsString');

      // 验证并清理UID列表
      final uids = uidsString.split(',').map((uid) => uid.trim()).where((uid) => uid.isNotEmpty && uid.length > 10).toList();
      if (uids.isNotEmpty) {
        final result = uids.join(',');
        debugPrint('🔍 方法2验证通过，提取到${uids.length}个UID: $result');
        return result;
      }
    }

    // 方法3：如果消息中包含具体的UID（假设以逗号分隔）
    if (message.contains(',') && !message.contains('耗时')) {
      // 可能是直接返回的UID列表
      debugPrint('🔍 方法3：可能是直接的UID列表');
      return message;
    }

    // 方法4：如果都不匹配，返回空字符串（表示解析失败）
    debugPrint('⚠️ 无法解析白名单错误消息，返回空字符串');
    return '';
  }

  /// 第二步：根据标签UID查询条码
  /// 输入：不在白名单的UID字符串（逗号分隔）
  /// 输出：条码列表
  Future<List<String>> getBarcodesFromUids(String uidsString) async {
    if (uidsString.isEmpty) {
      debugPrint('🔍 第二步：UID转条码，输入为空，直接返回空列表');
      return [];
    }

    try {
      final uids = uidsString.split(',').where((uid) => uid.isNotEmpty).toList();
      debugPrint('🔍 第二步：UID转条码，UID数量: ${uids.length}');
      debugPrint('🔍 输入UID字符串: $uidsString');
      debugPrint('🔍 解析后UID列表: $uids');

      List<String> barcodes = [];

      for (int i = 0; i < uids.length; i++) {
        String uid = uids[i];
        try {
          final url = await _configManager.getBookUidToBarcodeUrl(uid);

          debugPrint('');
          debugPrint('📤 ========== UID转条码接口 (${i + 1}/${uids.length}) ==========');
          debugPrint('📤 接口名称: 根据标签UID查询条码');
          debugPrint('📤 请求URL: $url');
          debugPrint('📤 请求方法: GET');
          debugPrint('📤 路径参数:');
          debugPrint('📤   - libraryCode: 从URL中提取的馆代码');
          debugPrint('📤   - Uid: $uid (标签UID/TID)');
          debugPrint('📤 =====================================');
          debugPrint('');

          final response = await http.get(Uri.parse(url));

          debugPrint('');
          debugPrint('📥 ========== UID转条码响应 (${i + 1}/${uids.length}) ==========');
          debugPrint('📥 UID: $uid');
          debugPrint('📥 响应状态码: ${response.statusCode}');
          debugPrint('📥 响应原始数据: ${response.body}');
          debugPrint('');

          if (response.statusCode == 200) {
            final data = jsonDecode(response.body);

            debugPrint('📥 响应数据解析:');
            debugPrint('📥   - id: ${data['id']}');
            debugPrint('📥   - requestObject: ${data['requestObject']}');
            debugPrint('📥   - operation: ${data['operation']}');
            debugPrint('📥   - errorCode: ${data['errorCode']} (0=成功, 其他=失败)');
            debugPrint('📥   - message: ${data['message']}');

            if (data['errorCode'] == 0 && data['result'] != null) {
              final result = data['result'];
              final barcode = result['barcode'] as String;
              debugPrint('📥   - result:');
              debugPrint('📥     * libraryCode: ${result['libraryCode']}');
              debugPrint('📥     * uid: ${result['uid']}');
              debugPrint('📥     * barcode: ${result['barcode']}');
              debugPrint('📥     * updated: ${result['updated']}');
              debugPrint('📥     * created: ${result['created']}');

              barcodes.add(barcode);
              debugPrint('✅ UID转条码成功: $uid → $barcode');
            } else {
              debugPrint('⚠️ UID转条码失败: $uid');
              debugPrint('⚠️ 错误信息: ${data['message']}');
            }
          } else {
            debugPrint('❌ HTTP请求失败: ${response.statusCode}');
            debugPrint('❌ 错误响应: ${response.body}');
          }

          debugPrint('📥 =====================================');
          debugPrint('');
        } catch (e) {
          debugPrint('❌ UID $uid 查询条码异常: $e');
          debugPrint('');
        }
      }

      debugPrint('✅ 第二步完成，获得条码数量: ${barcodes.length}');
      debugPrint('✅ 获得的条码列表: $barcodes');
      return barcodes;
    } catch (e) {
      debugPrint('❌ UID转条码失败: $e');
      rethrow;
    }
  }

  /// 第三步：获取图书信息
  /// 输入：条码列表
  /// 输出：图书信息列表
  Future<List<BookInfo>> getBookInfos(List<String> barcodes) async {
    if (barcodes.isEmpty) {
      debugPrint('🔍 第三步：获取图书信息，输入为空，直接返回空列表');
      return [];
    }

    try {
      debugPrint('🔍 第三步：获取图书信息，条码数量: ${barcodes.length}');
      debugPrint('🔍 输入条码列表: $barcodes');

      List<BookInfo> bookInfos = [];

      for (int i = 0; i < barcodes.length; i++) {
        String barcode = barcodes[i];
        try {
          final url = await _configManager.getBookInfoQueryUrl(barcode);

          debugPrint('');
          debugPrint('📤 ========== 获取图书信息接口 (${i + 1}/${barcodes.length}) ==========');
          debugPrint('📤 接口名称: 获取图书信息GET');
          debugPrint('📤 请求URL: $url');
          debugPrint('📤 请求方法: GET');
          debugPrint('📤 查询参数:');
          debugPrint('📤   - barcode: $barcode (图书条码)');
          debugPrint('📤 =====================================');
          debugPrint('');

          final response = await http.get(Uri.parse(url));

          debugPrint('');
          debugPrint('📥 ========== 图书信息响应 (${i + 1}/${barcodes.length}) ==========');
          debugPrint('📥 条码: $barcode');
          debugPrint('📥 响应状态码: ${response.statusCode}');
          debugPrint('📥 响应原始数据: ${response.body}');
          debugPrint('');

          if (response.statusCode == 200) {
            final data = jsonDecode(response.body);

            debugPrint('📥 响应数据解析:');
            debugPrint('📥   - errorCode: ${data['errorCode']} (0=成功, 其他=失败)');
            debugPrint('📥   - message: ${data['message']}');

            if (data['errorCode'] == 0 && data['result'] != null) {
              final result = data['result'];
              debugPrint('📥   - result:');
              debugPrint('📥     * bookSn: ${result['bookSn']} (图书条码)');
              debugPrint('📥     * bookTitle: ${result['bookTitle']} (书名)');
              debugPrint('📥     * author: ${result['author']} (作者)');
              debugPrint('📥     * isbn: ${result['isbn']} (ISBN)');
              debugPrint('📥     * callNumber: ${result['callNumber']} (索书号)');
              debugPrint('📥     * publisher: ${result['publisher']} (出版社)');
              debugPrint('📥     * borrowFlag: ${result['borrowFlag']} (借阅标志)');
              debugPrint('📥     * bookingFlag: ${result['bookingFlag']} (预约标志)');
              debugPrint('📥     * circulationStatus: ${result['circulationStatus']} (流通状态)');
              debugPrint('📥     * message: ${result['message']} (消息)');

              final bookInfo = BookInfo(
                barcode: result['bookSn'] ?? barcode,
                bookName: result['bookTitle'] ?? '未知书名',
                author: result['author'] ?? '未知作者',
                isbn: result['isbn'] ?? '',
                callNumber: result['callNumber'] ?? '',
                publisher: result['publisher'] ?? '未知出版社',
                borrowFlag: result['borrowFlag'] ?? false,
                bookingFlag: result['bookingFlag'] ?? false,
                circulationStatus: result['circulationStatus'] ?? 0,
                message: result['message'] ?? '',
              );
              bookInfos.add(bookInfo);
              debugPrint('✅ 图书信息获取成功: $barcode → ${bookInfo.bookName}');
            } else {
              debugPrint('⚠️ 图书信息获取失败: $barcode');
              debugPrint('⚠️ 错误信息: ${data['message']}');
            }
          } else {
            debugPrint('❌ HTTP请求失败: ${response.statusCode}');
            debugPrint('❌ 错误响应: ${response.body}');
          }

          debugPrint('📥 =====================================');
          debugPrint('');
        } catch (e) {
          debugPrint('❌ 条码 $barcode 查询图书信息异常: $e');
          debugPrint('');
        }
      }

      debugPrint('✅ 第三步完成，获得图书信息数量: ${bookInfos.length}');
      debugPrint('✅ 获得的图书信息列表:');
      for (int i = 0; i < bookInfos.length; i++) {
        final book = bookInfos[i];
        debugPrint('✅   [${i + 1}] ${book.barcode} - ${book.bookName} (${book.author})');
      }
      return bookInfos;
    } catch (e) {
      debugPrint('❌ 获取图书信息失败: $e');
      rethrow;
    }
  }

  /// 完整的三步检查流程
  /// 输入：UID列表
  /// 输出：检查结果
  Future<ExitBookCheckResult> performFullCheck(List<String> uids) async {
    try {
      debugPrint('🚀 开始完整的三步书籍检查流程，UID数量: ${uids.length}');

      // 第一步：白名单检查
      final notInWhitelistUids = await checkWhitelist(uids);
      
      if (notInWhitelistUids.isEmpty) {
        // 全部在白名单内，允许通过
        debugPrint('✅ 所有书籍都在白名单内，允许通过');
        return ExitBookCheckResult(
          allowPass: true,
          summary: '所有书籍都在白名单内，请通过',
          notInWhitelistBooks: [],
          totalUids: uids.length,
          whitelistUids: uids.length,
          notWhitelistUids: 0,
        );
      }

      // 第二步：UID转条码
      final barcodes = await getBarcodesFromUids(notInWhitelistUids);

      // 第三步：获取图书信息
      final bookInfos = await getBookInfos(barcodes);

      // 🔥 关键修改：有书不在白名单，不允许通过
      final notWhitelistCount = notInWhitelistUids.split(',').where((s) => s.isNotEmpty).length;
      final result = ExitBookCheckResult(
        allowPass: false, // 🔥 只要有书不在白名单，就不允许通过
        summary: _generateSummary(uids.length, notWhitelistCount, bookInfos),
        notInWhitelistBooks: bookInfos,
        totalUids: uids.length,
        whitelistUids: uids.length - notWhitelistCount,
        notWhitelistUids: notWhitelistCount,
      );

      debugPrint('❌ 三步检查完成: ${result.summary}');
      return result;
    } catch (e) {
      debugPrint('❌ 三步检查流程失败: $e');
      return ExitBookCheckResult(
        allowPass: true, // 出错时默认允许通过
        summary: '书籍检查服务异常，默认允许通过',
        notInWhitelistBooks: [],
        totalUids: uids.length,
        whitelistUids: 0,
        notWhitelistUids: 0,
      );
    }
  }

  /// 生成检查结果摘要
  String _generateSummary(int totalUids, int notWhitelistUids, List<BookInfo> bookInfos) {
    if (notWhitelistUids == 0) {
      return '所有书籍都在白名单内，请通过';
    } else {
      return '检测到 $totalUids 本书籍，其中 $notWhitelistUids 本不在白名单内，禁止通行';
    }
  }
}

/// 图书信息模型
class BookInfo {
  final String barcode;
  final String bookName;
  final String? author;
  final String? isbn;
  final String? callNumber;
  final String? publisher;
  final bool borrowFlag;
  final bool bookingFlag;
  final int circulationStatus;
  final String message;

  BookInfo({
    required this.barcode,
    required this.bookName,
    this.author,
    this.isbn,
    this.callNumber,
    this.publisher,
    required this.borrowFlag,
    required this.bookingFlag,
    required this.circulationStatus,
    required this.message,
  });

  Map<String, dynamic> toJson() {
    return {
      'barcode': barcode,
      'bookName': bookName,
      'author': author,
      'isbn': isbn,
      'callNumber': callNumber,
      'publisher': publisher,
      'borrowFlag': borrowFlag,
      'bookingFlag': bookingFlag,
      'circulationStatus': circulationStatus,
      'message': message,
    };
  }
}

/// 出馆书籍检查结果
class ExitBookCheckResult {
  final bool allowPass;
  final String summary;
  final List<BookInfo> notInWhitelistBooks;
  final int totalUids;
  final int whitelistUids;
  final int notWhitelistUids;

  ExitBookCheckResult({
    required this.allowPass,
    required this.summary,
    required this.notInWhitelistBooks,
    required this.totalUids,
    required this.whitelistUids,
    required this.notWhitelistUids,
  });
}
