import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:hardware/hardware.dart' hide GateCommand;
import 'package:provider/provider.dart';

import '../models/gate_state.dart';
import '../models/gate_command.dart';
import '../models/gate_event.dart';
import '../models/book_info.dart';
import 'gate_serial_service.dart';
import 'rfid_service.dart';
import 'enhanced_rfid_service.dart';
import 'shared_scan_pool_service.dart';
import 'master_slave_extension.dart';
import 'master_collection_a_service.dart';
import 'slave_collection_a_service.dart';
import 'book_info_query_service.dart';
import 'exit_book_check_service.dart' as exit_service;
import '../../auth/services/multi_auth_manager.dart';
import '../../auth/models/auth_result.dart';
import '../../auth/services/gate_context_service.dart';

/// 闸机协调器 - 核心业务逻辑控制器
class GateCoordinator extends ChangeNotifier {
  static GateCoordinator? _instance;
  static GateCoordinator get instance => _instance ??= GateCoordinator._();
  GateCoordinator._();
  
  // 当前状态
  GateState _currentState = GateState.idle;
  GateState get currentState => _currentState;
  
  // 服务依赖
  late GateSerialService _serialService;
  late MultiAuthManager _authManager;
  final GateContextService _gateContextService = GateContextService.instance;
  
  // 事件订阅
  StreamSubscription? _commandSubscription;
  StreamSubscription? _authSubscription;
  StreamSubscription? _serialErrorSubscription;
  
  // 🔥 新增：RFID监听器管理
  StreamSubscription? _rfidBarcodeSubscription;
  StreamSubscription? _rfidBookResultSubscription;
  
  // 事件流
  final StreamController<GateEvent> _eventController = 
      StreamController<GateEvent>.broadcast();
  Stream<GateEvent> get eventStream => _eventController.stream;
  
  // RFID扫描相关
  final List<String> _scannedBooks = [];
  Timer? _rfidTimer;
  bool _isRfidScanning = false;

  // 🔥 新增：到达位置信号处理标志
  bool _hasProcessedFirstPosition = false;

  // 🔥 新架构：集合A服务和书籍查询服务
  late final MasterCollectionAService _masterCollectionA;
  late final SlaveCollectionAService _slaveCollectionA;
  late final BookInfoQueryService _bookInfoQueryService;
  StreamSubscription<List<String>>? _collectionASubscription;
  
  // 状态超时控制
  Timer? _stateTimeoutTimer;
  static const int _stateTimeoutSeconds = 30; // 状态超时时间
  
  /// 初始化闸机协调器
  Future<void> initialize() async {
    try {
      debugPrint('开始初始化闸机协调器...');
      
      // 1. 初始化认证管理器（先拿到实例，避免后续错误处理时为空）
      _authManager = MultiAuthManager.instance;

      // 2. 初始化串口服务
      _serialService = GateSerialService.instance;
      await _serialService.initialize();

      // 3. 监听串口命令
      _commandSubscription = _serialService.commandStream.listen(
        _handleSerialCommand,
        onError: (error) {
          debugPrint('串口命令流错误: $error');
          _handleError('串口通信错误: $error');
        },
      );
      
      // 4. 监听串口错误
      _serialErrorSubscription = _serialService.errorStream.listen(
        (error) {
          debugPrint('串口错误: $error');
          _handleSerialError(error);
        },
      );
      
      // 5. 监听认证结果
      _authSubscription = _authManager.authResultStream.listen(
        _handleAuthResult,
        onError: (error) {
          debugPrint('认证结果流错误: $error');
          _handleError('认证系统错误: $error');
        },
      );
      
      // 6. 启动串口监听
      await _serialService.startListening();

      // 🔥 新增：7. 初始化RFID服务和共享池
      await _initializeRFIDAndSharedPool();

      // 8. 设置初始状态
      _setState(GateState.idle);

      debugPrint('闸机协调器初始化完成');
      
      // 发送初始化完成事件
      _eventController.add(GateEvent(
        type: 'initialized',
        data: {'message': '安全闸机系统已就绪'},
      ));
      
    } catch (e) {
      final errorMsg = '闸机协调器初始化失败: $e';
      debugPrint(errorMsg);
      _handleError(errorMsg);
      rethrow;
    }
  }
  
  /// 处理串口命令
  void _handleSerialCommand(GateCommand command) {
    debugPrint('收到闸机命令: ${command.type} (${command.displayName})');
    
    // 取消状态超时
    _cancelStateTimeout();
    
    switch (command.type) {
      case GateCommand.enterStart:
        _handleEnterStart();
        break;
      case GateCommand.enterEnd:
        _handleEnterEnd();
        break;
      case GateCommand.exitStart:
        _handleExitStart();
        break;
      case GateCommand.exitEnd:
        _handleExitEnd();
        break;
      case GateCommand.positionReached:
        _handlePositionReached();  // 🔥 修改：根据当前状态判断是进馆还是出馆到位
        break;
      case GateCommand.heartbeat:
        _handleHeartbeat();  // 🔥 新增：处理心跳信号
        break;
      case GateCommand.tailgating:
        _handleTailgating();
        break;
      case GateCommand.doorBlocked:
        _handleDoorBlocked();
        break;
      default:
        debugPrint('未处理的闸机命令: ${command.type}');
    }
  }
  
  /// 🔥 修改：进馆开始 - 等待到位信号
  void _handleEnterStart() {
    if (_currentState != GateState.idle) {
      debugPrint('警告：非空闲状态收到进馆开始命令，当前状态: $_currentState');
      return;
    }

    debugPrint('🚪 收到进馆开始命令，等待进馆到位信号...');
    _setState(GateState.enterStarted);

    // 🔥 新增：设置流程状态
    _isInEnterFlow = true;
    _isInExitFlow = false;
    _clearPendingAuth(); // 清除之前可能的待处理认证

    // 🔥 新增：重置进馆认证状态和时间记录
    _hasEnterAuthSuccess = false;
    _lastPositionReachedTime = null;
    _authSuccessTime = null;

    // 发送进馆开始事件
    _eventController.add(GateEvent.createEnterStart());

    // 🔥 新流程：不立即启动认证，等待到位信号
    // 设置状态超时
    _setStateTimeout();

    debugPrint('📊 流程状态：进馆流程已开始，等待到位信号');
  }
  
  // 🔥 移除：不再使用的_startAuthentication方法

  /// 🔥 新增：初始化RFID服务和共享池
  Future<void> _initializeRFIDAndSharedPool() async {
    try {
      debugPrint('开始初始化RFID服务和共享池...');

      // 1. 初始化RFID服务（包含持续扫描启动）
      if (!RFIDService.instance.isInitialized) {
        await RFIDService.instance.initialize();
      }

      // 2. 初始化共享扫描池服务
      await SharedScanPoolService.initializeGlobal();

      // 🔥 3. 初始化新架构服务
      await _initializeNewArchitectureServices();

      debugPrint('RFID服务和共享池初始化完成，持续扫描已启动');
    } catch (e) {
      debugPrint('初始化RFID服务和共享池失败: $e');
      // 不抛出异常，允许闸机系统继续运行
    }
  }

  /// 🔥 新增：初始化新架构服务
  Future<void> _initializeNewArchitectureServices() async {
    try {
      debugPrint('🚀 初始化新架构服务...');

      // 初始化集合A服务
      _masterCollectionA = MasterCollectionAService.instance;
      _slaveCollectionA = SlaveCollectionAService.instance;

      // 初始化书籍查询服务
      _bookInfoQueryService = BookInfoQueryService.instance;

      // 根据主从机模式选择合适的集合A服务
      final masterSlaveExtension = MasterSlaveExtension.instance;
      if (masterSlaveExtension.isEnabled && !masterSlaveExtension.isMaster) {
        // 从机模式：使用从机集合A服务
        debugPrint('📱 从机模式：使用从机集合A服务');
        _bookInfoQueryService.startMonitoring(_slaveCollectionA.collectionChangeStream);
      } else {
        // 主机模式：使用主机集合A服务
        debugPrint('🖥️ 主机模式：使用主机集合A服务');
        _bookInfoQueryService.startMonitoring(_masterCollectionA.collectionChangeStream);
      }

      debugPrint('✅ 新架构服务初始化完成');
    } catch (e) {
      debugPrint('❌ 初始化新架构服务失败: $e');
      // 不抛出异常，允许系统继续运行
    }
  }

  /// 🔥 修改：处理认证结果（区分进馆和出馆）
  void _handleAuthResult(AuthResult result) {
    if (_currentState == GateState.enterScanning) {
      // 进馆认证处理
      _handleEnterAuthResult(result);
    } else if (_currentState == GateState.exitScanning) {
      // 🔥 修改：出馆认证处理（不关注结果）
      _handleExitAuthResult(result);
    } else {
      debugPrint('非认证状态收到认证结果，忽略。当前状态: $_currentState');
      return;
    }
  }

  /// 🔥 新增：处理进馆认证结果
  void _handleEnterAuthResult(AuthResult result) {
    debugPrint('收到进馆认证结果: ${result.status}, 用户: ${result.userName}');

    // 🔥 新增：无论认证成功还是失败，立即停止所有认证监听
    debugPrint('进馆认证结果已产生，立即停止所有认证监听');
    _authManager.stopListening();

    if (result.status == AuthStatus.success) {
      final now = DateTime.now();
      _authSuccessTime = now;
      _hasEnterAuthSuccess = true;

      debugPrint('✅ 进馆认证成功: ${result.userName}，时间: ${now.toString()}');

      // 🔥 核心逻辑：检查时间条件
      if (_lastPositionReachedTime != null) {
        final timeDiff = now.difference(_lastPositionReachedTime!);
        debugPrint('📊 时间差分析：认证成功时间 - 最近到位时间 = ${timeDiff.inSeconds}秒');

        if (timeDiff.inSeconds <= 9) {
          debugPrint('✅ 时间差 ≤ 9秒，立即发送成功信号');
          _sendSuccessSignalImmediately();
        } else {
          debugPrint('⏰ 时间差 > 9秒，等待下一次到位信号');
          _waitForNextPositionSignal();
        }
      } else {
        debugPrint('⚠️ 没有到位信号记录，认证在到位前完成，等待到位信号');
        // 认证在到位前完成，等待到位信号处理
      }

      // 通知UI显示成功信息
      _eventController.add(GateEvent.createAuthSuccess(
        userName: result.userName ?? '未知用户',
        userId: result.userId,
      ));

    } else {
      debugPrint('❌ 进馆认证失败: ${result.errorMessage}');

      // 通知UI显示失败信息
      _eventController.add(GateEvent.createAuthFailed(
        message: result.errorMessage ?? '认证失败，请重试',
      ));

      // 重置状态，允许重新认证
      _setState(GateState.enterStarted);  // 回到等待到位状态
    }
  }

  /// 🔥 修改：处理出馆认证结果（不关注结果）
  void _handleExitAuthResult(AuthResult result) {
    debugPrint('📱 收到出馆认证结果: ${result.status}, 用户: ${result.userName}');

    // 🔥 新需求：出馆认证不关注结果，只要API请求发送成功就继续
    debugPrint('✅ 出馆认证API请求已发送，继续数据收集流程（不关注认证结果）');

    // 🔥 标记认证请求已发送成功
    _exitAuthRequestSent = true;

    // 通知UI显示认证已提交（不管成功失败）
    _eventController.add(GateEvent.createExitAuthSuccess(
      userName: result.userName ?? '认证用户',
      userId: result.userId,
    ));

    // 🔥 注意：数据收集已经在_handleExitPositionReached中启动了
    // 这里不需要重复启动
  }
  
  /// 🔥 修改：出馆开始 - 等待到位信号
  void _handleExitStart() {
    if (_currentState != GateState.idle) {
      debugPrint('警告：非空闲状态收到出馆开始命令，当前状态: $_currentState');
      return;
    }

    debugPrint('🚪 收到出馆开始命令，等待出馆到位信号...');
    _setState(GateState.exitStarted);

    // 🔥 新增：设置流程状态
    _isInEnterFlow = false;
    _isInExitFlow = true;
    _clearPendingAuth(); // 清除之前可能的待处理认证

    // 🔥 新增：记录最后收到的命令
    _lastReceivedCommand = 'exit_start';

    // 🔥 新增：重置出馆流程状态
    _exitAuthRequestSent = false;
    _collectedUids.clear();
    _exitDataCollectionTimer?.cancel();

    // 清空之前的扫描结果
    _scannedBooks.clear();

    // 🔥 新增：重置到达位置处理标志
    _hasProcessedFirstPosition = false;

    // 发送出馆开始事件
    _eventController.add(GateEvent.createExitStart());

    // 🔥 新流程：不立即启动认证，等待到位信号
    // 设置状态超时
    _setStateTimeout();

    debugPrint('📊 流程状态：出馆流程已开始，等待到位信号');
  }

  /// 🔥 新架构：开始数据收集和书籍查询
  void _startNewArchitectureDataCollection() async {
    try {
      debugPrint('🚀 新架构：开始出馆数据收集...');
      _setState(GateState.exitScanning);
      _isRfidScanning = true;

      // 根据主从机模式选择合适的同步方式
      final masterSlaveExtension = MasterSlaveExtension.instance;
      if (masterSlaveExtension.isEnabled && !masterSlaveExtension.isMaster) {
        // 从机模式：请求主机清空并同步数据到从机集合A
        debugPrint('📱 从机模式：请求主机清空并同步数据到集合A');
        await _slaveCollectionA.syncOnExitStart();
      } else {
        // 主机模式：清空共享池并同步数据到主机集合A
        debugPrint('🖥️ 主机模式：清空共享池并同步数据到集合A');
        await _masterCollectionA.syncOnExitStartWithClear();
      }

      debugPrint('✅ 新架构数据收集启动完成');
    } catch (e) {
      debugPrint('❌ 新架构数据收集启动失败: $e');
      // 回退到旧的数据收集方式
      _startDataCollection();
    }
  }
  
  /// 🔥 新增：开始数据收集（不启动硬件扫描）
  void _startDataCollection() {
    if (_isRfidScanning) {
      debugPrint('RFID数据收集已在进行中');
      return;
    }

    debugPrint('开始RFID数据收集...');
    _setState(GateState.exitScanning);
    _isRfidScanning = true;

    // 🔥 先取消之前的监听器，避免重复监听
    _cancelRFIDListeners();

    // 🔥 清空共享池和缓冲区，实现数据隔离
    _clearPoolAndBuffer();

    // 🔥 使用增强的RFID服务开始数据收集（不启动硬件扫描）
    RFIDService.instance.startDataCollection().then((_) {
      // 🔥 设置新的监听器并保存引用
      _rfidBarcodeSubscription = RFIDService.instance.barcodeStream.listen(
        (barcode) {
          _onBookScanned(barcode);
        },
        onError: (error) {
          debugPrint('RFID条码流错误: $error');
        },
      );

      _rfidBookResultSubscription = RFIDService.instance.bookResultStream.listen(
        (result) {
          _onBookScanResult(result);
        },
        onError: (error) {
          debugPrint('RFID书籍结果流错误: $error');
        },
      );

      debugPrint('RFID数据收集监听器已正确设置');
    }).catchError((e) {
      debugPrint('启动RFID数据收集失败: $e');

      // 🔥 检查是否为从机模式，从机不应该生成测试数据
      final masterSlaveExtension = MasterSlaveExtension.instance;
      if (masterSlaveExtension.isEnabled && !masterSlaveExtension.isMaster) {
        debugPrint('⚠️ 从机模式：不启用模拟扫描，等待主机同步数据');
        _isRfidScanning = false;
        return;
      }

      // 只有主机才回退到模拟扫描
      debugPrint('主机模式：回退到模拟扫描');
      _simulateRFIDScanning();
    });
  }

  /// 🔥 新增：清空共享池和缓冲区
  void _clearPoolAndBuffer() {
    try {
      SharedScanPoolService.instance.clearPoolAndBuffer();
      debugPrint('共享池和RFID缓冲区已清空');
    } catch (e) {
      debugPrint('清空共享池和缓冲区失败: $e');
    }
  }

  /// 🔥 新增：取消RFID监听器
  void _cancelRFIDListeners() {
    _rfidBarcodeSubscription?.cancel();
    _rfidBookResultSubscription?.cancel();
    _rfidBarcodeSubscription = null;
    _rfidBookResultSubscription = null;
  }
  
  /// 模拟RFID扫描（实际项目中替换为真实插件）
  void _simulateRFIDScanning() {
    _rfidTimer = Timer.periodic(const Duration(milliseconds: 800), (timer) {
      if (!_isRfidScanning) {
        timer.cancel();
        return;
      }
      
      // 模拟随机扫描到书籍
      if (Random().nextBool()) {
        final barcode = 'BOOK${Random().nextInt(1000).toString().padLeft(3, '0')}';
        _onBookScanned(barcode);
      }
    });
  }
  
  /// 处理扫描到的书籍
  void _onBookScanned(String barcode) {
    if (!_isRfidScanning) return;
    
    // 避免重复扫描
    if (_scannedBooks.contains(barcode)) return;
    
    _scannedBooks.add(barcode);
    debugPrint('扫描到书籍: $barcode (总计: ${_scannedBooks.length})');
    
    // 发送书籍扫描事件
    _eventController.add(GateEvent.createBookScanned(
      barcode: barcode,
      totalCount: _scannedBooks.length,
    ));
  }

  /// 🔥 新增：处理书籍扫描结果（包含书籍信息）
  void _onBookScanResult(BookScanResult result) {
    if (!_isRfidScanning) return;

    // 根据状态处理不同的情况，使用简化日志
    switch (result.status) {
      case BookScanStatus.processing:
        debugPrint('查询书籍: ${result.barcode}');
        break;
      case BookScanStatus.success:
        if (result.bookInfo != null) {
          debugPrint('书籍信息: ${result.barcode} - ${result.bookInfo!.bookName}');
        }
        break;
      case BookScanStatus.failed:
        debugPrint('查询失败: ${result.barcode}');
        break;
      case BookScanStatus.notFound:
        debugPrint('未找到书籍: ${result.barcode} (忽略)');
        break;
    }

    // 发送增强的书籍扫描事件
    _eventController.add(GateEvent.createEnhancedBookScanned(
      barcode: result.barcode,
      bookInfo: result.bookInfo,
      totalCount: _scannedBooks.length,
      status: result.status.toString(),
    ));
  }

  /// 🔥 重写：到达指定位置 - 根据当前状态判断进馆/出馆到位
  void _handlePositionReached() {
    debugPrint('📍 收到到位信号，当前状态: $_currentState');
    debugPrint('📊 流程状态：进馆=$_isInEnterFlow, 出馆=$_isInExitFlow');
    debugPrint('📊 待处理认证：进馆=$_hasPendingEnterAuth, 出馆=$_hasPendingExitAuth');

    // 🔥 新逻辑：优先检查是否有待处理的认证状态
    if (_hasPendingEnterAuth && _isInEnterFlow) {
      debugPrint('🎯 检测到待处理的进馆认证，立即发送成功信号');
      _processPendingEnterAuth();
      return;
    }

    if (_hasPendingExitAuth && _isInExitFlow) {
      debugPrint('🎯 检测到待处理的出馆检查，立即发送成功信号');
      _processPendingExitAuth();
      return;
    }

    // 🔥 原有逻辑：根据当前状态处理到位信号
    if (_currentState == GateState.enterStarted) {
      // 进馆到位信号
      _handleEnterPositionReached();
    } else if (_currentState == GateState.exitStarted) {
      // 出馆到位信号
      _handleExitPositionReached();
    } else if (_currentState == GateState.exitScanning) {
      // 🔥 新逻辑：出馆扫描中收到到位信号，立即停止数据收集并开始书籍检查
      debugPrint('出馆扫描中收到到位信号，立即停止数据收集');

      // 🔥 更新最后收到的命令
      _lastReceivedCommand = 'position_reached';

      // 立即停止5秒数据收集计时器
      _stopExitDataCollection();
    } else {
      debugPrint('⚠️ 非预期状态收到位置到达命令，当前状态: $_currentState');
    }
  }
  
  /// 🔥 修改：停止数据收集（不停止硬件扫描）
  void _stopDataCollection() {
    if (!_isRfidScanning) return;

    _isRfidScanning = false;
    _rfidTimer?.cancel();
    _rfidTimer = null;

    // 🔥 取消RFID监听器，避免内存泄漏和重复处理
    _cancelRFIDListeners();

    debugPrint('停止RFID数据收集，共收集到${_scannedBooks.length}本书');

    // 🔥 停止RFID数据收集服务（不停止硬件扫描）
    RFIDService.instance.stopDataCollection().then((result) {
      debugPrint('RFID数据收集已停止，最终结果: ${result.length}个条码');
    }).catchError((e) {
      debugPrint('停止RFID数据收集失败: $e');
    });
  }
  
  /// 🔥 修改：检查书籍状态并决定是否开门（实时获取，不使用缓存）
  Future<void> _checkBooksAndDecide() async {
    debugPrint('开始检查书籍状态，共${_scannedBooks.length}本书');

    if (_scannedBooks.isEmpty) {
      debugPrint('未扫描到书籍，允许通过');
      _allowExit('未检测到书籍，请通过');
      return;
    }

    try {
      // 🔥 实时获取所有书籍信息，不使用缓存
      List<BookInfo> validBooks = [];
      List<String> ignoredBarcodes = [];

      for (String barcode in _scannedBooks) {
        // 使用SIP2实时获取书籍信息
        final bookInfo = await RFIDService.instance.getSingleBookInfoRealTime(barcode);

        if (bookInfo != null) {
          // 找到书籍信息，加入检查列表
          validBooks.add(bookInfo);
          debugPrint('有效书籍: $barcode - ${bookInfo.bookName}');
        } else {
          // 没有找到书籍信息，忽略（可能是其他馆的条码）
          ignoredBarcodes.add(barcode);
          debugPrint('忽略条码: $barcode (其他馆)');
        }
      }

      debugPrint('检查完成: ${validBooks.length}本有效书籍，${ignoredBarcodes.length}个忽略条码');

      // 通知UI显示书籍列表
      _eventController.add(GateEvent.createShowBooks(
        books: validBooks.map((book) => book.toJson()).toList(),
      ));

      // 🔥 新的判断逻辑：只对有效书籍进行检查
      if (validBooks.isEmpty) {
        // 所有条码都被忽略，允许通过
        debugPrint('所有条码都是其他馆的，允许通过');
        _allowExit('未检测到本馆书籍，请通过');
      } else {
        // 检查有效书籍的借阅状态
        final result = BookCheckResult(books: validBooks);

        if (result.allowPass) {
          _allowExit(result.summary, validBooks);
        } else {
          _blockExit(result.summary, result.books, result.unborrowedBooks);
        }
      }

    } catch (e) {
      debugPrint('检查书籍状态失败: $e');
      // 出错时默认允许通过，避免阻挡用户
      _allowExit('书籍检查服务异常，默认允许通过');
    }
  }

  
  /// 异步发送闸机命令（不等待结果，避免阻塞流程）
  void _sendGateCommandAsync(String commandType) {
    debugPrint('📤 准备发送闸机命令: $commandType');

    Future.microtask(() async {
      try {
        final result = await _serialService.sendCommand(commandType);
        if (result) {
          debugPrint('✅ 闸机命令 $commandType 发送成功');
        } else {
          debugPrint('❌ 闸机命令 $commandType 发送失败（硬件可能未连接）');
        }
      } catch (e) {
        debugPrint('❌ 闸机命令 $commandType 发送异常: $e');
      }
    });
  }

  /// 🔥 修改：允许出馆（检查9秒约束）
  void _allowExit(String message, [List<BookInfo>? books]) {
    debugPrint('✅ 书籍检查通过: $message');

    // 🔥 增强日志：检查9秒约束状态
    final hasEightSecondTimer = _eightSecondTimer != null;
    debugPrint('📊 9秒计时器状态：${hasEightSecondTimer ? "运行中" : "已超时"}');

    if (hasEightSecondTimer) {
      debugPrint('✅ 在9秒约束内检查完成，准备发送成功信号');

      // 取消9秒计时器
      _cancel8SecondTimer();

      // 🔥 增强日志：发送成功信号
      debugPrint('📤 正在发送成功信号到闸机：AA 00 01 01 00 00 48 36');
      _sendGateCommandAsync('success_signal');
      debugPrint('📤 成功信号已发送（异步）');

      _setState(GateState.exitOver);

      // 发送允许出馆事件
      _eventController.add(GateEvent.createExitAllowed(
        message: message,
        books: books?.map((book) => book.toJson()).toList(),
      ));

      // 设置自动完成定时器
      Timer(const Duration(seconds: 3), () {
        if (_currentState == GateState.exitOver) {
          debugPrint('出馆完成，状态重置为idle');
          _setState(GateState.idle);
          _isInExitFlow = false; // 清除流程状态
        }
      });
    } else {
      debugPrint('⏰ 9秒约束已超时，检查通过但不发送信号');
      debugPrint('💾 保存检查通过状态，等待下一次出馆到位信号');

      // 🔥 新逻辑：保存检查通过状态，等待下次到位信号
      _savePendingExitAuth(message, books);
      _setState(GateState.exitStarted);  // 回到等待到位状态

      // 发送超时事件
      _eventController.add(GateEvent(
        type: 'exit_timeout',
        data: {'message': '检查完成但超时，等待下次到位信号'},
      ));
    }
  }
  
  /// 阻止出馆
  void _blockExit(String message, List<BookInfo> books, List<BookInfo> unborrowedBooks) {
    debugPrint('阻止出馆: $message');
    
    // 发送阻止出馆事件
    _eventController.add(GateEvent.createExitBlocked(
      message: message,
      books: books.map((book) => book.toJson()).toList(),
      unborrowedBooks: unborrowedBooks.map((book) => book.toJson()).toList(),
    ));
  }
  
  /// 进馆结束
  void _handleEnterEnd() {
    debugPrint('进馆流程结束');
    _setState(GateState.idle);

    // 🔥 新增：清除流程状态和待处理认证
    _isInEnterFlow = false;
    _isInExitFlow = false;
    _clearPendingAuth();
    _cancel8SecondTimer();

    _eventController.add(GateEvent(
      type: GateEvent.enterEnd,
      data: {'message': '进馆完成'},
    ));

    _cancelStateTimeout();
    debugPrint('📊 流程状态已清除：进馆=$_isInEnterFlow, 出馆=$_isInExitFlow');
  }
  
  /// 🔥 增强：出馆结束
  void _handleExitEnd() {
    debugPrint('出馆流程结束');

    // 🔥 修改：确保停止数据收集（不停止硬件扫描）
    _stopDataCollection();

    // 🔥 新增：停止出馆数据收集
    _exitDataCollectionTimer?.cancel();
    _exitDataCollectionTimer = null;

    // 🔥 新增：出馆结束时的全面清空（不清空共享池）
    _handleExitEndCleanup();

    _setState(GateState.idle);

    // 🔥 新增：清除流程状态和待处理认证
    _isInEnterFlow = false;
    _isInExitFlow = false;
    _clearPendingAuth();
    _cancel8SecondTimer();

    // 🔥 新增：重置出馆流程状态
    _resetExitFlowState();

    _eventController.add(GateEvent(
      type: GateEvent.exitEnd,
      data: {'message': '出馆完成'},
    ));

    _cancelStateTimeout();
    debugPrint('📊 流程状态已清除：进馆=$_isInEnterFlow, 出馆=$_isInExitFlow');
  }
  
  /// 处理尾随检测
  void _handleTailgating() {
    debugPrint('检测到尾随');
    _eventController.add(GateEvent(
      type: GateEvent.tailgating,
      data: {'message': '检测到尾随，请单人通过'},
    ));
  }
  
  /// 处理通道阻挡
  void _handleDoorBlocked() {
    debugPrint('检测到开门有人');
    _eventController.add(GateEvent(
      type: GateEvent.doorBlocked,
      data: {'message': '通道被阻挡，请清理通道'},
    ));
  }

  /// 处理串口错误（不影响系统状态）
  void _handleSerialError(String errorMessage) {
    debugPrint('串口错误（不影响系统功能）: $errorMessage');

    // 检查是否为硬件发送失败相关的错误
    if (errorMessage.contains('SEND_INCOMPLETE') ||
        errorMessage.contains('发送数据失败') ||
        errorMessage.contains('发送闸机命令失败') ||
        errorMessage.contains('硬件可能未连接')) {
      // 这些错误在没有真实硬件时是正常的，不影响系统功能
      debugPrint('检测到硬件相关错误，在模拟环境中忽略: $errorMessage');
      return;
    }

    // 对于其他类型的串口错误，记录但不设置系统为错误状态
    debugPrint('其他串口错误，记录但不影响系统状态: $errorMessage');
  }

  /// 处理系统错误
  void _handleError(String errorMessage) {
    debugPrint('闸机系统错误: $errorMessage');
    
    _setState(GateState.error);
    
    _eventController.add(GateEvent(
      type: GateEvent.error,
      data: {'message': errorMessage},
    ));

    // 🔥 修改：错误状态下停止数据收集
    _stopDataCollection();
    _authManager.stopListening();
    _cancelStateTimeout();
    
    // 5秒后自动恢复到空闲状态
    Timer(const Duration(seconds: 5), () {
      if (_currentState == GateState.error) {
        _setState(GateState.idle);
      }
    });
  }
  
  /// 设置状态超时
  void _setStateTimeout() {
    _cancelStateTimeout();
    _stateTimeoutTimer = Timer(const Duration(seconds: _stateTimeoutSeconds), () {
      debugPrint('状态超时，自动重置到空闲状态');
      _handleTimeout();
    });
  }
  
  /// 取消状态超时
  void _cancelStateTimeout() {
    _stateTimeoutTimer?.cancel();
    _stateTimeoutTimer = null;
  }
  
  /// 处理状态超时
  void _handleTimeout() {
    debugPrint('状态超时，当前状态: $_currentState');

    // 🔥 修改：停止数据收集
    _stopDataCollection();
    _authManager.stopListening();

    // 🔥 新增：清除流程状态和待处理认证
    _isInEnterFlow = false;
    _isInExitFlow = false;
    _clearPendingAuth();
    _cancel8SecondTimer();

    // 重置到空闲状态
    _setState(GateState.idle);

    // 发送超时事件
    _eventController.add(GateEvent(
      type: 'timeout',
      data: {'message': '操作超时，系统已重置'},
    ));

    debugPrint('📊 超时处理完成，流程状态已清除');
  }
  
  /// 设置状态
  void _setState(GateState newState) {
    if (_currentState != newState) {
      final oldState = _currentState;
      _currentState = newState;
      
      debugPrint('闸机状态变更: $oldState -> $newState');

      // 🔥 新增：同步状态到GateContextService
      _gateContextService.updateGateState(newState);

      // 发送状态变更事件
      _eventController.add(GateEvent(
        type: GateEvent.stateChanged,
        data: {
          'old_state': oldState.name,
          'new_state': newState.name,
          'message': newState.displayName,
        },
      ));
      
      notifyListeners();
    }
  }
  
  /// 获取系统状态信息
  Map<String, dynamic> getSystemStatus() {
    return {
      'current_state': _currentState.name,
      'state_display_name': _currentState.displayName,
      'is_rfid_scanning': _isRfidScanning,
      'scanned_books_count': _scannedBooks.length,
      'serial_status': _serialService.getStatus(),
      'timestamp': DateTime.now().toIso8601String(),
    };
  }
  
  /// 手动重置系统
  void resetSystem() {
    debugPrint('手动重置闸机系统');

    // 🔥 修改：重置时停止数据收集
    _stopDataCollection();
    _authManager.stopListening();
    _cancelStateTimeout();
    _scannedBooks.clear();

    _setState(GateState.idle);

    _eventController.add(GateEvent(
      type: 'reset',
      data: {'message': '系统已手动重置'},
    ));
  }

  /// 模拟串口命令（用于调试）
  void simulateSerialCommand(String commandType) {
    final commandData = GateCommand.receiveCommandMap[commandType];
    if (commandData != null) {
      final command = GateCommand(type: commandType, data: commandData);
      _handleSerialCommand(command);
    } else {
      debugPrint('未知的命令类型: $commandType');
    }
  }

  /// 🔥 新增：公共发送闸机命令方法（用于测试）
  void sendGateCommand(String commandType) {
    _sendGateCommandAsync(commandType);
  }
  
  /// 🔥 修改：处理进馆到位信号 - 记录时间并处理逻辑
  void _handleEnterPositionReached() {
    final now = DateTime.now();
    _lastPositionReachedTime = now;

    debugPrint('🚪 进馆到位信号，时间: ${now.toString()}');
    _setState(GateState.enterWaitingAuth);

    // 启动9秒计时器
    _start8SecondTimer();

    // 🔥 检查是否已经有认证成功（认证在到位前完成的情况）
    if (_hasEnterAuthSuccess && _authSuccessTime != null) {
      debugPrint('🎯 检测到认证已成功，立即处理条件检查...');
      _checkEnterConditionsAndSendSignal();
    } else {
      // 🔐 启动认证系统
      _startEnterAuthentication();
    }
  }

  /// 🔥 修改：处理出馆到位信号 - 新的三步检查流程
  void _handleExitPositionReached() {
    debugPrint('🚪 出馆到位信号，启动认证和10秒数据收集...');
    _setState(GateState.exitWaitingAuth);

    // 🔥 新增：记录最后收到的命令
    _lastReceivedCommand = 'position_reached';

    // 🔥 修改：出馆无9秒时间约束
    // 不启动9秒计时器

    // 🔥 启动出馆认证（必须发送请求，不关注结果）
    _startExitAuthentication();

    // 🔥 修改：启动5秒数据收集
    _startExitDataCollection();
  }

  /// 🔥 新增：启动9秒计时器
  Timer? _eightSecondTimer;

  // 🔥 新增：认证成功但超时的状态保存
  bool _hasPendingEnterAuth = false;  // 进馆认证成功但超时
  bool _hasPendingExitAuth = false;   // 出馆检查通过但超时
  String? _pendingUserName;           // 保存的用户名
  String? _pendingUserId;             // 保存的用户ID
  List<BookInfo>? _pendingBooks;      // 保存的书籍信息（出馆用）
  String? _pendingMessage;            // 保存的消息
  Timer? _pendingAuthTimer;           // 15秒清除计时器

  // 🔥 新增：流程状态跟踪
  bool _isInEnterFlow = false;        // 是否在进馆流程中
  bool _isInExitFlow = false;         // 是否在出馆流程中

  // 🔥 新增：进馆认证状态跟踪
  bool _hasEnterAuthSuccess = false;  // 进馆认证是否成功
  DateTime? _lastPositionReachedTime; // 最近一次到位信号时间
  DateTime? _authSuccessTime;         // 认证成功时间

  // 🔥 新增：出馆流程状态跟踪
  String? _lastReceivedCommand;       // 最后收到的闸机命令
  bool _exitAuthRequestSent = false;  // 出馆认证请求是否已发送
  Timer? _exitDataCollectionTimer;    // 出馆数据收集5秒计时器
  final List<String> _collectedUids = [];   // 收集到的UID列表
  final exit_service.ExitBookCheckService _exitBookCheckService = exit_service.ExitBookCheckService.instance;
  void _start8SecondTimer() {
    _cancel8SecondTimer();
    debugPrint('⏰ 启动9秒约束计时器（9秒内必须完成认证/检查）');

    _eightSecondTimer = Timer(const Duration(seconds: 9), () {
      debugPrint('⏰ 9秒约束超时！认证/检查必须在9秒内完成');
      debugPrint('📊 当前流程状态：进馆=$_isInEnterFlow, 出馆=$_isInExitFlow');
      _handle8SecondTimeout();
    });
  }

  /// 🔥 新增：取消9秒计时器
  void _cancel8SecondTimer() {
    if (_eightSecondTimer != null) {
      debugPrint('⏰ 取消9秒约束计时器');
      _eightSecondTimer?.cancel();
      _eightSecondTimer = null;
    }
  }

  /// 🔥 新增：9秒超时处理
  void _handle8SecondTimeout() {
    debugPrint('⏰ 9秒约束超时，重置状态等待下一次到位信号');

    if (_currentState == GateState.enterWaitingAuth) {
      _setState(GateState.enterStarted);  // 回到等待进馆到位状态
    } else if (_currentState == GateState.exitWaitingAuth || _currentState == GateState.exitScanning) {
      _setState(GateState.exitStarted);   // 回到等待出馆到位状态
      _stopDataCollection();  // 停止RFID扫描
    }

    // 停止认证
    _authManager.stopListening();
  }

  /// 🔥 新增：启动进馆认证
  void _startEnterAuthentication() {
    try {
      debugPrint('🔐 启动进馆认证系统...');
      _setState(GateState.enterScanning);
      _authManager.startListening();
      debugPrint('✅ 进馆认证系统已启动');
    } catch (e) {
      debugPrint('❌ 启动进馆认证系统失败: $e');
      _handleError('启动认证系统失败: $e');
    }
  }

  /// 🔥 新增：启动出馆认证（不关注结果）
  void _startExitAuthentication() {
    try {
      debugPrint('🔐 启动出馆认证系统（不关注结果）...');
      _setState(GateState.exitScanning);
      _authManager.startListening();
      debugPrint('✅ 出馆认证系统已启动');
    } catch (e) {
      debugPrint('❌ 启动出馆认证系统失败: $e');
      // 出馆认证失败不影响RFID扫描，继续流程
    }
  }

  /// 🔥 新增：处理心跳信号
  void _handleHeartbeat() {
    debugPrint('💓 收到心跳信号');
    // 心跳信号不需要特殊处理，只是确认通信正常
  }

  /// 🔥 新增：清除待处理的认证状态
  void _clearPendingAuth() {
    if (_hasPendingEnterAuth || _hasPendingExitAuth) {
      debugPrint('🧹 清除待处理认证状态：进馆=$_hasPendingEnterAuth, 出馆=$_hasPendingExitAuth');
    }

    _hasPendingEnterAuth = false;
    _hasPendingExitAuth = false;
    _pendingUserName = null;
    _pendingUserId = null;
    _pendingBooks = null;
    _pendingMessage = null;
    _pendingAuthTimer?.cancel();
    _pendingAuthTimer = null;
  }

  /// 🔥 新增：保存进馆认证成功状态
  void _savePendingEnterAuth(String? userName, String? userId) {
    debugPrint('💾 保存进馆认证成功状态：用户=$userName, ID=$userId');

    _hasPendingEnterAuth = true;
    _hasPendingExitAuth = false;
    _pendingUserName = userName;
    _pendingUserId = userId;
    _pendingBooks = null;
    _pendingMessage = null;

    // 启动15秒清除计时器
    _startPendingAuthTimer();
  }

  /// 🔥 新增：保存出馆检查通过状态
  void _savePendingExitAuth(String message, List<BookInfo>? books) {
    debugPrint('💾 保存出馆检查通过状态：消息=$message, 书籍数量=${books?.length ?? 0}');

    _hasPendingEnterAuth = false;
    _hasPendingExitAuth = true;
    _pendingUserName = null;
    _pendingUserId = null;
    _pendingBooks = books;
    _pendingMessage = message;

    // 启动15秒清除计时器
    _startPendingAuthTimer();
  }

  /// 🔥 新增：启动15秒待处理认证清除计时器
  void _startPendingAuthTimer() {
    _pendingAuthTimer?.cancel();
    debugPrint('⏰ 启动15秒待处理认证清除计时器');

    _pendingAuthTimer = Timer(const Duration(seconds: 15), () {
      debugPrint('⏰ 15秒超时，清除待处理认证状态');
      _clearPendingAuth();
    });
  }

  /// 🔥 新增：检查进馆条件并发送成功信号
  void _checkEnterConditionsAndSendSignal() {
    debugPrint('🔍 检查进馆条件：认证成功=$_hasEnterAuthSuccess');

    // 检查条件：认证成功 + 到位后1秒延迟完成
    if (_hasEnterAuthSuccess) {
      debugPrint('✅ 进馆条件满足：认证成功 + 到位后1秒延迟完成');

      // 延迟1秒后发送成功信号
      Timer(const Duration(seconds: 1), () {
        debugPrint('📤 延迟1秒后发送成功信号到闸机：AA 00 01 01 00 00 48 36');
        _sendGateCommandAsync('success_signal');
        debugPrint('📤 成功信号已发送（异步）');

        _setState(GateState.enterOpening);

        // 设置自动完成定时器（模拟用户通过闸机）
        Timer(const Duration(seconds: 3), () {
          if (_currentState == GateState.enterOpening) {
            debugPrint('模拟进馆完成，状态重置为idle');
            _setState(GateState.idle);
            _isInEnterFlow = false; // 清除流程状态
            _hasEnterAuthSuccess = false; // 重置认证状态
            _lastPositionReachedTime = null; // 重置时间记录
            _authSuccessTime = null;
          }
        });
      });
    } else {
      debugPrint('⚠️ 进馆条件不满足：认证尚未成功，等待认证结果...');
      // 继续等待认证结果
    }
  }

  /// 🔥 修复：启动出馆5秒数据收集
  void _startExitDataCollection() {
    debugPrint('🚀 启动出馆5秒数据收集...');
    _setState(GateState.exitScanning);

    // 清空之前的数据
    _collectedUids.clear();

    // 🔥 修复：启动5秒计时器
    _exitDataCollectionTimer?.cancel();
    debugPrint('🔧 启动5秒计时器，当前时间: ${DateTime.now()}');
    _exitDataCollectionTimer = Timer(const Duration(seconds: 5), () {
      debugPrint('⏰ 5秒数据收集完成，开始书籍检查... 时间: ${DateTime.now()}');
      _stopExitDataCollection();
    });
    debugPrint('🔧 5秒计时器已设置，计时器对象: $_exitDataCollectionTimer');

    // 开始从共享池收集数据
    _startCollectingFromSharedPool();
  }

  /// 🔥 新增：停止出馆数据收集
  void _stopExitDataCollection() {
    debugPrint('🛑 停止出馆数据收集，收集到UID数量: ${_collectedUids.length}');
    debugPrint('🔧 停止时间: ${DateTime.now()}');

    _exitDataCollectionTimer?.cancel();
    _exitDataCollectionTimer = null;

    // 停止从共享池收集数据
    _stopCollectingFromSharedPool();

    // 开始三步书籍检查
    _startExitBookCheck();
  }

  /// 🔥 修复：开始从共享池收集数据（区分主机和从机）
  void _startCollectingFromSharedPool() {
    debugPrint('📡 开始从共享池收集数据...');

    // 🔥 修复：检查主从机模式
    final masterSlaveExtension = MasterSlaveExtension.instance;
    if (masterSlaveExtension.isEnabled && !masterSlaveExtension.isMaster) {
      // 从机模式：请求主机清空共享池
      debugPrint('🔧 从机模式：请求主机清空共享池准备收集新数据');
      _requestMasterClearPoolForExitPosition();
    } else {
      // 主机模式：直接清空共享池并通知页面更新
      debugPrint('🔧 主机模式：直接清空共享池准备收集新数据');
      SharedScanPoolService.instance.clearPoolAndBuffer();

      // 🔥 新增：通知页面清空显示
      _notifyPageClearForMaster();
    }
  }

  /// 🔥 新增：从机在出馆到位时请求主机清空共享池并启动5秒数据收集
  Future<void> _requestMasterClearPoolForExitPosition() async {
    try {
      final masterSlaveExtension = MasterSlaveExtension.instance;

      // 🔥 关键：清空从机收集数据，避免去重问题
      masterSlaveExtension.clearCollectedData();
      debugPrint('🧹 已清空从机收集数据，避免去重问题');

      // 请求主机清空共享池
      final success = await masterSlaveExtension.requestMasterClearPool();
      if (success) {
        debugPrint('✅ 从机请求主机清空共享池成功');
      } else {
        debugPrint('❌ 从机请求主机清空共享池失败');
      }

      // 🔥 新增：启动5秒数据收集
      debugPrint('🔄 从机开始5秒数据收集...');
      await masterSlaveExtension.startDataCollectionFor5Seconds();

    } catch (e) {
      debugPrint('❌ 从机请求主机清空共享池异常: $e');
    }
  }

  /// 🔥 修复：停止从共享池收集数据（区分主机和从机）
  void _stopCollectingFromSharedPool() {
    debugPrint('📡 停止从共享池收集数据...');

    // 🔥 修复：检查主从机模式
    final masterSlaveExtension = MasterSlaveExtension.instance;
    if (masterSlaveExtension.isEnabled && !masterSlaveExtension.isMaster) {
      // 从机模式：使用从主机获取的数据
      debugPrint('🔧 从机模式：使用从主机获取的数据');
      _collectUidsFromMasterSlaveData();
    } else {
      // 主机模式：从RFID服务获取数据
      debugPrint('🔧 主机模式：从RFID服务获取数据');
      _collectUidsFromRFIDService();
    }

    debugPrint('📊 数据收集完成，UID列表: $_collectedUids');
  }

  /// 🔥 新增：从主从机数据收集UID数据
  void _collectUidsFromMasterSlaveData() {
    try {
      debugPrint('📡 开始从主从机数据收集UID数据...');

      final masterSlaveExtension = MasterSlaveExtension.instance;
      final collectedBarcodes = masterSlaveExtension.getCollectedBarcodes();

      debugPrint('📊 从主从机获取到条码数量: ${collectedBarcodes.length}');
      debugPrint('📋 条码详情: $collectedBarcodes');

      // 🔥 关键：从机使用从主机获取的条码作为UID
      // 在从机模式下，条码就是我们需要的UID数据
      for (final barcode in collectedBarcodes) {
        if (barcode.isNotEmpty && !_collectedUids.contains(barcode)) {
          _collectedUids.add(barcode);
          debugPrint('📋 收集到UID: $barcode');
        }
      }

      debugPrint('📊 从主从机数据收集到UID数量: ${_collectedUids.length}');

    } catch (e) {
      debugPrint('❌ 从主从机数据收集UID失败: $e');
    }
  }

  /// 🔥 修改：从RFID服务收集UID数据
  void _collectUidsFromRFIDService() {
    try {
      debugPrint('📡 开始从RFID服务收集UID数据...');

      // 🔥 方法1：从HWTagProvider获取当前标签的UID（使用Provider模式）
      try {
        final context = Get.context;
        if (context != null) {
          final tagProvider = Provider.of<HWTagProvider>(context, listen: false);
          if (tagProvider.tagList.isNotEmpty) {
            debugPrint('📊 从HWTagProvider获取UID，标签数量: ${tagProvider.tagList.length}');

            for (final tag in tagProvider.tagList) {
              if (tag.uid?.isNotEmpty == true) {
                final uid = tag.uid!;
                if (!_collectedUids.contains(uid)) {
                  _collectedUids.add(uid);
                  debugPrint('📡 收集到UID: $uid (条码: ${tag.barCode})');
                }
              }
            }
          } else {
            debugPrint('📊 HWTagProvider.tagList为空，无UID数据');
          }
        } else {
          debugPrint('⚠️ Get.context为null，无法获取HWTagProvider');
        }
      } catch (e) {
        debugPrint('⚠️ 无法获取HWTagProvider: $e');
      }

      // 🔥 方法2：如果没有从HWTagProvider获取到UID，使用模拟数据
      if (_collectedUids.isEmpty && _scannedBooks.isNotEmpty) {
        debugPrint('⚠️ 未从HWTagProvider获取到UID，使用模拟数据');
        for (int i = 0; i < _scannedBooks.length; i++) {
          String mockUid = 'UID${DateTime.now().millisecondsSinceEpoch + i}';
          if (!_collectedUids.contains(mockUid)) {
            _collectedUids.add(mockUid);
            debugPrint('📡 生成模拟UID: $mockUid (对应条码: ${_scannedBooks[i]})');
          }
        }
      }

      debugPrint('📡 从RFID服务收集到UID数量: ${_collectedUids.length}');
      debugPrint('📋 UID列表: ${_collectedUids.take(5).join(', ')}${_collectedUids.length > 5 ? '...' : ''}');
    } catch (e) {
      debugPrint('❌ 从RFID服务收集UID失败: $e');
    }
  }

  /// 🔥 新增：开始三步书籍检查
  void _startExitBookCheck() async {
    debugPrint('🔍 开始三步书籍检查，UID数量: ${_collectedUids.length}');
    _setState(GateState.exitChecking);

    if (_collectedUids.isEmpty) {
      debugPrint('未收集到UID，允许通过');
      _allowExitWithNewLogic('未检测到书籍，请通过', []);
      return;
    }

    try {
      // 执行完整的三步检查流程
      final result = await _exitBookCheckService.performFullCheck(_collectedUids);

      debugPrint('✅ 三步检查完成: ${result.summary}');
      debugPrint('📊 检查结果: 允许通过=${result.allowPass}, 不在白名单书籍数量=${result.notInWhitelistBooks.length}');

      if (result.allowPass) {
        _allowExitWithNewLogic(result.summary, result.notInWhitelistBooks);
      } else {
        _blockExitWithNewLogic(result.summary, result.notInWhitelistBooks);
      }
    } catch (e) {
      debugPrint('❌ 三步书籍检查失败: $e');
      // 出错时默认允许通过
      _allowExitWithNewLogic('书籍检查服务异常，默认允许通过', []);
    }
  }

  /// 🔥 新增：允许出馆（新逻辑）
  void _allowExitWithNewLogic(String message, List<exit_service.BookInfo> notInWhitelistBooks) {
    debugPrint('✅ 允许出馆: $message');

    // 🔥 关键：检查命令顺序，无时间约束
    if (_lastReceivedCommand == 'position_reached' && _isInExitFlow) {
      debugPrint('✅ 命令顺序正确：出馆流程中且最后命令是到位信号');
      debugPrint('📤 正在发送成功信号到闸机：AA 00 01 01 00 00 48 36');
      _sendGateCommandAsync('success_signal');
      debugPrint('📤 成功信号已发送（异步）');

      _setState(GateState.exitOver);

      // 🔥 发送允许出馆事件，包含不在白名单的书籍信息
      _eventController.add(GateEvent.createExitAllowed(
        message: message,
        books: notInWhitelistBooks.map((book) => {
          'barcode': book.barcode,
          'bookName': book.bookName,
          'author': book.author ?? '未知作者',
          'publisher': book.publisher ?? '未知出版社',
        }).toList(),
      ));

      // 设置自动完成定时器
      Timer(const Duration(seconds: 3), () {
        if (_currentState == GateState.exitOver) {
          debugPrint('模拟出馆完成，状态重置为idle');
          _setState(GateState.idle);
          _isInExitFlow = false;
          _resetExitFlowState();
        }
      });
    } else {
      debugPrint('⚠️ 命令顺序不正确，不发送成功信号');
      debugPrint('📊 当前状态: 最后命令=$_lastReceivedCommand, 出馆流程=$_isInExitFlow');

      // 发送错误事件
      _eventController.add(GateEvent.createExitBlocked(
        message: '命令顺序错误，请重新操作',
        books: [],
        unborrowedBooks: [],
      ));
    }
  }

  /// 🔥 新增：阻止出馆（新逻辑）
  void _blockExitWithNewLogic(String message, List<exit_service.BookInfo> notInWhitelistBooks) {
    debugPrint('❌ 阻止出馆: $message');

    // 🔥 关键：检查命令顺序，发送失败信号
    if (_lastReceivedCommand == 'position_reached' && _isInExitFlow) {
      debugPrint('❌ 命令顺序正确：出馆流程中且最后命令是到位信号');
      debugPrint('📤 正在发送失败信号到闸机：AA 00 02 01 00 00 48 72');
      _sendGateCommandAsync('fail_signal');
      debugPrint('📤 失败信号已发送（异步）');

      _setState(GateState.exitStarted); // 保持在出馆开始状态，等待重新操作
    } else {
      debugPrint('❌ 命令顺序不正确或不在出馆流程中，不发送闸机信号');
      debugPrint('❌ 最后命令: $_lastReceivedCommand, 出馆流程: $_isInExitFlow');
    }

    // 🔥 发送阻止出馆事件，包含不在白名单的书籍信息
    _eventController.add(GateEvent.createExitBlocked(
      message: message,
      books: notInWhitelistBooks.map((book) => {
        // 🔥 只传递UI模型需要的字段，避免字段不匹配
        'barcode': book.barcode,
        'book_name': book.bookName,
        'author': book.author ?? '未知作者',
        'isbn': book.isbn ?? '',
        'is_borrowed': book.borrowFlag,
        'borrow_date': null, // 暂时为空，可以根据需要填充
        'return_date': null, // 暂时为空，可以根据需要填充
        'borrower_name': null, // 暂时为空，可以根据需要填充
        'borrower_id': null, // 暂时为空，可以根据需要填充
      }).toList(),
      unborrowedBooks: [], // 暂时为空，可以根据需要填充
    ));
  }

  /// 🔥 新增：重置出馆流程状态
  void _resetExitFlowState() {
    _lastReceivedCommand = null;
    _exitAuthRequestSent = false;
    _collectedUids.clear();
    _exitDataCollectionTimer?.cancel();
    _exitDataCollectionTimer = null;
  }

  /// 🔥 新增：通知页面清空显示（主机模式）
  void _notifyPageClearForMaster() {
    try {
      // 发送页面清空事件，通知ViewModel更新显示为0
      _eventController.add(GateEvent.createPageClear());
      debugPrint('📱 已通知页面清空显示（主机模式）');
    } catch (e) {
      debugPrint('❌ 通知页面清空失败: $e');
    }
  }

  /// 🔥 新增：出馆结束时的全面清空（不清空共享池）
  void _handleExitEndCleanup() {
    try {
      debugPrint('🧹 开始出馆结束清空操作...');

      final masterSlaveExtension = MasterSlaveExtension.instance;

      if (masterSlaveExtension.isEnabled && !masterSlaveExtension.isMaster) {
        // 从机模式清空
        debugPrint('📱 从机模式：清空本地集合和关键信息');
        _handleSlaveExitEndCleanup(masterSlaveExtension);
      } else {
        // 主机模式清空
        debugPrint('🖥️ 主机模式：清空本地集合和关键信息');
        _handleMasterExitEndCleanup();
      }

      debugPrint('✅ 出馆结束清空操作完成');
    } catch (e) {
      debugPrint('❌ 出馆结束清空操作失败: $e');
    }
  }

  /// 🔥 新增：主机模式出馆结束清空
  void _handleMasterExitEndCleanup() {
    try {
      // 1. 清空RFID去重信息
      debugPrint('🧹 清空RFID去重信息...');
      try {
        RFIDService.instance.clearScanResult();
        RFIDService.instance.resetProcessedBarcodes();
        debugPrint('✅ RFID去重信息已清空');
      } catch (e) {
        debugPrint('⚠️ 清空RFID去重信息失败: $e');
      }

      // 2. 清空收集的UID
      debugPrint('🧹 清空收集的UID...');
      _collectedUids.clear();
      debugPrint('✅ 收集的UID已清空: ${_collectedUids.length}个');

      // 3. 通知页面清空显示
      debugPrint('📱 通知页面清空显示...');
      _notifyPageClearForMaster();

    } catch (e) {
      debugPrint('❌ 主机模式出馆结束清空失败: $e');
    }
  }

  /// 🔥 新增：从机模式出馆结束清空
  void _handleSlaveExitEndCleanup(MasterSlaveExtension extension) {
    try {
      // 1. 清空从机收集数据
      debugPrint('🧹 清空从机收集数据...');
      try {
        extension.clearCollectedData();
        debugPrint('✅ 从机收集数据已清空');
      } catch (e) {
        debugPrint('⚠️ 清空从机收集数据失败: $e');
      }

      // 2. 停止持续数据收集
      debugPrint('⏹️ 停止持续数据收集...');
      try {
        extension.stopContinuousDataCollection();
        debugPrint('✅ 持续数据收集已停止');
      } catch (e) {
        debugPrint('⚠️ 停止持续数据收集失败: $e');
      }

      // 3. 清空收集的UID
      debugPrint('🧹 清空收集的UID...');
      _collectedUids.clear();
      debugPrint('✅ 收集的UID已清空: ${_collectedUids.length}个');

    } catch (e) {
      debugPrint('❌ 从机模式出馆结束清空失败: $e');
    }
  }

  /// 🔥 新增：立即发送成功信号（≤9秒情况）
  void _sendSuccessSignalImmediately() {
    debugPrint('📤 立即发送成功信号到闸机：AA 00 01 01 00 00 48 36');
    _sendGateCommandAsync('success_signal');
    debugPrint('📤 成功信号已发送（异步）');

    _setState(GateState.enterOpening);

    // 设置自动完成定时器（模拟用户通过闸机）
    Timer(const Duration(seconds: 3), () {
      if (_currentState == GateState.enterOpening) {
        debugPrint('模拟进馆完成，状态重置为idle');
        _setState(GateState.idle);
        _isInEnterFlow = false; // 清除流程状态
        _hasEnterAuthSuccess = false; // 重置认证状态
        _lastPositionReachedTime = null; // 重置时间记录
        _authSuccessTime = null;
      }
    });
  }

  /// 🔥 新增：等待下一次到位信号（>9秒情况）
  void _waitForNextPositionSignal() {
    debugPrint('⏳ 等待下一次进馆到位信号...');
    _setState(GateState.enterStarted); // 回到等待到位状态

    // 保存认证成功状态，等待下次到位信号
    // 认证状态保持，下次到位时会检查并处理
  }

  /// 🔥 新增：处理待处理的进馆认证
  void _processPendingEnterAuth() {
    debugPrint('🎯 处理待处理的进馆认证：用户=$_pendingUserName');

    // 发送成功信号
    debugPrint('📤 正在发送成功信号到闸机：AA 00 01 01 00 00 48 36');
    _sendGateCommandAsync('success_signal');
    debugPrint('📤 成功信号已发送（异步）');

    _setState(GateState.enterOpening);

    // 通知UI显示成功信息
    _eventController.add(GateEvent.createAuthSuccess(
      userName: _pendingUserName ?? '未知用户',
      userId: _pendingUserId,
    ));

    // 设置自动完成定时器
    Timer(const Duration(seconds: 3), () {
      if (_currentState == GateState.enterOpening) {
        debugPrint('进馆完成，状态重置为idle');
        _setState(GateState.idle);
        _isInEnterFlow = false; // 清除流程状态
      }
    });

    // 清除待处理状态
    _clearPendingAuth();
  }

  /// 🔥 新增：处理待处理的出馆检查
  void _processPendingExitAuth() {
    debugPrint('🎯 处理待处理的出馆检查：消息=$_pendingMessage');

    // 发送成功信号
    debugPrint('📤 正在发送成功信号到闸机：AA 00 01 01 00 00 48 36');
    _sendGateCommandAsync('success_signal');
    debugPrint('📤 成功信号已发送（异步）');

    _setState(GateState.exitOver);

    // 发送允许出馆事件
    _eventController.add(GateEvent.createExitAllowed(
      message: _pendingMessage ?? '检查通过',
      books: _pendingBooks?.map((book) => book.toJson()).toList(),
    ));

    // 设置自动完成定时器
    Timer(const Duration(seconds: 3), () {
      if (_currentState == GateState.exitOver) {
        debugPrint('出馆完成，状态重置为idle');
        _setState(GateState.idle);
        _isInExitFlow = false; // 清除流程状态
      }
    });

    // 清除待处理状态
    _clearPendingAuth();
  }

  /// 释放资源
  @override
  void dispose() {
    debugPrint('释放闸机协调器资源');

    _commandSubscription?.cancel();
    _authSubscription?.cancel();
    _serialErrorSubscription?.cancel();

    // 🔥 确保取消RFID监听器
    _cancelRFIDListeners();

    // 🔥 取消9秒计时器
    _cancel8SecondTimer();

    // 🔥 修改：dispose时停止数据收集（保持硬件扫描运行）
    _stopDataCollection();
    _cancelStateTimeout();

    // 🔥 新增：清理新增的计时器和状态
    _cancel8SecondTimer();
    _clearPendingAuth();

    _serialService.dispose();
    _eventController.close();

    super.dispose();

    debugPrint('闸机协调器已释放');
  }
}
