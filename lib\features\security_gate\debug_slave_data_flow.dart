import 'dart:async';
import 'package:flutter/foundation.dart';

import 'services/master_slave_extension.dart';
import 'services/shared_scan_pool_service.dart';
import 'viewmodels/silence_page_viewmodel.dart';

/// 调试从机数据流的工具类
class SlaveDataFlowDebugger {
  static SlaveDataFlowDebugger? _instance;
  static SlaveDataFlowDebugger get instance => _instance ??= SlaveDataFlowDebugger._();
  SlaveDataFlowDebugger._();

  StreamSubscription? _dataChangeSubscription;
  SilencePageViewModel? _viewModel;
  bool _isDebugging = false;

  /// 开始调试从机数据流
  Future<void> startDebugging() async {
    if (_isDebugging) {
      debugPrint('⚠️ 调试已在进行中');
      return;
    }

    _isDebugging = true;
    debugPrint('🔍 开始调试从机数据流...');

    try {
      // 1. 配置为从机模式
      await _setupSlaveMode();
      
      // 2. 监听数据变化
      _setupDataChangeListener();
      
      // 3. 初始化ViewModel
      await _setupViewModel();
      
      // 4. 模拟数据变化
      await _simulateDataChanges();
      
      debugPrint('✅ 从机数据流调试启动成功');
    } catch (e) {
      debugPrint('❌ 从机数据流调试启动失败: $e');
      _isDebugging = false;
    }
  }

  /// 配置为从机模式
  Future<void> _setupSlaveMode() async {
    debugPrint('🔧 配置为从机模式...');
    
    final extension = MasterSlaveExtension.instance;
    
    // 如果已经是从机模式，跳过配置
    if (extension.isEnabled && !extension.isMaster) {
      debugPrint('✅ 已经是从机模式');
      return;
    }
    
    // 禁用现有配置
    if (extension.isEnabled) {
      extension.disable();
      await Future.delayed(const Duration(milliseconds: 100));
    }
    
    // 启用从机模式
    await extension.enable(
      channelId: 'debug_channel',
      isMaster: false,
      masterAddress: '127.0.0.1',
      port: 8888,
    );
    
    debugPrint('✅ 从机模式配置完成');
  }

  /// 设置数据变化监听
  void _setupDataChangeListener() {
    debugPrint('🔧 设置数据变化监听...');
    
    final extension = MasterSlaveExtension.instance;
    
    _dataChangeSubscription = extension.dataChangeStream.listen((barcodes) {
      debugPrint('📥 [调试] 接收到数据变化通知: ${barcodes.length}个条码');
      debugPrint('📊 [调试] 条码列表: $barcodes');
    });
    
    debugPrint('✅ 数据变化监听设置完成');
  }

  /// 设置ViewModel
  Future<void> _setupViewModel() async {
    debugPrint('🔧 设置ViewModel...');
    
    _viewModel = SilencePageViewModel();
    await _viewModel!.initialize();
    
    // 监听ViewModel状态变化
    _viewModel!.addListener(() {
      final scannedCount = _viewModel!.scannedBarcodes.length;
      final pageState = _viewModel!.currentPageState;
      debugPrint('📱 [调试] ViewModel状态变化: $scannedCount个条码, 页面状态: $pageState');
    });
    
    debugPrint('✅ ViewModel设置完成');
  }

  /// 模拟数据变化
  Future<void> _simulateDataChanges() async {
    debugPrint('🎭 开始模拟数据变化...');
    
    final sharedPool = SharedScanPoolService.instance;
    
    // 等待2秒
    await Future.delayed(const Duration(seconds: 2));
    
    // 添加测试数据
    debugPrint('📊 添加测试数据: TEST001');
    sharedPool.addBarcode('TEST001');
    
    await Future.delayed(const Duration(seconds: 1));
    
    debugPrint('📊 添加测试数据: TEST002');
    sharedPool.addBarcode('TEST002');
    
    await Future.delayed(const Duration(seconds: 1));
    
    debugPrint('📊 添加测试数据: TEST003');
    sharedPool.addBarcode('TEST003');
    
    debugPrint('✅ 数据模拟完成');
  }

  /// 停止调试
  void stopDebugging() {
    if (!_isDebugging) {
      debugPrint('⚠️ 调试未在进行中');
      return;
    }

    debugPrint('🛑 停止从机数据流调试...');
    
    _dataChangeSubscription?.cancel();
    _dataChangeSubscription = null;
    
    _viewModel?.dispose();
    _viewModel = null;
    
    _isDebugging = false;
    
    debugPrint('✅ 从机数据流调试已停止');
  }

  /// 获取调试状态
  Map<String, dynamic> getDebugStatus() {
    final extension = MasterSlaveExtension.instance;
    
    return {
      'debugging': _isDebugging,
      'master_slave_enabled': extension.isEnabled,
      'is_master': extension.isMaster,
      'channel_id': extension.channelId,
      'shared_pool_size': SharedScanPoolService.instance.poolSize,
      'viewmodel_initialized': _viewModel != null,
      'scanned_count': _viewModel?.scannedBarcodes.length ?? 0,
      'page_state': _viewModel?.currentPageState.toString(),
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// 手动触发数据变化测试
  void triggerDataChangeTest() {
    debugPrint('🧪 手动触发数据变化测试...');
    
    final extension = MasterSlaveExtension.instance;
    final testData = ['MANUAL_TEST_001', 'MANUAL_TEST_002'];
    
    // 直接调用数据变化通知
    try {
      // 通过反射或直接访问私有方法（这里用模拟的方式）
      debugPrint('📤 模拟数据变化通知: $testData');
      
      // 如果有公共方法可以触发数据变化，在这里调用
      // extension.triggerDataChange(testData);
      
    } catch (e) {
      debugPrint('❌ 手动触发数据变化失败: $e');
    }
  }

  /// 检查数据流连接状态
  void checkDataFlowConnection() {
    debugPrint('🔍 检查数据流连接状态...');
    
    final extension = MasterSlaveExtension.instance;
    final status = extension.getStatus();
    
    debugPrint('📊 主从机扩展状态: $status');
    
    final hasDataSubscription = _dataChangeSubscription != null;
    debugPrint('📡 数据订阅状态: $hasDataSubscription');
    
    final viewModelStatus = _viewModel != null;
    debugPrint('📱 ViewModel状态: $viewModelStatus');
    
    if (_viewModel != null) {
      final scannedBarcodes = _viewModel!.scannedBarcodes;
      debugPrint('📊 当前扫描结果: ${scannedBarcodes.length}个条码 - $scannedBarcodes');
    }
  }

  /// 运行完整的调试流程
  static Future<void> runFullDebug() async {
    final debugger = SlaveDataFlowDebugger.instance;
    
    try {
      await debugger.startDebugging();
      
      // 等待10秒观察数据流
      await Future.delayed(const Duration(seconds: 10));
      
      // 检查连接状态
      debugger.checkDataFlowConnection();
      
      // 手动触发测试
      debugger.triggerDataChangeTest();
      
      // 再等待5秒
      await Future.delayed(const Duration(seconds: 5));
      
      // 最终状态检查
      final finalStatus = debugger.getDebugStatus();
      debugPrint('🏁 最终调试状态: $finalStatus');
      
    } finally {
      debugger.stopDebugging();
    }
  }
}
