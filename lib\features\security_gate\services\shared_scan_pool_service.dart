import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:hardware/hardware.dart';
import 'package:seasetting/seasetting.dart';
import 'rfid_service.dart';


/// 共享扫描池服务 - 管理列表1
/// 集成现有RFID服务，主机负责填充，从机通过网络访问
/// 🔥 新设计：应用启动时就初始化，不依赖主从机模式
class SharedScanPoolService {
  static final _instance = SharedScanPoolService._internal();
  static SharedScanPoolService get instance => _instance;
  SharedScanPoolService._internal();

  // 列表1：共享扫描池（自动去重）
  final Set<String> _scanPool = <String>{};
  
  // 🔥 新增：条码时间戳管理（用于时间窗口过滤）
  final Map<String, DateTime> _barcodeTimestamps = <String, DateTime>{};

  // 扫描池变化流
  final StreamController<Set<String>> _poolController =
      StreamController<Set<String>>.broadcast();

  // 错误流
  final StreamController<String> _errorController =
      StreamController<String>.broadcast();

  // 🔥 集成现有RFID服务
  RFIDService? _rfidService;
  StreamSubscription? _rfidBarcodeSubscription;

  /// 获取扫描池变化流
  Stream<Set<String>> get poolStream => _poolController.stream;
  
  /// 获取错误流
  Stream<String> get errorStream => _errorController.stream;
  
  /// 获取当前扫描池大小
  int get poolSize => _scanPool.length;
  
  /// 获取当前扫描池快照
  Set<String> getCurrentPool() {
    return Set<String>.from(_scanPool);
  }

  /// 🔥 新增：全局初始化方法（应用启动时调用）
  static Future<void> initializeGlobal() async {
    try {
      debugPrint('开始全局初始化共享扫描池服务...');

      // 确保RFID服务已初始化
      if (!RFIDService.instance.isInitialized) {
        await RFIDService.instance.initialize();
      }

      // 初始化共享池与RFID服务的集成
      await instance.initializeWithRFID();

      debugPrint('共享扫描池服务全局初始化完成');
    } catch (e) {
      debugPrint('共享扫描池服务全局初始化失败: $e');
      // 不抛出异常，允许应用继续运行
    }
  }

  /// 🔥 集成现有RFID服务
  Future<void> initializeWithRFID() async {
    try {
      _rfidService = RFIDService.instance;

      // 监听现有RFID服务的条码流
      _rfidBarcodeSubscription = _rfidService!.barcodeStream.listen((barcode) {
        addBarcode(barcode);
      });

      debugPrint('共享扫描池已集成现有RFID服务');

      // 🔁 修复启动竞态：共享池订阅建立后，立即请求RFID重新识别当前场上标签
      try {
        final scanning = _rfidService!.isScanning;
        debugPrint('📡 初始化后RFID扫描状态: scanning=$scanning');
        // 触发重置去重集合，让当前标签重新产出条码事件（不影响集合A）
        _rfidService!.resetProcessedBarcodes();
      } catch (e) {
        debugPrint('⚠️ 初始化后触发RFID重置失败: $e');
      }
    } catch (e) {
      final errorMsg = '集成RFID服务失败: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
    }
  }

  /// 清空扫描池
  /// 在任一通道开始出馆流程时调用
  void clearPool() {
    try {
      final sizeBefore = _scanPool.length;
      final contentBefore = _scanPool.toList();

      debugPrint('🧹 开始清空共享扫描池...');
      debugPrint('📊 清空前状态: 大小=$sizeBefore, 内容=$contentBefore');

      _scanPool.clear();

      // 🔥 同时清空时间戳记录
      _barcodeTimestamps.clear();

      // 🔥 关键修复：清空共享池时同时重置去重集合，并确保轮询处于运行状态
      if (_rfidService != null) {
        debugPrint('🔄 重置RFID去重集合...');
        _rfidService!.resetProcessedBarcodes();
        debugPrint('✅ 已重置RFID去重集合，现有标签将被重新识别');

        // 确认主机轮询在运行（只读保障，不影响集合A）
        final isScanning = _rfidService!.isScanning;
        debugPrint('📡 RFID扫描状态（清空后）: isScanning=$isScanning');
        if (!isScanning) {
          debugPrint('⚠️ RFID未在扫描状态，尝试启动数据收集以恢复轮询...');
          // 注意：此处为同步方法，不能使用 await。改为非阻塞启动并记录结果。
          _rfidService!.startDataCollection().then((_) {
            debugPrint('✅ 已启动RFID数据收集（恢复轮询）');
          }).catchError((e) {
            debugPrint('❌ 启动RFID数据收集失败: $e');
          });
        }
      } else {
        debugPrint('⚠️ RFID服务不可用，无法重置去重集合');
      }

      // ❌ 移除全局RFID清空：不应该影响各通道独立的RFID扫描状态
      // 各通道应该独立管理自己的RFID扫描状态和计数
      // _rfidService?.clearScanResult();

      _notifyPoolChange();
      debugPrint('✅ 共享扫描池已清空: $sizeBefore -> ${_scanPool.length}');
      debugPrint('📡 清空通知已发送，等待RFID重新检测标签...');
    } catch (e) {
      final errorMsg = '清空扫描池失败: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
    }
  }

  /// 🔥 修改：清空扫描池并清空RFID缓冲区（不清空HWTagProvider）
  Future<void> clearPoolAndBuffer() async {
    try {
      debugPrint('🧹 开始清空共享扫描池和所有缓冲区...');

      // 🔥 新增：清空LSGate硬件缓冲区
      await _clearLSGateHardwareBuffer();

      // 清空共享池
      clearPool();

      // 清空RFID缓冲区（但不清空HWTagProvider）
      if (_rfidService != null) {
        await _rfidService!.clearScanBuffer();

        // 🔥 关键修复：重置已处理条码集合，让当前场上的标签能够重新被识别
        _rfidService!.resetProcessedBarcodes();
      }

      debugPrint('✅ 共享扫描池、RFID缓冲区和LSGate硬件缓冲区已清空');
    } catch (e) {
      final errorMsg = '❌ 清空扫描池和缓冲区失败: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
    }
  }

  /// 🔥 新增：清空LSGate硬件缓冲区
  Future<void> _clearLSGateHardwareBuffer() async {
    try {
      // 检查是否配置了LSGate设备
      if (_isLSGateDevice()) {
        debugPrint('🧹 检测到LSGate设备，开始清空硬件缓冲区...');

        // 🔥 调用ReaderManager的clearLSGateCache方法（如果可用）
        try {
          // 使用反射调用clearLSGateCache方法（如果存在）
          final readerManager = ReaderManager.instance;
          final clearResult = (readerManager as dynamic).clearLSGateCache?.call() ?? 0;
          debugPrint('📊 LSGate硬件缓存清空命令发送结果: $clearResult');
        } catch (e) {
          debugPrint('⚠️ clearLSGateCache方法不可用，使用备用方案: $e');
          // 备用方案：直接调用LSGateWindowsBridge
          await _clearLSGateDirectly();
        }

        // 等待硬件清空完成
        await Future.delayed(const Duration(milliseconds: 200));

        debugPrint('✅ LSGate硬件缓冲区清空完成');
      } else {
        debugPrint('📊 当前未配置LSGate设备，跳过硬件缓冲区清空');
      }
    } catch (e) {
      debugPrint('❌ 清空LSGate硬件缓冲区异常: $e');
      // 不抛出异常，允许其他清空操作继续
    }
  }

  /// 🔥 备用方案：直接调用LSGateWindowsBridge清空缓存
  Future<void> _clearLSGateDirectly() async {
    try {
      debugPrint('🔧 使用备用方案：通过硬件包清空LSGate缓存');
      // 暂时使用日志记录，等待硬件包更新后实现
      debugPrint('⚠️ 等待硬件包更新clearLSGateCache方法');
    } catch (e) {
      debugPrint('❌ LSGate直接清空失败: $e');
    }
  }

  /// 🔥 新增：检查是否配置了LSGate设备
  bool _isLSGateDevice() {
    try {
      final settingProvider = Get.context?.read<SettingProvider>();
      final readerConfigs = settingProvider?.readerConfigData?.bookReaders ?? [];

      // 检查是否有LSGate类型的阅读器（readerType == 22）
      for (final config in readerConfigs) {
        if (config.readerType == 22 || config.info is HWLSGateInfoData) {
          debugPrint('📡 检测到LSGate设备配置: readerType=${config.readerType}');
          return true;
        }
      }
      return false;
    } catch (e) {
      debugPrint('⚠️ 检查LSGate设备类型失败: $e');
      return false;
    }
  }

  /// 添加条码到扫描池
  /// 主机RFID扫描时调用，Set自动去重
  void addBarcode(String barcode) {
    if (barcode.isEmpty) {
      debugPrint('⚠️ 尝试添加空条码到共享池，忽略');
      return;
    }

    try {
      final sizeBefore = _scanPool.length;
      final wasEmpty = _scanPool.isEmpty;

      debugPrint('🔄 尝试添加条码到共享池: $barcode');
      debugPrint('📊 添加前状态: 共享池大小=$sizeBefore, 是否为空=$wasEmpty');

      _scanPool.add(barcode);

      // 只有真正添加了新条码才记录时间戳和通知变化
      if (_scanPool.length > sizeBefore) {
        // 🔥 记录条码添加时间
        _barcodeTimestamps[barcode] = DateTime.now();

        debugPrint('✅ 成功添加条码到共享池: $barcode (总计: ${_scanPool.length})');
        debugPrint('📋 当前共享池内容: ${_scanPool.toList()}');
        _notifyPoolChange();
        debugPrint('📡 共享池变化通知已发送');
      } else {
        debugPrint('🔄 条码已存在于共享池: $barcode (总计: ${_scanPool.length})');
      }
    } catch (e) {
      final errorMsg = '添加条码失败: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
    }
  }

  /// 批量添加条码到扫描池
  /// 用于从机同步数据
  void addBarcodes(List<String> barcodes) {
    if (barcodes.isEmpty) return;
    
    try {
      final sizeBefore = _scanPool.length;
      _scanPool.addAll(barcodes.where((b) => b.isNotEmpty));
      
      if (_scanPool.length > sizeBefore) {
        debugPrint('批量添加${barcodes.length}个条码，实际新增${_scanPool.length - sizeBefore}个');
        _notifyPoolChange();
      }
    } catch (e) {
      final errorMsg = '批量添加条码失败: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
    }
  }

  /// 同步扫描池数据（用于从机）
  /// 完全替换当前扫描池
  void syncPool(Set<String> newPool) {
    try {
      final oldSize = _scanPool.length;
      _scanPool.clear();
      _scanPool.addAll(newPool);
      
      debugPrint('同步扫描池: $oldSize -> ${_scanPool.length}');
      _notifyPoolChange();
    } catch (e) {
      final errorMsg = '同步扫描池失败: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
    }
  }

  /// 获取扫描池的JSON表示（用于网络传输）
  String toJson() {
    try {
      return jsonEncode(_scanPool.toList());
    } catch (e) {
      debugPrint('序列化扫描池失败: $e');
      return '[]';
    }
  }

  /// 从JSON恢复扫描池（用于网络传输）
  void fromJson(String jsonStr) {
    try {
      final List<dynamic> list = jsonDecode(jsonStr);
      final Set<String> newPool = list.cast<String>().toSet();
      syncPool(newPool);
    } catch (e) {
      final errorMsg = '反序列化扫描池失败: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
    }
  }

  /// 检查条码是否在扫描池中
  bool containsBarcode(String barcode) {
    return _scanPool.contains(barcode);
  }

  /// 移除指定条码（一般不需要，但提供接口）
  bool removeBarcode(String barcode) {
    try {
      final removed = _scanPool.remove(barcode);
      if (removed) {
        debugPrint('从扫描池移除条码: $barcode');
        _notifyPoolChange();
      }
      return removed;
    } catch (e) {
      final errorMsg = '移除条码失败: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
      return false;
    }
  }

  /// 通知扫描池变化
  void _notifyPoolChange() {
    if (!_poolController.isClosed) {
      _poolController.add(Set<String>.from(_scanPool));
    }
  }

  /// 🔥 新增：获取指定时间后的条码
  Set<String> getBarcodesAfter(DateTime timeThreshold) {
    final filteredBarcodes = <String>{};
    
    for (final barcode in _scanPool) {
      final timestamp = _barcodeTimestamps[barcode];
      if (timestamp != null && timestamp.isAfter(timeThreshold)) {
        filteredBarcodes.add(barcode);
      }
    }
    
    return filteredBarcodes;
  }

  /// 🔥 新增：获取条码的时间戳
  DateTime? getBarcodeTimestamp(String barcode) {
    return _barcodeTimestamps[barcode];
  }

  /// 获取扫描池统计信息
  Map<String, dynamic> getStatistics() {
    return {
      'pool_size': _scanPool.length,
      'pool_content': _scanPool.toList(),
      'timestamps_count': _barcodeTimestamps.length,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// 清理资源
  void dispose() {
    _rfidBarcodeSubscription?.cancel();
    _rfidBarcodeSubscription = null;
    _rfidService = null;
    _scanPool.clear();
    _poolController.close();
    _errorController.close();
  }
}
