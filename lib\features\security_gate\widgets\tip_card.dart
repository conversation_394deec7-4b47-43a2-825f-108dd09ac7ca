import 'package:a3g/core/utils/window_util.dart';
import 'package:flutter/material.dart';

import '../../../shared/utils/asset_util.dart';

class TipCard extends StatelessWidget {
  final Widget child;
  const TipCard({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 700.p,
      height: 365.p,
      padding: EdgeInsets.all(40.p),
      decoration: BoxDecoration(
        image: DecorationImage(
          image: AssetImage(AssetUtil.fullPath('tip_card_bg')),
        )
        
      ),
      child: child,
      
    );
  }
}