import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/utils/window_util.dart';
import '../../../core/widgets/custom_button.dart';
import '../view_models/device_view_model.dart';

class ApiConfigPanel extends StatefulWidget {
  const ApiConfigPanel({Key? key}) : super(key: key);

  @override
  State<ApiConfigPanel> createState() => _ApiConfigPanelState();
}

class _ApiConfigPanelState extends State<ApiConfigPanel> {
  final _urlController = TextEditingController();
  final _libraryCodeController = TextEditingController();
  final _deviceMacController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    // 监听ViewModel变化，同步数据到控制器
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final viewModel = context.read<DeviceViewModel>();
      _urlController.text = viewModel.authBaseUrl;  // 使用认证baseurl
      _libraryCodeController.text = viewModel.libraryCode;
      _deviceMacController.text = viewModel.deviceMac;
    });
  }

  @override
  void dispose() {
    _urlController.dispose();
    _libraryCodeController.dispose();
    _deviceMacController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<DeviceViewModel>(
      builder: (context, viewModel, child) {
        // 同步数据到控制器
        if (_urlController.text != viewModel.authBaseUrl) {
          _urlController.text = viewModel.authBaseUrl;
        }
        if (_libraryCodeController.text != viewModel.libraryCode) {
          _libraryCodeController.text = viewModel.libraryCode;
        }
        if (_deviceMacController.text != viewModel.deviceMac) {
          _deviceMacController.text = viewModel.deviceMac;
        }
        
        return Card(
          margin: EdgeInsets.all(16.p),
          child: Column(
            children: [
              // 标题栏
              ListTile(
                title: Text(
                  'API接口配置',
                  style: TextStyle(
                    fontSize: 18.p,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF333333),
                  ),
                ),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '认证地址: ${viewModel.authBaseUrl}',
                      style: TextStyle(
                        fontSize: 12.p,
                        color: const Color(0xFF666666),
                      ),
                    ),
                    Text(
                      '图书馆代码: ${viewModel.libraryCode}',
                      style: TextStyle(
                        fontSize: 12.p,
                        color: const Color(0xFF666666),
                      ),
                    ),
                    Text(
                      '设备MAC: ${viewModel.deviceMac}',
                      style: TextStyle(
                        fontSize: 12.p,
                        color: const Color(0xFF666666),
                      ),
                    ),
                  ],
                ),
                trailing: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 连接测试按钮
                    IconButton(
                      onPressed: viewModel.isLoading ? null : () {
                        viewModel.testConnection();
                      },
                      icon: viewModel.isLoading 
                        ? SizedBox(
                            width: 20.p,
                            height: 20.p,
                            child: const CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.wifi_find),
                      tooltip: '测试连接',
                    ),
                    
                    // 展开/收起按钮
                    IconButton(
                      onPressed: () {
                        setState(() {
                          _isExpanded = !_isExpanded;
                        });
                      },
                      icon: Icon(_isExpanded ? Icons.expand_less : Icons.expand_more),
                      tooltip: _isExpanded ? '收起' : '展开配置',
                    ),
                  ],
                ),
              ),
              
              // 配置面板
              if (_isExpanded) ...[
                const Divider(),
                Padding(
                  padding: EdgeInsets.all(16.p),
                  child: _buildConfigForm(viewModel),
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildConfigForm(DeviceViewModel viewModel) {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // API地址输入
          Text(
            'API基础地址',
            style: TextStyle(
              fontSize: 16.p,
              fontWeight: FontWeight.w500,
              color: const Color(0xFF333333),
            ),
          ),
          SizedBox(height: 8.p),
          
          TextFormField(
            controller: _urlController,
            decoration: InputDecoration(
              hintText: '请输入API基础地址，如: http://172.16.0.118:9000',
              hintStyle: TextStyle(
                fontSize: 14.p,
                color: const Color(0xFF999999),
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.p),
                borderSide: const BorderSide(color: Color(0xFFE0E0E0)),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.p),
                borderSide: const BorderSide(color: Color(0xFFE0E0E0)),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.p),
                borderSide: const BorderSide(color: Color(0xFF54A0FF)),
              ),
              contentPadding: EdgeInsets.symmetric(horizontal: 12.p, vertical: 12.p),
              suffixIcon: IconButton(
                onPressed: () {
                  _urlController.clear();
                },
                icon: const Icon(Icons.clear, size: 20),
                tooltip: '清空',
              ),
            ),
            style: TextStyle(fontSize: 14.p),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'API地址不能为空';
              }
              
              // 简单的URL格式验证
              final url = value.trim();
              if (!url.startsWith('http://') && !url.startsWith('https://')) {
                return 'URL格式不正确，请以http://或https://开头';
              }
              
              return null;
            },
          ),
          
          SizedBox(height: 16.p),
          
          // 接口信息展示
          _buildEndpointInfo(),
          
          SizedBox(height: 24.p),
          
          // 操作按钮
          Row(
            children: [
              Expanded(
                child: CustomButton.outline(
                  text: '重置默认',
                  onTap: () {
                    viewModel.resetToDefault();
                  },
                ),
              ),
              SizedBox(width: 16.p),
              Expanded(
                child: CustomButton.filled(
                  text: '保存配置',
                  onTap: () {
                    if (_formKey.currentState!.validate()) {
                      viewModel.updateAuthBaseUrl(_urlController.text.trim());
                    }
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEndpointInfo() {
    return Container(
      padding: EdgeInsets.all(12.p),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(8.p),
        border: Border.all(color: const Color(0xFFE9ECEF)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '接口端点信息',
            style: TextStyle(
              fontSize: 14.p,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF495057),
            ),
          ),
          SizedBox(height: 8.p),
          
          // 设备管理接口
          Text(
            '设备管理接口',
            style: TextStyle(
              fontSize: 12.p,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF007BFF),
            ),
          ),
          SizedBox(height: 4.p),
          _buildEndpointItem('设备注册', '/tunano/ldc/entrance/v1/api/door/register', 'POST'),
          SizedBox(height: 2.p),
          _buildEndpointItem('状态上传', '/tunano/ldc/entrance/v1/api/door/status', 'POST'),
          SizedBox(height: 2.p),
          _buildEndpointItem('读者认证', '/tunano/ldc/entrance/v1/api/door/verify', 'POST'),

          SizedBox(height: 8.p),

          // 书籍管理接口
          Text(
            '书籍管理接口',
            style: TextStyle(
              fontSize: 12.p,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF28A745),
            ),
          ),
          SizedBox(height: 4.p),
          _buildEndpointItem('白名单检查', '/tunano/ldc/white/tag/whitelist/contain', 'POST'),
          SizedBox(height: 2.p),
          _buildEndpointItem('UID转条码', '/tunano/rfdc/tag/v1/rfdc/tag/booktag/...', 'GET'),
          SizedBox(height: 2.p),
          _buildEndpointItem('图书信息查询', '/tunano/cdc/acs/v1/cdc/acs/queryBookInfo', 'GET'),
        ],
      ),
    );
  }

  Widget _buildEndpointItem(String name, String endpoint, String method) {
    return Row(
      children: [
        Container(
          padding: EdgeInsets.symmetric(horizontal: 6.p, vertical: 2.p),
          decoration: BoxDecoration(
            color: method == 'POST' ? const Color(0xFF28A745) : const Color(0xFF007BFF),
            borderRadius: BorderRadius.circular(4.p),
          ),
          child: Text(
            method,
            style: TextStyle(
              fontSize: 10.p,
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        SizedBox(width: 8.p),
        
        Text(
          '$name: ',
          style: TextStyle(
            fontSize: 12.p,
            fontWeight: FontWeight.w500,
            color: const Color(0xFF495057),
          ),
        ),
        
        Expanded(
          child: Text(
            endpoint,
            style: TextStyle(
              fontSize: 12.p,
              color: const Color(0xFF6C757D),
              fontFamily: 'monospace',
            ),
          ),
        ),
      ],
    );
  }
}
