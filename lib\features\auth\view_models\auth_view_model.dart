import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hardware/hardware.dart';
import 'package:oktoast/oktoast.dart';
import 'package:provider/provider.dart';
import 'package:sea_socket/sea_socket.dart';
import 'package:seasetting/seasetting.dart';
import 'package:base_package/base_package.dart';
import 'package:intl/intl.dart';

import '../../../core/providers/user_provider.dart';
import '../../../core/router/app_router.dart';
import '../../../core/database/implementations/sqlite_db.dart';
import '../../../core/services/post_auth_service.dart';
import '../models/auth_result.dart';
import '../repositories/auth_repository.dart';
import '../services/door_auth_service.dart';
import '../services/user_info_service.dart';
import '../services/gate_context_service.dart';

class AuthViewModel extends ChangeNotifier {
  // 基础依赖
  final AuthRepository _repository = AuthRepository();
  final BuildContext context;

  // 🔥 新增：门径认证服务
  final DoorAuthService _doorAuthService = DoorAuthService.instance;
  final UserInfoService _userInfoService = UserInfoService.instance;
  final GateContextService _gateContextService = GateContextService.instance;
  
  // 认证状态
  AuthStatus _authStatus = AuthStatus.failureNoMatch;
  AuthMethod _currentMethod = AuthMethod.face;
  String? _userName;
  String? _userId;
  AuthResult? _lastResult;
  bool _isAuthenticating = false;
  
  // 读卡器相关
  late final HWTagProvider _tagProvider;
  late final SettingProvider _settingProvider;
  List<HWReaderSettingData>? _openedReaders;
  bool _isListening = false;
  String? _errorMessage;
  
  // 读者信息
  Sip2PatronInfoData? _readerInfo;

  // Getters
  AuthStatus get authStatus => _authStatus;
  AuthMethod get currentMethod => _currentMethod;
  String? get userName => _userName;
  String? get userId => _userId;
  AuthResult? get lastResult => _lastResult;
  bool get isAuthenticating => _isAuthenticating;
  String? get errorMessage => _errorMessage;
  Sip2PatronInfoData? get readerInfo => _readerInfo;

  AuthViewModel({required this.context}) {
    // 从Provider获取依赖而不是使用Get.find
    _tagProvider = Provider.of<HWTagProvider>(context, listen: false);
    _settingProvider = Provider.of<SettingProvider>(context, listen: false);
  }

  // 初始化
  Future<void> init() async {
    try {
      await _initializeReader();
    } catch (e) {
      _handleError(e);
    }
  }

  // 设置认证方法
  void setAuthMethod(AuthMethod method) {
    debugPrint('设置认证方法: $method, 当前方法: $_currentMethod');
    
    if (_currentMethod != method) {
      // 切换认证方法时也清除用户信息
      clearUserInfo();
      
      // 先停止当前读卡器
      _cleanup();
      
      // 更新状态
      _currentMethod = method;
      debugPrint('认证方法已更新为: $_currentMethod');
      
      // 初始化新的读卡器
      _initializeReader();
      
      // 通知UI更新
      notifyListeners();
    }
  }

  // 执行认证过程
  Future<AuthResult> authenticate() async {
    if (_isAuthenticating) return _lastResult!;
    
    // 在开始新认证前，清除之前用户的信息
    clearUserInfo();
    
    _isAuthenticating = true;
    notifyListeners();
    
    try {
      AuthResult result;
      
      // 根据选择的认证方式调用不同的方法
      switch (_currentMethod) {
        case AuthMethod.face:
          // 人脸认证通过 AuthView 中的 CameraPreviewWidget 处理
          // 这里只是标记认证方式，实际认证通过 onFaceRecognized 回调触发
          result = await _repository.authenticateFace();
          break;
        case AuthMethod.idCard:
          result = await _repository.authenticateIdCard();
          break;
        case AuthMethod.readerCard:
          result = await _repository.authenticateReaderCard();
          break;
        case AuthMethod.qrCode:
          result = await _repository.authenticateQrCode();
          break;
        case AuthMethod.socialSecurityCard:
        case AuthMethod.citizenCard:
        case AuthMethod.eletricSocialSecurityCard:
          result = await _repository.authenticateReaderCard();
          break;
        case AuthMethod.wechatQRCode:
        case AuthMethod.wechatScanQRCode:
        case AuthMethod.alipayQRCode:
        case AuthMethod.aliCreditQRCode:
        case AuthMethod.huiwenQRCode:
        case AuthMethod.shangHaiQRCode:
        case AuthMethod.alipayQRCodeCredit:
          result = await _repository.authenticateQrCode();
          break;
        case AuthMethod.keyboardInput:
          result = await _repository.authenticateReaderCard();
          break;
        case AuthMethod.tencentTCard:
        case AuthMethod.imiAuth:
        case AuthMethod.jieYueBao:
          result = await _repository.authenticateReaderCard();
          break;
        case AuthMethod.takePhoto:
          result = await _repository.authenticateFace();
          break;
        case AuthMethod.wechatOrAlipay:
          result = await _repository.authenticateQrCode();
          break;
      }
      
      // 保存结果
      _authStatus = result.status;
      _userName = result.userName;
      _userId = result.userId;
      _lastResult = result;
      
      // 记录日志
      if (result.status == AuthStatus.success) {
        await _repository.saveAuthLog(result);
      }
      
      return result;
    } catch (e) {
      // 处理错误
      _handleError(e);
      _authStatus = AuthStatus.failureError;
      _lastResult = AuthResult(
        method: _currentMethod,
        status: AuthStatus.failureError,
      );
      return _lastResult!;
    } finally {
      _isAuthenticating = false;
      notifyListeners();
    }
  }

  // 重置认证状态
  void resetAuth() {
    _authStatus = AuthStatus.failureNoMatch;
    _userName = null;
    _userId = null;
    _lastResult = null;
    _readerInfo = null;
    notifyListeners();
  }


  // 🔥 修改：请求读者认证
  Future<void> requestReaderInfo(
    String barcode,
    String uid, {
    String? psw,
    HWIDCardInfo? info,
  }) async {
    try {
      _isAuthenticating = true;
      notifyListeners();

      // 🔥 使用新的门径认证接口
      final authResult = await _authenticateWithDoorApi(
        barcode.isNotEmpty ? barcode : uid,
        AuthMethod.readerCard,
      );

      if (authResult.status == AuthStatus.success) {
        // 认证成功，更新本地状态
        _readerInfo = authResult.readerInfo as Sip2PatronInfoData?;
        _userName = authResult.userName;
        _userId = authResult.userId;
        _authStatus = AuthStatus.success;

        // 通知UI状态已更改
        notifyListeners();
        debugPrint("已通知认证成功状态变更");

        // 如果有读者信息，保存认证记录
        if (_readerInfo != null) {
          await _saveAccessLog(_readerInfo!);
        }

        debugPrint('认证成功: 读者姓名=${authResult.userName}, 读者编号=${authResult.userId}');
      } else {
        // 认证失败
        _authStatus = authResult.status;
        _errorMessage = authResult.errorMessage ?? '认证失败';

        // 通知UI状态已更改
        notifyListeners();

        // 显示错误提示
        showToast(authResult.errorMessage ?? '认证失败，请重试');

        debugPrint('认证失败: ${authResult.errorMessage}');
      }
    } catch (e) {
      // 异常情况下也保存失败记录
      try {
        await _saveFailureAccessLog(barcode, uid, info, null);
      } catch (saveError) {
        debugPrint('保存异常失败记录时出错: $saveError');
      }

      _handleError(e);
    } finally {
      _isAuthenticating = false;
      notifyListeners();
    }
  }

  // 🔥 修改：使用门径认证接口获取认证结果
  Future<AuthResult> _authenticateWithDoorApi(
    String identifier,
    AuthMethod method,
  ) async {
    try {
      // 获取当前闸机状态，判断是进馆还是出馆
      final isEnterFlow = _gateContextService.isEnterFlow;

      debugPrint('使用门径认证接口: identifier=$identifier, method=$method, isEnter=$isEnterFlow');

      // 调用门径认证服务
      final authResult = await _doorAuthService.authenticate(
        identifier: identifier,
        method: method,
        isEnterFlow: isEnterFlow,
      );

      // 如果认证成功且需要用户详细信息，尝试获取
      if (authResult.status == AuthStatus.success) {
        final shouldFetchUserInfo = _gateContextService.shouldFetchUserInfo();

        if (shouldFetchUserInfo) {
          // 获取用户详细信息
          final enrichedResult = await _userInfoService.enrichAuthResult(
            baseResult: authResult,
            identifier: identifier,
            skipUserInfo: !shouldFetchUserInfo,
          );

          debugPrint('认证成功，已获取用户信息: ${enrichedResult.userName}');
          return enrichedResult;
        } else {
          debugPrint('认证成功，跳过用户信息获取');
          return authResult;
        }
      } else {
        debugPrint('门径认证失败: ${authResult.errorMessage}');
        return authResult;
      }
    } catch (e) {
      debugPrint('门径认证异常: $e');
      return AuthResult(
        method: method,
        status: AuthStatus.failureError,
        errorMessage: '认证服务异常: $e',
        timestamp: DateTime.now(),
      );
    }
  }

  // 🔥 保留：获取读者信息（用于兼容性，但标记为已弃用）
  @Deprecated('请使用_authenticateWithDoorApi方法')
  Future<Sip2PatronInfoData?> _fetchReaderInfo(
    String barcode,
    String uid,
    String? psw
  ) async {
    final cardTypes = _getCardTypes();

    if (cardTypes?.isNotEmpty ?? false) {
      // 尝试每种卡类型
      for (String cardType in cardTypes!) {
        try {
          final readerInfo = await NewSip2Request.instance.getReaderInfo(
            barcode,
            uid,
            psw: psw,
            CardType: cardType,
          ).timeout(const Duration(seconds: 10));

          if (readerInfo?.isSuccess ?? false) {
            return readerInfo;
          }
        } catch (e) {
          print('使用卡类型 $cardType 获取读者信息失败: $e');
          // 继续尝试下一种卡类型
          continue;
        }
      }
    }

    // 无卡类型或所有卡类型都失败时，使用默认请求
    try {
      return await NewSip2Request.instance.getReaderInfo(
        barcode,
        uid,
        psw: psw,
      ).timeout(const Duration(seconds: 10));
    } catch (e) {
      throw e;
    }
  }

  // 处理有效读者
  Future<void> _handleValidPatron(
    Sip2PatronInfoData readerInfo,
    String barcode,
    String uid,
    HWIDCardInfo? info,
  ) async {
    // 保存到数据库的记录
    final record = _createRecordData(
      barcode: barcode,
      uid: uid,
      info: info,
      readerInfo: readerInfo,
      isSuccess: true,
    );

    // 先清理资源
    await _cleanup();
    
    // 保存登录记录到数据库
    await _saveLoginRecord(record);

    // 更新本地状态，用于UI显示
    _readerInfo = readerInfo;
    _userName = readerInfo.PersonName;
    _userId = readerInfo.PatronIdentifier;
    _authStatus = AuthStatus.success;
    
    // 先通知UI状态已更改 - 确保监听器能立即感知到认证成功
    notifyListeners();
    debugPrint("已通知认证成功状态变更");
    
    // 保存认证记录到access_logs表
    await _saveAccessLog(readerInfo);
    
    // 调试输出确认数据
    debugPrint('认证成功: 读者姓名=${readerInfo.PersonName}, 读者编号=${readerInfo.PatronIdentifier}');
    
    // 调用后认证处理服务 - 异步执行，不阻塞认证流程
    try {
      String authMethodText = _getAuthMethodDisplayName(_currentMethod);
      debugPrint('调用认证后处理服务: ${readerInfo.PersonName}, 认证方式: $authMethodText');

      // 考勤系统：门锁操作不应该阻塞读卡器监听，改为异步执行
      PostAuthService.instance.executeHandlers(readerInfo, authMethodText).then((_) {
        debugPrint('认证后处理流程执行完毕');
      }).catchError((e) {
        debugPrint('调用认证后处理服务失败: $e');
        // 错误处理不影响正常流程
      });
    } catch (e) {
      debugPrint('启动认证后处理服务失败: $e');
      // 错误处理不影响正常流程
    }
    
    // 移除自动清除用户信息的计时器
    // 用户信息将在页面关闭或新认证开始时才被清除
    
    // 更新用户信息，但不要触发导航
    if (context.mounted) {
      // 使用我们自己的状态管理而非UserProvider来避免导航
      // 保持本地状态更新，确保底部卡片可以显示用户信息
    }
    
    // 再次通知以确保所有监听器都收到更新
    notifyListeners();
  }

  // 保存认证失败记录到access_logs表
  Future<void> _saveFailureAccessLog(
    String barcode,
    String uid,
    HWIDCardInfo? info,
    Sip2PatronInfoData? readerInfo
  ) async {
    try {
      // 获取数据库实例
      final db = await SqliteDB.instance();

      // 获取认证方式显示名称
      String authMethodText = _getAuthMethodDisplayName(_currentMethod);

      // 确定失败原因
      String failureReason = (readerInfo?.ScreenMessage?.isNotEmpty ?? false)
          ? readerInfo!.ScreenMessage!
          : '读者证不存在或密码错误';

      // 确定用户名称和证号
      String userName = info?.name ?? readerInfo?.PersonName ?? '未知用户';
      String readerCardNo = barcode.isNotEmpty ? barcode : uid;

      // 添加认证失败记录
      await db.addAccessLog(
        name: userName,
        readerCardNo: readerCardNo,
        openTime: DateTime.now(),
        authMethod: authMethodText,
        status: 'failure',
        remark: failureReason,
      );

      debugPrint('保存认证失败记录到access_logs表成功: $userName, $readerCardNo');

    } catch (e) {
      debugPrint('保存认证失败记录到access_logs表失败: $e');
    }
  }

  // 保存认证记录到access_logs表
  Future<void> _saveAccessLog(Sip2PatronInfoData readerInfo) async {
    try {
      // 获取数据库实例
      final db = await SqliteDB.instance();
      
      // 获取当前认证方法对应的文本描述
      String authMethodText;
      switch (_currentMethod) {
        case AuthMethod.face:
          authMethodText = '人脸认证';
          break;
        case AuthMethod.idCard:
          authMethodText = '身份证认证';
          break;
        case AuthMethod.readerCard:
          authMethodText = '读者证认证';
          break;
        case AuthMethod.qrCode:
          authMethodText = '二维码认证';
          break;
        case AuthMethod.socialSecurityCard:
          authMethodText = '社保卡认证';
          break;
        case AuthMethod.citizenCard:
          authMethodText = '市民卡认证';
          break;
        case AuthMethod.eletricSocialSecurityCard:
          authMethodText = '电子社保卡认证';
          break;
        case AuthMethod.wechatQRCode:
          authMethodText = '微信二维码认证';
          break;
        case AuthMethod.wechatScanQRCode:
          authMethodText = '微信扫码认证';
          break;
        case AuthMethod.alipayQRCode:
          authMethodText = '支付宝二维码认证';
          break;
        case AuthMethod.aliCreditQRCode:
          authMethodText = '支付宝信用二维码认证';
          break;
        case AuthMethod.huiwenQRCode:
          authMethodText = '汇文二维码认证';
          break;
        case AuthMethod.shangHaiQRCode:
          authMethodText = '上海二维码认证';
          break;
        case AuthMethod.keyboardInput:
          authMethodText = '键盘输入认证';
          break;
        case AuthMethod.tencentTCard:
          authMethodText = '腾讯T卡认证';
          break;
        case AuthMethod.imiAuth:
          authMethodText = 'IMI认证';
          break;
        case AuthMethod.takePhoto:
          authMethodText = '拍照认证';
          break;
        case AuthMethod.wechatOrAlipay:
          authMethodText = '微信或支付宝认证';
          break;
        case AuthMethod.alipayQRCodeCredit:
          authMethodText = '支付宝信用二维码认证';
          break;
        case AuthMethod.jieYueBao:
          authMethodText = '借阅宝认证';
          break;
      }
      
      // 添加认证记录
      await db.addAccessLog(
        name: readerInfo.PersonName ?? '未知用户',
        readerCardNo: readerInfo.PatronIdentifier ?? '未知ID',
        openTime: DateTime.now(),
        authMethod: authMethodText,
        status: 'success',
        remark: '认证成功',
      );
      
      debugPrint('保存认证记录到access_logs表成功');
      
      // 不再需要延迟清除用户信息，因为我们在新认证开始时清除
    } catch (e) {
      debugPrint('保存认证记录到access_logs表失败: $e');
    }
  }

  // 清除用户认证信息 - 改为完全重置认证状态
  void clearUserInfo() {
    debugPrint('清除用户认证信息 - 调用完全重置');
    // 直接调用resetAuth，确保所有状态一起重置
    resetAuth();
  }

  // 初始化读卡器
  Future<void> _initializeReader() async {
    AuthLoginType authLoginType;
    
    // 根据当前认证方法设置对应的AuthLoginType
    switch (_currentMethod) {
      case AuthMethod.face:
        authLoginType = AuthLoginType.faceAuth;
        break;
      case AuthMethod.idCard:
        authLoginType = AuthLoginType.IDCard;
        break;
      case AuthMethod.readerCard:
        authLoginType = AuthLoginType.readerCard;
        break;
      case AuthMethod.socialSecurityCard:
        authLoginType = AuthLoginType.socailSecurityCard;
        break;
      case AuthMethod.citizenCard:
        authLoginType = AuthLoginType.citizenCard;
        break;
      case AuthMethod.eletricSocialSecurityCard:
        authLoginType = AuthLoginType.eletricSocialSecurityCard;
        break;
      case AuthMethod.wechatQRCode:
        authLoginType = AuthLoginType.wechatQRCode;
        break;
      case AuthMethod.wechatScanQRCode:
        authLoginType = AuthLoginType.wechatScanQRCode;
        break;
      case AuthMethod.alipayQRCode:
        authLoginType = AuthLoginType.alipayQRCode;
        break;
      case AuthMethod.aliCreditQRCode:
        authLoginType = AuthLoginType.aliCreditQRCode;
        break;
      case AuthMethod.huiwenQRCode:
        authLoginType = AuthLoginType.huiwenQRCode;
        break;
      case AuthMethod.shangHaiQRCode:
        authLoginType = AuthLoginType.shangHaiQRCode;
        break;
      case AuthMethod.qrCode:
        authLoginType = AuthLoginType.readerQRCode;
        break;
      case AuthMethod.keyboardInput:
        authLoginType = AuthLoginType.keyboardInput;
        break;
      case AuthMethod.tencentTCard:
        authLoginType = AuthLoginType.tencentTCard;
        break;
      case AuthMethod.imiAuth:
        authLoginType = AuthLoginType.IMIAuth;
        break;
      case AuthMethod.takePhoto:
        authLoginType = AuthLoginType.takePhoto;
        break;
      case AuthMethod.wechatOrAlipay:
        authLoginType = AuthLoginType.wecharOrAlipay;
        break;
      case AuthMethod.alipayQRCodeCredit:
        authLoginType = AuthLoginType.alipayQRCode_credit;
        break;
      case AuthMethod.jieYueBao:
        authLoginType = AuthLoginType.jieYueBao;
        break;
    }
    
    // _tagProvider.clearTagList();
    await LightManager.instance.operateAuthType(authLoginType, true);
    
    // 打开读卡器
    final readers = _getReadersForType(authLoginType);
    if (readers?.isEmpty ?? true) {
      if (authLoginType != AuthLoginType.keyboardInput) {
        showToast('未配置读卡器设备');
      }
      return;
    }
    
    _setupReaderListeners();
    await ReaderManager.instance.changeReaders(jsonEncode(readers!));
    await ReaderManager.instance.open();
    await ReaderManager.instance.untilDeteted();
    _openedReaders = readers;
  }

  // 获取指定类型的读卡器配置
  List<HWReaderSettingData>? _getReadersForType(AuthLoginType type) {
    final readerConfig = _settingProvider.readerConfigData;
    if (readerConfig == null) return null;

    final key = AuthLoginMapReverse[type];
    if (key == null) return null;

    return readerConfig.authMap[key] ?? [];
  }

  // 获取卡片类型列表
  List<String>? _getCardTypes() {
    final cardTypes = _openedReaders?.firstOrNull?.info?.valueForKey('cardType');
    if (cardTypes == null) return null;
    
    return cardTypes.split(',')
      ..removeWhere((element) => element.trim().isEmpty);
  }

  // 设置读卡器监听器
  void _setupReaderListeners() {
    if (_isListening) return;
    
    ReaderManager.instance.controller.addListener(_readerStateListener);
    _tagProvider.addListener(_cardListener);
    _isListening = true;
  }

  // 监听读卡器状态
  void _readerStateListener() {
    // 获取当前读卡器状态 - 如果需要可以根据状态做不同处理
    final type = ReaderManager.instance.controller.type;
    debugPrint('读卡器状态变化: $type');
  }

  // 监听卡片数据
  void _cardListener() async {
    if (!(_tagProvider.readerList.isNotEmpty)) {
      if (_tagProvider.tagList.isNotEmpty) {
        ReaderManager.instance.resumeInventory();
      }
      return;
    }
    
    if (_tagProvider.type != HWTagType.addedItem) {
      ReaderManager.instance.resumeInventory();
      return;
    }
    
    try {
      final readerList = [..._tagProvider.readerList];
      if (readerList.isNotEmpty) {
        await _processCardData(readerList);
      }
    } catch (e) {
      print('处理卡片数据时发生错误: $e');
      ReaderManager.instance.resumeInventory();
    }
  }

  // 处理卡片数据
  Future<void> _processCardData(List<HWTagData> tagList) async {
    bool isNeedResume = true;
    for (var tag in tagList) {
      final barcode = tag.barCode;
      if (barcode?.isNotEmpty ?? false) {
        isNeedResume = false;
        try {
          // 检查是否为管理员卡
          final type = await SettingUtil.verifyAdminCardNum(barcode!);
          if (type != null) {
            await _cleanupAndNavigateToAdmin(type);
            break;
          } else {
            // 处理普通读者卡
            final info = HWIDCardInfo.fromJson(tag.info ?? {});
            await requestReaderInfo(
              barcode,
              tag.uid ?? '',
              info: info,
            );
            break;
          }
        } catch (e) {
          debugPrint('验证卡片时发生错误: $e');
          isNeedResume = true;
          break;
        }
      }
    }

    if (isNeedResume) {
      ReaderManager.instance.resumeInventory();
    }
  }

  // 创建登录记录
  SeaRecordData _createRecordData({
    required String barcode,
    required String uid,
    required bool isSuccess,
    HWIDCardInfo? info,
    Sip2PatronInfoData? readerInfo,
    String? errorMessage,
  }) {
    AuthLoginType authLoginType;
    switch (_currentMethod) {
      case AuthMethod.face:
        authLoginType = AuthLoginType.faceAuth;
        break;
      case AuthMethod.idCard:
        authLoginType = AuthLoginType.IDCard;
        break;
      case AuthMethod.readerCard:
        authLoginType = AuthLoginType.readerCard;
        break;
      case AuthMethod.qrCode:
        authLoginType = AuthLoginType.readerQRCode;
        break;
        case AuthMethod.socialSecurityCard:
        case AuthMethod.citizenCard:
        case AuthMethod.eletricSocialSecurityCard:
          authLoginType = AuthLoginType.socailSecurityCard;
          break;
        case AuthMethod.wechatQRCode:
        case AuthMethod.wechatScanQRCode:
        case AuthMethod.alipayQRCode:
        case AuthMethod.aliCreditQRCode:
        case AuthMethod.huiwenQRCode:
        case AuthMethod.shangHaiQRCode:
        case AuthMethod.alipayQRCodeCredit:
          authLoginType = AuthLoginType.readerQRCode;
          break;
        case AuthMethod.keyboardInput:
          authLoginType = AuthLoginType.keyboardInput;
          break;
        case AuthMethod.tencentTCard:
          authLoginType = AuthLoginType.tencentTCard;
          break;
        case AuthMethod.imiAuth:
          authLoginType = AuthLoginType.IMIAuth;
          break;
        case AuthMethod.takePhoto:
          authLoginType = AuthLoginType.takePhoto;
          break;
        case AuthMethod.wechatOrAlipay:
          authLoginType = AuthLoginType.wecharOrAlipay;
          break;
        case AuthMethod.jieYueBao:
          authLoginType = AuthLoginType.jieYueBao;
          break;
    }
    
    return SeaRecordData.fromJson({})
      ..operate_type = SeaSettingRecordMap[SeaSettingRecordType.login] ?? ''
      ..auth_type = SeaSettingAuthMap[authLoginType] ?? ''
      ..barcode = barcode
      ..uid = uid
      ..dateTime = DateTime.now().millisecondsSinceEpoch.toString()
      ..readerIdCard = info?.idnum ?? ''
      ..readerName = info?.name ?? ''
      ..result = isSuccess ? '0' : '-1'
      ..readerId = readerInfo?.PatronIdentifier ?? ''
      ..reason = errorMessage ?? '';
  }

  // 保存登录记录
  Future<void> _saveLoginRecord(SeaRecordData record) async {
    try {
      final db = await DBSettingManager.getDBInstance();
      await db.insertRecord(record);
      debugPrint('保存登录记录成功: ${record.barcode}');
    } catch (e) {
      debugPrint('保存登录记录失败: $e');
    }
  }

  // 清理资源并导航到管理页面
  Future<void> _cleanupAndNavigateToAdmin(int type) async {
    await _cleanup();
    AppNavigator.untilHome();
    SettingUtil.jumpAdminPage(type);
  }

  // 清理资源 - 考勤系统特殊处理：认证成功后保持读卡器连接和监听
  Future<void> _cleanup() async {
    // 考勤系统：认证成功后不应该关闭读卡器连接，也不应该移除监听器
    // 这样可以确保后续用户可以继续使用读卡器进行认证

    // 注释掉移除监听器的代码，保持监听器继续工作
    // _removeReaderListeners();

    // 注释掉停止扫描和关闭连接的代码，保持读卡器可用
    // await ReaderManager.instance.stopInventory();
    // if (ReaderManager.instance.isConnectReader) {
    //   await ReaderManager.instance.close();
    // }

    debugPrint('认证成功后清理完成，读卡器连接和监听器保持开启以供后续使用');
    
    AuthLoginType authLoginType;
    switch (_currentMethod) {
      case AuthMethod.face:
        authLoginType = AuthLoginType.faceAuth;
        break;
      case AuthMethod.idCard:
        authLoginType = AuthLoginType.IDCard;
        break;
      case AuthMethod.readerCard:
        authLoginType = AuthLoginType.readerCard;
        break;
      case AuthMethod.qrCode:
        authLoginType = AuthLoginType.readerQRCode;
        break;
        case AuthMethod.socialSecurityCard:
        case AuthMethod.citizenCard:
        case AuthMethod.eletricSocialSecurityCard:
          authLoginType = AuthLoginType.socailSecurityCard;
          break;
        case AuthMethod.wechatQRCode:
        case AuthMethod.wechatScanQRCode:
        case AuthMethod.alipayQRCode:
        case AuthMethod.aliCreditQRCode:
        case AuthMethod.huiwenQRCode:
        case AuthMethod.shangHaiQRCode:
        case AuthMethod.alipayQRCodeCredit:
          authLoginType = AuthLoginType.readerQRCode;
          break;
        case AuthMethod.keyboardInput:
          authLoginType = AuthLoginType.keyboardInput;
          break;
        case AuthMethod.tencentTCard:
          authLoginType = AuthLoginType.tencentTCard;
          break;
        case AuthMethod.imiAuth:
          authLoginType = AuthLoginType.IMIAuth;
          break;
        case AuthMethod.takePhoto:
          authLoginType = AuthLoginType.takePhoto;
          break;
        case AuthMethod.wechatOrAlipay:
          authLoginType = AuthLoginType.wecharOrAlipay;
          break;
        case AuthMethod.jieYueBao:
          authLoginType = AuthLoginType.jieYueBao;
          break;
    }
    
    await LightManager.instance.operateAuthType(authLoginType, false);
  }

  // 移除读卡器监听器
  void _removeReaderListeners() {
    if (!_isListening) return;
    
    ReaderManager.instance.controller.removeListener(_readerStateListener);
    _tagProvider.removeListener(_cardListener);
    _isListening = false;
  }

  // 处理错误
  void _handleError(dynamic error) {
    String errorMessage;
    
    if (error.toString().contains('信号灯超时时间已到') || 
        error.toString().contains('SocketException')) {
      errorMessage = '网络连接超时，请检查网络后重试';
    } else {
      errorMessage = error.toString().replaceAll('Exception: ', '');
    }

    _errorMessage = errorMessage;
    showToast(
      errorMessage,
      position: ToastPosition.bottom,
      duration: const Duration(seconds: 3),
    );
  }
  
  // 获取认证方法显示名称
  String _getAuthMethodDisplayName(AuthMethod method) {
    switch (method) {
      case AuthMethod.face:
        return '人脸认证';
      case AuthMethod.idCard:
        return '身份证认证';
      case AuthMethod.readerCard:
        return '读者证认证';
      case AuthMethod.qrCode:
        return '二维码认证';
      case AuthMethod.socialSecurityCard:
        return '社保卡认证';
      case AuthMethod.citizenCard:
        return '市民卡认证';
      case AuthMethod.eletricSocialSecurityCard:
        return '电子社保卡认证';
      case AuthMethod.wechatQRCode:
        return '微信二维码认证';
      case AuthMethod.wechatScanQRCode:
        return '微信扫码认证';
      case AuthMethod.alipayQRCode:
        return '支付宝二维码认证';
      case AuthMethod.aliCreditQRCode:
        return '支付宝信用二维码认证';
      case AuthMethod.huiwenQRCode:
        return '汇文二维码认证';
      case AuthMethod.shangHaiQRCode:
        return '上海二维码认证';
      case AuthMethod.keyboardInput:
        return '键盘输入认证';
      case AuthMethod.tencentTCard:
        return '腾讯T卡认证';
      case AuthMethod.imiAuth:
        return 'IMI认证';
      case AuthMethod.takePhoto:
        return '拍照认证';
      case AuthMethod.wechatOrAlipay:
        return '微信或支付宝认证';
      case AuthMethod.alipayQRCodeCredit:
        return '支付宝信用二维码认证';
      case AuthMethod.jieYueBao:
        return '借阅宝认证';
    }
  }

  @override
  void dispose() {
    _cleanup();
    super.dispose();
  }
} 