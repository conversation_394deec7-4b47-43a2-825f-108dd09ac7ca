import 'dart:async';
import 'package:flutter/foundation.dart';
import 'collection_a_service.dart';
import 'shared_scan_pool_service.dart';

/// 🔥 主机集合A服务
/// 
/// 职责：
/// 1. 从共享池同步数据到主机集合A
/// 2. 在出馆开始等特定时机触发同步
/// 3. 为主机业务逻辑提供数据集合
class MasterCollectionAService extends CollectionAService {
  static MasterCollectionAService? _instance;
  
  /// 单例模式
  static MasterCollectionAService get instance {
    _instance ??= MasterCollectionAService._internal();
    return _instance!;
  }
  
  MasterCollectionAService._internal();

  /// 共享池服务引用
  SharedScanPoolService get _sharedPool => SharedScanPoolService.instance;

  @override
  Future<List<String>> syncFromSource() async {
    try {
      debugPrint('🔄 主机从共享池同步数据...');
      
      // 从共享池获取当前所有条码
      final poolData = _sharedPool.getCurrentPool();
      final dataList = poolData.toList();
      
      debugPrint('📊 共享池当前数据: ${dataList.length}个条码');
      if (dataList.isNotEmpty) {
        debugPrint('📋 条码列表: ${dataList.take(5).join(', ')}${dataList.length > 5 ? '...' : ''}');
      }
      
      return dataList;
      
    } catch (e) {
      debugPrint('❌ 主机同步失败: $e');
      rethrow;
    }
  }

  /// 🔥 出馆开始时的同步逻辑（不清空共享池）
  Future<void> syncOnExitStart() async {
    try {
      debugPrint('🚪 主机出馆开始：同步共享池数据到集合A');

      // 先清空集合A，确保数据隔离
      clearCollection();

      // 从共享池同步数据
      await performSync();

      debugPrint('✅ 主机出馆开始同步完成');

    } catch (e) {
      debugPrint('❌ 主机出馆开始同步失败: $e');
      rethrow;
    }
  }

  /// 🔥 出馆开始时的同步逻辑（先清空共享池）
  Future<void> syncOnExitStartWithClear() async {
    try {
      debugPrint('🚪 主机出馆开始：清空共享池并同步数据到集合A');

      // 🔥 记录清空前状态
      final poolSizeBefore = _sharedPool.poolSize;
      final collectionSizeBefore = collectionSize;
      debugPrint('📊 清空前状态: 共享池=$poolSizeBefore个, 集合A=$collectionSizeBefore个');

      // 🔥 关键：先清空共享池，确保数据隔离
      debugPrint('🧹 步骤1: 清空共享池...');
      await _sharedPool.clearPoolAndBuffer();
      debugPrint('✅ 共享池清空完成: $poolSizeBefore -> ${_sharedPool.poolSize}');

      // 清空集合A
      debugPrint('🧹 步骤2: 清空主机集合A...');
      clearCollection();
      debugPrint('✅ 集合A清空完成: $collectionSizeBefore -> $collectionSize');

      // 🔥 等待一小段时间，让RFID重新检测标签进入共享池
      debugPrint('⏳ 步骤3: 等待RFID重新检测标签...');
      await Future.delayed(const Duration(milliseconds: 500));

      // 检查RFID是否重新检测到标签
      final poolSizeAfterWait = _sharedPool.poolSize;
      debugPrint('📊 等待后状态: 共享池=$poolSizeAfterWait个');

      // 从共享池同步数据
      debugPrint('🔄 步骤4: 从共享池同步数据到集合A...');
      await performSync();

      final finalCollectionSize = collectionSize;
      debugPrint('✅ 主机出馆开始清空并同步完成');
      debugPrint('📊 最终状态: 共享池=${_sharedPool.poolSize}个, 集合A=$finalCollectionSize个');

    } catch (e) {
      debugPrint('❌ 主机出馆开始清空并同步失败: $e');
      rethrow;
    }
  }

  /// 🔥 手动触发同步（用于测试或调试）
  Future<void> manualSync() async {
    try {
      debugPrint('🔧 主机手动同步...');
      await performSync();
    } catch (e) {
      debugPrint('❌ 主机手动同步失败: $e');
      rethrow;
    }
  }

  /// 🔥 获取主机特有的状态信息
  Map<String, dynamic> getMasterStatusInfo() {
    final baseInfo = getStatusInfo();
    final sharedPoolSize = _sharedPool.poolSize;
    
    return {
      ...baseInfo,
      'service_type': 'master',
      'shared_pool_size': sharedPoolSize,
      'sync_source': 'shared_pool',
    };
  }

  /// 🔥 检查数据一致性
  bool checkDataConsistency() {
    final poolData = _sharedPool.getCurrentPool();
    final collectionData = currentCollection;
    
    // 检查集合A是否是共享池的子集
    final isSubset = collectionData.every((barcode) => poolData.contains(barcode));
    
    debugPrint('🔍 数据一致性检查:');
    debugPrint('  - 共享池: ${poolData.length}个条码');
    debugPrint('  - 集合A: ${collectionData.length}个条码');
    debugPrint('  - 一致性: ${isSubset ? '✅' : '❌'}');
    
    return isSubset;
  }

  /// 🔥 实时监听共享池变化（可选功能）
  StreamSubscription<Set<String>>? _poolChangeSubscription;

  /// 启动实时同步模式
  void startRealtimeSync() {
    stopRealtimeSync(); // 先停止之前的监听

    _poolChangeSubscription = _sharedPool.poolStream.listen((poolData) {
      debugPrint('📡 共享池变化: ${poolData.length}个条码，触发实时同步');
      performSync().catchError((e) {
        debugPrint('❌ 实时同步失败: $e');
      });
    });

    debugPrint('🚀 主机实时同步模式已启动');
  }
  
  /// 停止实时同步模式
  void stopRealtimeSync() {
    _poolChangeSubscription?.cancel();
    _poolChangeSubscription = null;
    debugPrint('⏹️ 主机实时同步模式已停止');
  }

  @override
  void dispose() {
    stopRealtimeSync();
    super.dispose();
    debugPrint('🗑️ MasterCollectionAService已释放');
  }
}
