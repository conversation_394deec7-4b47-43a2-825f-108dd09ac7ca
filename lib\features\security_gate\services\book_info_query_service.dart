import 'dart:async';
import 'dart:collection';
import 'package:flutter/foundation.dart';
import '../models/book_info.dart';
import '../models/book_scan_result.dart';
import 'sip2_book_service.dart';

/// 🔥 独立的书籍信息查询服务
/// 
/// 职责：
/// 1. 监听集合A的变化
/// 2. 异步查询新增条码的书籍信息
/// 3. 缓存查询结果
/// 4. 提供查询状态和结果
class BookInfoQueryService {
  static BookInfoQueryService? _instance;
  
  /// 单例模式
  static BookInfoQueryService get instance {
    _instance ??= BookInfoQueryService._internal();
    return _instance!;
  }
  
  BookInfoQueryService._internal() {
    _sip2BookService = Sip2BookService.instance;
  }

  /// SIP2书籍信息服务
  late final Sip2BookService _sip2BookService;

  /// 书籍信息缓存 {条码: 书籍信息}
  final Map<String, BookInfo?> _bookInfoCache = <String, BookInfo?>{};
  
  /// 正在查询的条码集合
  final Set<String> _queryingBarcodes = <String>{};
  
  /// 查询结果流控制器
  final StreamController<BookScanResult> _queryResultController = 
      StreamController<BookScanResult>.broadcast();
  
  /// 查询统计信息
  int _totalQueries = 0;
  int _successfulQueries = 0;
  int _failedQueries = 0;
  int _notFoundQueries = 0;

  /// 获取查询结果流
  Stream<BookScanResult> get queryResultStream => _queryResultController.stream;
  
  /// 获取缓存的书籍信息
  UnmodifiableMapView<String, BookInfo?> get bookInfoCache => 
      UnmodifiableMapView(_bookInfoCache);
  
  /// 获取正在查询的条码
  UnmodifiableSetView<String> get queryingBarcodes => 
      UnmodifiableSetView(_queryingBarcodes);

  /// 🔥 开始监听集合A的变化
  StreamSubscription<List<String>>? _collectionSubscription;
  
  void startMonitoring(Stream<List<String>> collectionStream) {
    stopMonitoring(); // 先停止之前的监听
    
    _collectionSubscription = collectionStream.listen((barcodes) {
      _onCollectionChanged(barcodes);
    });
    
    debugPrint('🚀 书籍信息查询服务开始监听集合A变化');
  }
  
  /// 停止监听
  void stopMonitoring() {
    _collectionSubscription?.cancel();
    _collectionSubscription = null;
    debugPrint('⏹️ 书籍信息查询服务停止监听');
  }

  /// 🔥 处理集合A变化
  void _onCollectionChanged(List<String> barcodes) {
    debugPrint('📢 集合A变化: ${barcodes.length}个条码');
    
    // 找出新增的条码（未缓存且未在查询中）
    final newBarcodes = barcodes.where((barcode) => 
        !_bookInfoCache.containsKey(barcode) && 
        !_queryingBarcodes.contains(barcode)
    ).toList();
    
    if (newBarcodes.isNotEmpty) {
      debugPrint('🔍 发现${newBarcodes.length}个新条码需要查询: ${newBarcodes.join(', ')}');
      
      // 异步查询新条码的书籍信息
      for (final barcode in newBarcodes) {
        _queryBookInfoAsync(barcode);
      }
    } else {
      debugPrint('✅ 所有条码都已查询或正在查询中');
    }
  }

  /// 🔥 异步查询单个条码的书籍信息
  Future<void> _queryBookInfoAsync(String barcode) async {
    if (_queryingBarcodes.contains(barcode)) {
      debugPrint('⚠️ 条码$barcode正在查询中，跳过重复查询');
      return;
    }

    _queryingBarcodes.add(barcode);
    _totalQueries++;
    
    try {
      debugPrint('🔍 开始查询书籍信息: $barcode');
      
      // 发送查询开始通知
      _queryResultController.add(BookScanResult(
        barcode: barcode,
        bookInfo: null,
        scanTime: DateTime.now(),
        status: BookScanStatus.processing,
      ));

      // 使用SIP2协议查询书籍信息
      final bookInfo = await _sip2BookService.getBookInfoRealTime(barcode);

      if (bookInfo != null) {
        // 查询成功
        _bookInfoCache[barcode] = bookInfo;
        _successfulQueries++;
        
        debugPrint('✅ 查询成功: $barcode - ${bookInfo.bookName}');
        
        _queryResultController.add(BookScanResult(
          barcode: barcode,
          bookInfo: bookInfo,
          scanTime: DateTime.now(),
          status: BookScanStatus.success,
        ));
        
      } else {
        // 未找到书籍信息（可能是其他馆的条码）
        _bookInfoCache[barcode] = null;
        _notFoundQueries++;
        
        debugPrint('⚠️ 未找到书籍信息: $barcode (可能是其他馆的条码)');
        
        _queryResultController.add(BookScanResult(
          barcode: barcode,
          bookInfo: null,
          scanTime: DateTime.now(),
          status: BookScanStatus.notFound,
          error: '未找到书籍信息，可能是其他馆的条码',
        ));
      }
      
    } catch (e) {
      // 查询异常
      _bookInfoCache[barcode] = null;
      _failedQueries++;
      
      debugPrint('❌ 查询异常: $barcode - $e');
      
      _queryResultController.add(BookScanResult(
        barcode: barcode,
        bookInfo: null,
        scanTime: DateTime.now(),
        status: BookScanStatus.failed,
        error: e.toString(),
      ));
      
    } finally {
      _queryingBarcodes.remove(barcode);
    }
  }

  /// 🔥 手动查询单个条码（用于测试或特殊场景）
  Future<BookInfo?> queryBookInfo(String barcode) async {
    if (barcode.isEmpty) return null;
    
    // 检查缓存
    if (_bookInfoCache.containsKey(barcode)) {
      debugPrint('📋 从缓存获取书籍信息: $barcode');
      return _bookInfoCache[barcode];
    }
    
    // 异步查询
    await _queryBookInfoAsync(barcode);
    
    // 返回查询结果
    return _bookInfoCache[barcode];
  }

  /// 获取缓存的书籍信息
  BookInfo? getCachedBookInfo(String barcode) {
    return _bookInfoCache[barcode];
  }

  /// 检查条码是否已查询
  bool isQueried(String barcode) {
    return _bookInfoCache.containsKey(barcode);
  }

  /// 检查条码是否正在查询
  bool isQuerying(String barcode) {
    return _queryingBarcodes.contains(barcode);
  }

  /// 清空缓存和查询状态
  void clearCache() {
    final cacheSize = _bookInfoCache.length;
    final queryingSize = _queryingBarcodes.length;
    
    _bookInfoCache.clear();
    _queryingBarcodes.clear();
    
    debugPrint('🧹 书籍信息缓存已清空: ${cacheSize}个缓存项，${queryingSize}个查询中');
  }

  /// 获取查询统计信息
  Map<String, dynamic> getQueryStats() {
    return {
      'total_queries': _totalQueries,
      'successful_queries': _successfulQueries,
      'failed_queries': _failedQueries,
      'not_found_queries': _notFoundQueries,
      'cache_size': _bookInfoCache.length,
      'querying_count': _queryingBarcodes.length,
      'success_rate': _totalQueries > 0 ? (_successfulQueries / _totalQueries * 100).toStringAsFixed(1) + '%' : '0%',
    };
  }

  /// 释放资源
  void dispose() {
    stopMonitoring();
    _queryResultController.close();
    clearCache();
    debugPrint('🗑️ BookInfoQueryService已释放');
  }
}
