import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/foundation.dart';
import '../lib/features/auth/services/door_auth_service.dart';
import '../lib/features/auth/services/user_info_service.dart';
import '../lib/features/auth/services/gate_context_service.dart';
import '../lib/features/auth/services/auth_service_initializer.dart';
import '../lib/features/auth/models/auth_result.dart';
import '../lib/features/security_gate/models/gate_state.dart';

void main() {
  group('认证接口替换测试', () {
    late DoorAuthService doorAuthService;
    late UserInfoService userInfoService;
    late GateContextService gateContextService;
    late AuthServiceInitializer authInitializer;

    setUpAll(() async {
      // 初始化服务
      doorAuthService = DoorAuthService.instance;
      userInfoService = UserInfoService.instance;
      gateContextService = GateContextService.instance;
      authInitializer = AuthServiceInitializer.instance;
    });

    test('门径认证服务初始化测试', () async {
      // 测试服务初始化（可能会因为网络问题失败，但不应该抛出异常）
      try {
        await authInitializer.initialize();
        expect(authInitializer.isInitialized, isTrue);
      } catch (e) {
        // 网络相关错误是可以接受的
        debugPrint('初始化失败（可能是网络问题）: $e');
        expect(e, isA<Exception>());
      }
    });

    test('闸机上下文服务状态管理测试', () {
      // 测试状态更新
      gateContextService.updateGateState(GateState.enterStarted);
      expect(gateContextService.currentState, equals(GateState.enterStarted));
      expect(gateContextService.isEnterFlow, isTrue);
      expect(gateContextService.authType, equals(1)); // 进馆

      // 测试出馆状态
      gateContextService.updateGateState(GateState.exitStarted);
      expect(gateContextService.currentState, equals(GateState.exitStarted));
      expect(gateContextService.isExitFlow, isTrue);
      expect(gateContextService.authType, equals(2)); // 出馆
    });

    test('门径认证服务配置测试', () async {
      // 测试设备MAC地址设置
      const testMac = 'AA:BB:CC:DD:EE:FF';
      await doorAuthService.setDeviceMac(testMac);
      expect(doorAuthService.deviceMac, equals(testMac));
    });

    test('用户信息服务测试', () async {
      // 创建基础认证结果
      final baseResult = AuthResult(
        method: AuthMethod.readerCard,
        status: AuthStatus.success,
        userId: '12345',
        userName: '测试用户',
        timestamp: DateTime.now(),
      );

      // 测试用户信息丰富化（跳过SIP2查询）
      final enrichedResult = await userInfoService.enrichAuthResult(
        baseResult: baseResult,
        identifier: '12345',
        skipUserInfo: true,
      );

      expect(enrichedResult.status, equals(AuthStatus.success));
      expect(enrichedResult.userId, equals('12345'));
    });

    test('认证类型判断测试', () {
      // 测试进馆流程
      gateContextService.updateGateState(GateState.enterStarted);
      expect(gateContextService.shouldFetchUserInfo(), isTrue);
      expect(gateContextService.canAuthenticate(), isTrue);

      // 测试出馆流程
      gateContextService.updateGateState(GateState.exitStarted);
      expect(gateContextService.shouldFetchUserInfo(), isFalse);
      expect(gateContextService.canAuthenticate(), isTrue);

      // 测试空闲状态
      gateContextService.updateGateState(GateState.idle);
      expect(gateContextService.canAuthenticate(), isFalse);
    });

    test('服务状态获取测试', () {
      // 测试所有服务状态
      final allStatus = authInitializer.getAllServicesStatus();
      
      expect(allStatus, isA<Map<String, dynamic>>());
      expect(allStatus['initializer_status'], isNotNull);
      expect(allStatus['door_auth_service'], isNotNull);
      expect(allStatus['user_info_service'], isNotNull);
      expect(allStatus['gate_context_service'], isNotNull);
    });

    test('错误处理测试', () async {
      // 测试无效MAC地址的处理
      gateContextService.updateGateState(GateState.enterStarted);
      
      final result = await doorAuthService.authenticate(
        identifier: 'invalid_user',
        method: AuthMethod.readerCard,
        isEnterFlow: true,
      );

      // 应该返回错误结果而不是抛出异常
      expect(result, isA<AuthResult>());
      expect(result.status, isNot(equals(AuthStatus.success)));
    });

    tearDown(() {
      // 重置服务状态
      gateContextService.reset();
    });
  });

  group('SIP2替换验证测试', () {
    test('验证SIP2调用已被替换', () {
      // 这个测试主要是确保代码编译通过，说明SIP2调用已被正确替换
      expect(DoorAuthService.instance, isNotNull);
      expect(UserInfoService.instance, isNotNull);
      expect(GateContextService.instance, isNotNull);
    });

    test('验证新认证流程完整性', () async {
      final gateContext = GateContextService.instance;

      // 模拟进馆流程
      gateContext.updateGateState(GateState.enterStarted);
      
      // 验证状态正确
      expect(gateContext.isEnterFlow, isTrue);
      expect(gateContext.authType, equals(1));
      
      // 验证认证提示信息
      final prompt = gateContext.getAuthPrompt();
      expect(prompt, contains('进入'));
    });
  });
}
