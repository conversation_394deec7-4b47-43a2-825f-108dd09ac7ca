import 'dart:async';
import 'package:flutter/material.dart';
import '../../../core/utils/window_util.dart';
import '../services/unified_api_config_manager.dart';

/// 自动保存输入框组件
/// 支持失去焦点时立即保存，文本变化时延迟保存（防抖动）
class AutoSaveTextField extends StatefulWidget {
  final String fieldKey;                    // 字段标识
  final String formType;                    // 表单类型
  final TextEditingController controller;   // 文本控制器
  final String labelText;                   // 标签文本
  final String? hintText;                   // 提示文本
  final String? Function(String?)? validator; // 验证器
  final TextInputType? keyboardType;        // 键盘类型
  final bool enabled;                       // 是否启用
  final int? maxLines;                      // 最大行数
  final int? maxLength;                     // 最大长度
  final Widget? prefixIcon;                 // 前缀图标
  final Widget? suffixIcon;                 // 后缀图标
  final bool obscureText;                   // 是否隐藏文本
  final VoidCallback? onTap;                // 点击回调
  final Function(String)? onChanged;        // 文本变化回调
  final int debounceMilliseconds;           // 防抖动延迟时间

  const AutoSaveTextField({
    Key? key,
    required this.fieldKey,
    required this.formType,
    required this.controller,
    required this.labelText,
    this.hintText,
    this.validator,
    this.keyboardType,
    this.enabled = true,
    this.maxLines = 1,
    this.maxLength,
    this.prefixIcon,
    this.suffixIcon,
    this.obscureText = false,
    this.onTap,
    this.onChanged,
    this.debounceMilliseconds = 500,
  }) : super(key: key);

  @override
  State<AutoSaveTextField> createState() => _AutoSaveTextFieldState();
}

class _AutoSaveTextFieldState extends State<AutoSaveTextField> {
  final FocusNode _focusNode = FocusNode();
  final _configManager = UnifiedApiConfigManager.instance;
  Timer? _debounceTimer;
  bool _isLoading = false;
  bool _hasLoadedInitialValue = false;

  @override
  void initState() {
    super.initState();
    
    // 监听焦点变化
    _focusNode.addListener(_onFocusChange);
    
    // 监听文本变化
    widget.controller.addListener(_onTextChange);
    
    // 页面加载时自动填充数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadFieldValue();
    });
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    _focusNode.removeListener(_onFocusChange);
    widget.controller.removeListener(_onTextChange);
    _focusNode.dispose();
    super.dispose();
  }

  /// 焦点变化处理
  void _onFocusChange() {
    if (!_focusNode.hasFocus && _hasLoadedInitialValue) {
      // 失去焦点时立即保存
      _saveFieldValue(immediate: true);
    }
  }

  /// 文本变化处理
  void _onTextChange() {
    if (!_hasLoadedInitialValue) return;
    
    // 调用外部回调
    widget.onChanged?.call(widget.controller.text);
    
    // 文本变化时延迟保存（防抖动）
    _debounceTimer?.cancel();
    _debounceTimer = Timer(Duration(milliseconds: widget.debounceMilliseconds), () {
      _saveFieldValue(immediate: false);
    });
  }

  /// 加载字段值（页面打开时反显）
  Future<void> _loadFieldValue() async {
    if (_isLoading) return;
    
    setState(() {
      _isLoading = true;
    });

    try {
      final value = await _configManager.getFormFieldValue(widget.formType, widget.fieldKey);
      
      if (value != null && value.isNotEmpty && widget.controller.text != value) {
        widget.controller.text = value;
        debugPrint('📖 反显字段值: ${widget.formType}.${widget.fieldKey} = $value');
      }
    } catch (e) {
      debugPrint('❌ 加载字段值失败: ${widget.formType}.${widget.fieldKey}, 错误: $e');
    } finally {
      setState(() {
        _isLoading = false;
        _hasLoadedInitialValue = true;
      });
    }
  }

  /// 保存字段值
  Future<void> _saveFieldValue({required bool immediate}) async {
    final value = widget.controller.text.trim();
    
    try {
      await _configManager.saveFormFieldValue(widget.formType, widget.fieldKey, value);
      
      if (immediate) {
        debugPrint('💾 立即保存: ${widget.formType}.${widget.fieldKey} = $value');
      } else {
        debugPrint('⏰ 延迟保存: ${widget.formType}.${widget.fieldKey} = $value');
      }
    } catch (e) {
      debugPrint('❌ 保存字段值失败: ${widget.formType}.${widget.fieldKey}, 错误: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: widget.controller,
      focusNode: _focusNode,
      decoration: InputDecoration(
        labelText: widget.labelText,
        hintText: widget.hintText,
        prefixIcon: widget.prefixIcon,
        suffixIcon: _isLoading 
            ? SizedBox(
                width: 16.p,
                height: 16.p,
                child: Padding(
                  padding: EdgeInsets.all(12.p),
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      Theme.of(context).primaryColor,
                    ),
                  ),
                ),
              )
            : widget.suffixIcon,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.p),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.p),
          borderSide: BorderSide(
            color: const Color(0xFFE0E0E0),
            width: 1.p,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.p),
          borderSide: BorderSide(
            color: const Color(0xFF54A0FF),
            width: 2.p,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.p),
          borderSide: BorderSide(
            color: const Color(0xFFE53E3E),
            width: 1.p,
          ),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.p),
          borderSide: BorderSide(
            color: const Color(0xFFE53E3E),
            width: 2.p,
          ),
        ),
        contentPadding: EdgeInsets.symmetric(
          horizontal: 16.p,
          vertical: 12.p,
        ),
        counterText: widget.maxLength != null ? null : '',
      ),
      validator: widget.validator,
      keyboardType: widget.keyboardType,
      enabled: widget.enabled,
      maxLines: widget.maxLines,
      maxLength: widget.maxLength,
      obscureText: widget.obscureText,
      onTap: widget.onTap,
      style: TextStyle(
        fontSize: 14.p,
        color: widget.enabled ? const Color(0xFF333333) : const Color(0xFF999999),
      ),
    );
  }
}

/// 自动保存下拉选择框组件
class AutoSaveDropdownField extends StatefulWidget {
  final String fieldKey;
  final String formType;
  final String labelText;
  final List<DropdownMenuItem<String>> items;
  final String? value;
  final Function(String?)? onChanged;
  final String? Function(String?)? validator;
  final bool enabled;

  const AutoSaveDropdownField({
    Key? key,
    required this.fieldKey,
    required this.formType,
    required this.labelText,
    required this.items,
    this.value,
    this.onChanged,
    this.validator,
    this.enabled = true,
  }) : super(key: key);

  @override
  State<AutoSaveDropdownField> createState() => _AutoSaveDropdownFieldState();
}

class _AutoSaveDropdownFieldState extends State<AutoSaveDropdownField> {
  final _configManager = UnifiedApiConfigManager.instance;
  String? _currentValue;
  bool _isLoading = false;
  bool _hasLoadedInitialValue = false;

  @override
  void initState() {
    super.initState();
    _currentValue = widget.value;
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadFieldValue();
    });
  }

  Future<void> _loadFieldValue() async {
    if (_isLoading) return;
    
    setState(() {
      _isLoading = true;
    });

    try {
      final value = await _configManager.getFormFieldValue(widget.formType, widget.fieldKey);
      
      if (value != null && value.isNotEmpty) {
        setState(() {
          _currentValue = value;
        });
        debugPrint('📖 反显下拉值: ${widget.formType}.${widget.fieldKey} = $value');
      }
    } catch (e) {
      debugPrint('❌ 加载下拉值失败: ${widget.formType}.${widget.fieldKey}, 错误: $e');
    } finally {
      setState(() {
        _isLoading = false;
        _hasLoadedInitialValue = true;
      });
    }
  }

  Future<void> _saveFieldValue(String? value) async {
    if (!_hasLoadedInitialValue) return;
    
    try {
      await _configManager.saveFormFieldValue(widget.formType, widget.fieldKey, value ?? '');
      debugPrint('💾 保存下拉值: ${widget.formType}.${widget.fieldKey} = $value');
    } catch (e) {
      debugPrint('❌ 保存下拉值失败: ${widget.formType}.${widget.fieldKey}, 错误: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return DropdownButtonFormField<String>(
      value: _currentValue,
      decoration: InputDecoration(
        labelText: widget.labelText,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.p),
        ),
        contentPadding: EdgeInsets.symmetric(
          horizontal: 16.p,
          vertical: 12.p,
        ),
      ),
      items: widget.items,
      onChanged: widget.enabled ? (value) {
        setState(() {
          _currentValue = value;
        });
        _saveFieldValue(value);
        widget.onChanged?.call(value);
      } : null,
      validator: widget.validator,
      style: TextStyle(
        fontSize: 14.p,
        color: widget.enabled ? const Color(0xFF333333) : const Color(0xFF999999),
      ),
    );
  }
}
