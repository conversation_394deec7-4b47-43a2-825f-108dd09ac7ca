import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:sea_socket/sea_socket.dart';
import '../models/auth_result.dart';

/// 用户信息获取服务
/// 由于新的门径认证接口不返回用户详细信息，
/// 这个服务负责在需要时通过SIP2获取用户详细信息
class UserInfoService {
  static UserInfoService? _instance;
  static UserInfoService get instance => _instance ??= UserInfoService._();
  UserInfoService._();

  /// 获取用户详细信息
  /// [identifier] 用户标识符（读者证号或物理卡号）
  /// [method] 认证方式
  /// [skipUserInfo] 是否跳过用户信息获取（出馆流程可能不需要）
  Future<AuthResult> enrichAuthResult({
    required AuthResult baseResult,
    String? identifier,
    bool skipUserInfo = false,
  }) async {
    // 如果认证失败或跳过用户信息获取，直接返回原结果
    if (baseResult.status != AuthStatus.success || skipUserInfo) {
      return baseResult;
    }

    // 如果没有提供标识符，使用原结果中的userId
    final userIdentifier = identifier ?? baseResult.userId;
    if (userIdentifier == null || userIdentifier.isEmpty) {
      debugPrint('无法获取用户信息：缺少用户标识符');
      return baseResult;
    }

    try {
      debugPrint('尝试获取用户详细信息: $userIdentifier');
      
      // 使用SIP2获取用户详细信息
      final readerInfo = await _fetchUserInfoFromSip2(userIdentifier);
      
      if (readerInfo != null && readerInfo.ValidPatron == 'Y') {
        // 成功获取用户信息，更新认证结果
        return AuthResult(
          method: baseResult.method,
          status: AuthStatus.success,
          userId: readerInfo.PatronIdentifier ?? userIdentifier,
          userName: readerInfo.PersonName ?? '认证用户',
          timestamp: baseResult.timestamp,
          readerInfo: readerInfo, // 保存完整的读者信息
        );
      } else {
        // 无法获取用户信息，但认证已成功，返回简化信息
        debugPrint('无法获取用户详细信息，使用简化信息');
        return AuthResult(
          method: baseResult.method,
          status: AuthStatus.success,
          userId: userIdentifier,
          userName: '认证用户',
          timestamp: baseResult.timestamp,
        );
      }
    } catch (e) {
      debugPrint('获取用户详细信息失败: $e');
      
      // 获取用户信息失败，但认证已成功，返回简化信息
      return AuthResult(
        method: baseResult.method,
        status: AuthStatus.success,
        userId: userIdentifier,
        userName: '认证用户',
        timestamp: baseResult.timestamp,
      );
    }
  }

  /// 使用SIP2获取用户信息
  Future<Sip2PatronInfoData?> _fetchUserInfoFromSip2(String identifier) async {
    try {
      // 调用SIP2服务获取用户信息
      final readerInfo = await NewSip2Request.instance.getReaderInfo(
        identifier,
        identifier,
      ).timeout(const Duration(seconds: 5)); // 缩短超时时间，避免影响认证流程
      
      return readerInfo;
    } catch (e) {
      debugPrint('SIP2用户信息请求失败: $e');
      return null;
    }
  }

  /// 批量获取用户信息（支持多种卡类型）
  Future<Sip2PatronInfoData?> _fetchUserInfoWithCardTypes(
    String identifier,
    List<String>? cardTypes,
  ) async {
    if (cardTypes?.isNotEmpty ?? false) {
      // 尝试每种卡类型
      for (String cardType in cardTypes!) {
        try {
          final readerInfo = await NewSip2Request.instance.getReaderInfo(
            identifier,
            identifier,
            CardType: cardType,
          ).timeout(const Duration(seconds: 3));
          
          if (readerInfo?.isSuccess ?? false) {
            return readerInfo;
          }
        } catch (e) {
          debugPrint('使用卡类型 $cardType 获取用户信息失败: $e');
          continue;
        }
      }
    }
    
    // 无卡类型或所有卡类型都失败时，使用默认请求
    return await _fetchUserInfoFromSip2(identifier);
  }

  /// 检查是否需要获取用户详细信息
  /// [method] 认证方式
  /// [isEnterFlow] 是否为进馆流程
  bool shouldFetchUserInfo({
    required AuthMethod method,
    required bool isEnterFlow,
  }) {
    // 进馆流程通常需要显示用户信息
    if (isEnterFlow) {
      return true;
    }
    
    // 出馆流程根据认证方式决定
    switch (method) {
      case AuthMethod.face:
        return true; // 人脸识别可能需要显示用户信息
      case AuthMethod.readerCard:
        return false; // 读者证出馆可能不需要详细信息
      default:
        return false;
    }
  }

  /// 创建简化的认证结果（不包含用户详细信息）
  AuthResult createSimpleAuthResult({
    required AuthMethod method,
    required String identifier,
    required bool isSuccess,
    String? errorMessage,
  }) {
    return AuthResult(
      method: method,
      status: isSuccess ? AuthStatus.success : AuthStatus.failureNoMatch,
      userId: isSuccess ? identifier : null,
      userName: isSuccess ? '认证用户' : null,
      errorMessage: errorMessage,
      timestamp: DateTime.now(),
    );
  }

  /// 获取服务状态
  Map<String, dynamic> getStatus() {
    return {
      'service_name': 'UserInfoService',
      'version': '1.0.0',
      'sip2_available': true, // 假设SIP2服务可用
    };
  }
}
