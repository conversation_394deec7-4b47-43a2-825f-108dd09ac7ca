import 'package:flutter/material.dart';
import '../models/silence_page_state.dart';
import '../models/book_info.dart';
import 'radar_scan_tip_card.dart';

/// 书籍检查界面内容组件
class BookCheckContent extends StatefulWidget {
  final UIContentData data;
  
  const BookCheckContent({
    Key? key,
    required this.data,
  }) : super(key: key);

  @override
  State<BookCheckContent> createState() => _BookCheckContentState();
}

class _BookCheckContentState extends State<BookCheckContent>
    with TickerProviderStateMixin {
  
  late AnimationController _slideController;
  late Animation<Offset> _slideAnimation;
  
  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimations();
  }
  
  void _initializeAnimations() {
    _slideController = AnimationController(
      duration: Duration(milliseconds: 800),
      vsync: this,
    );
    
    _slideAnimation = Tween<Offset>(
      begin: Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.elasticOut,
    ));
  }
  
  void _startAnimations() {
    _slideController.forward();
  }
  
  @override
  Widget build(BuildContext context) {
    final books = widget.data.books ?? [];
    final hasUnborrowedBooks = widget.data.extraData?['has_unborrowed_books'] as bool? ?? false;

    // 只有检测到未借书籍时才显示组件，检查通过时不显示任何内容
    if (!hasUnborrowedBooks) {
      return const SizedBox.shrink(); // 检查通过时不显示任何组件
    }

    return SlideTransition(
      position: _slideAnimation,
      child: Center(
        child: _buildUnborrowedBooksCard(books),
      ),
    );
  }

  /// 构建未借书籍卡片
  Widget _buildUnborrowedBooksCard(List<BookInfo> books) {
    final unborrowedCount = books.where((book) => !book.isBorrowed).length;

    return RadarScanTipCard(
      number: unborrowedCount,
      state: RadarScanState.detected, // 检测到未借书籍状态
    );
  }


  

  
  @override
  void dispose() {
    _slideController.dispose();
    super.dispose();
  }
}
