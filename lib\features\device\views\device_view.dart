import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../core/widgets/base_page2.dart';
import '../../../core/utils/window_util.dart';
import '../view_models/device_view_model.dart';
import '../widgets/api_config_panel.dart';
import '../widgets/device_register_form.dart';
import '../widgets/device_status_form.dart';
import '../widgets/reader_auth_form.dart';
import '../widgets/unified_config_form.dart';

class DeviceView extends StatefulWidget {
  const DeviceView({Key? key}) : super(key: key);

  @override
  State<DeviceView> createState() => _DeviceViewState();
}

class _DeviceViewState extends State<DeviceView> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this); // 增加到4个标签页

    // 初始化ViewModel
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<DeviceViewModel>().init();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BasePage2(
      topWrapper: _buildHeader(),
      mainWrapper: _buildMainContent(),
    );
  }

  Widget _buildHeader() {
    return SizedBox(
      height: 200.p,
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 20.p),
        child: Text(
          '设备管理',
          style: TextStyle(
            fontSize: 32.p,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      ),
    );
  }

  Widget _buildMainContent() {
    return Column(
      children: [
        SizedBox(height: 20.p),
        
        // API配置面板
        const ApiConfigPanel(),
        
        // Tab栏
        Container(
          margin: EdgeInsets.symmetric(horizontal: 16.p),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8.p),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: TabBar(
            controller: _tabController,
            labelColor: const Color(0xFF54A0FF),
            unselectedLabelColor: const Color(0xFF666666),
            indicatorColor: const Color(0xFF54A0FF),
            indicatorWeight: 3,
            labelStyle: TextStyle(
              fontSize: 16.p,
              fontWeight: FontWeight.w600,
            ),
            unselectedLabelStyle: TextStyle(
              fontSize: 16.p,
              fontWeight: FontWeight.normal,
            ),
            tabs: const [
              Tab(text: '设备注册'),
              Tab(text: '状态上传'),
              Tab(text: '读者认证'),
              Tab(text: 'API配置'),
            ],
          ),
        ),
        
        // Tab内容
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: const [
              DeviceRegisterForm(),
              DeviceStatusForm(),
              ReaderAuthForm(),
              UnifiedConfigForm(),
            ],
          ),
        ),
      ],
    );
  }
}
