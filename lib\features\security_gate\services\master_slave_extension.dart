import 'dart:async';
import 'package:flutter/foundation.dart';
import 'shared_scan_pool_service.dart';
import 'channel_queue_service.dart';
import 'master_slave_comm_service.dart';
import 'gate_coordinator.dart';

import '../models/gate_event.dart';

/// 主从机扩展管理器
/// 🔥 为现有GateCoordinator添加主从机支持
/// 作为现有架构的扩展，不破坏原有功能
class MasterSlaveExtension {
  static final _instance = MasterSlaveExtension._internal();
  static MasterSlaveExtension get instance => _instance;
  MasterSlaveExtension._internal();
  
  // 配置信息
  String? _channelId;
  bool _isMaster = false;
  bool _isEnabled = false;
  
  // 服务实例
  SharedScanPoolService? _sharedPool;
  ChannelQueueService? _channelQueue;
  MasterSlaveCommService? _commService;
  
  // 订阅管理
  StreamSubscription? _gateEventSubscription;
  StreamSubscription? _poolSubscription;
  StreamSubscription? _commSubscription;

  // 🔥 新增：数据变化通知流
  StreamController<List<String>>? _dataChangeController;

  // 🔥 新增：防重复调用标志
  bool _isHandlingExitStart = false;

  // 🔥 新增：持续数据获取定时器
  Timer? _dataCollectionTimer;
  List<String> _collectedBarcodes = [];

  /// 获取当前配置
  bool get isMaster => _isMaster;
  String? get channelId => _channelId;
  bool get isEnabled => _isEnabled;

  /// 🔥 新增：获取数据变化流
  Stream<List<String>> get dataChangeStream =>
      _dataChangeController?.stream ?? const Stream.empty();

  /// 🔥 新增：创建或重新创建数据变化通知流
  void _createDataChangeController() {
    // 如果已存在且未关闭，先关闭
    if (_dataChangeController != null && !_dataChangeController!.isClosed) {
      _dataChangeController!.close();
    }

    // 创建新的StreamController
    _dataChangeController = StreamController<List<String>>.broadcast();
    debugPrint('✅ 数据变化通知流已创建');
  }

  /// 🔥 新增：检查数据流状态
  bool get isDataStreamReady =>
      _dataChangeController != null && !_dataChangeController!.isClosed;

  /// 🔥 新增：获取详细状态信息
  Map<String, dynamic> getDetailedStatus() {
    return {
      'enabled': _isEnabled,
      'channel_id': _channelId,
      'is_master': _isMaster,
      'data_stream_ready': isDataStreamReady,
      'data_stream_exists': _dataChangeController != null,
      'data_stream_closed': _dataChangeController?.isClosed ?? true,
      'shared_pool_size': _sharedPool?.poolSize ?? 0,
      'queue_size': _channelQueue?.queueSize ?? 0,
      'comm_connected': _commService?.isConnected ?? false,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// 启用主从机扩展
  Future<void> enable({
    required String channelId,
    required bool isMaster,
    String? slaveAddress,
    String? masterAddress,
    int port = 8888,
  }) async {
    try {
      // 🔥 防止重复初始化，但允许StreamController重新创建
      if (_isEnabled && _channelId == channelId && _isMaster == isMaster) {
        debugPrint('⚠️ 主从机扩展已启用相同配置: $channelId');

        // 检查StreamController状态，如果有问题则重新创建
        if (!isDataStreamReady) {
          debugPrint('🔄 检测到数据流异常，重新创建StreamController');
          _createDataChangeController();
        } else {
          debugPrint('✅ 配置和数据流都正常，跳过重复初始化');
          return;
        }
        return;
      }
      
      // 如果已启用但配置不同，先禁用
      if (_isEnabled) {
        debugPrint('🔄 检测到配置变更，先禁用现有扩展...');
        disable();
      }
      
      debugPrint('启用主从机扩展: $channelId (${isMaster ? "主机" : "从机"})');

      _channelId = channelId;
      _isMaster = isMaster;

      // 🔥 新增：创建数据变化通知流
      _createDataChangeController();

      // 1. 初始化共享扫描池
      _sharedPool = SharedScanPoolService.instance;
      await _sharedPool!.initializeWithRFID();
      
      // 2. 初始化通道队列
      _channelQueue = ChannelQueueService(channelId);

      // 🔥 新增：监听通道队列数据变化
      _poolSubscription = _channelQueue!.queueStream.listen((data) {
        // 立即通知数据变化
        _notifyCollectedBarcodes(data.toList());
      });

      // 3. 初始化通信服务
      _commService = MasterSlaveCommService.instance;
      if (isMaster) {
        // 🔥 优化：主机模式不需要从机地址
        await _commService!.configureAsMaster(
          port: port,
        );
      } else if (!isMaster && masterAddress != null) {
        await _commService!.configureAsSlave(
          masterAddress: masterAddress,
          port: port,
        );
      }
      
      // 4. 监听现有GateCoordinator的事件
      _setupGateCoordinatorIntegration();
      
      _isEnabled = true;
      debugPrint('主从机扩展启用成功');
      
    } catch (e) {
      debugPrint('启用主从机扩展失败: $e');
      rethrow;
    }
  }

  /// 禁用主从机扩展
  void disable() {
    debugPrint('禁用主从机扩展');

    _gateEventSubscription?.cancel();
    _poolSubscription?.cancel();
    _commSubscription?.cancel();

    _gateEventSubscription = null;
    _poolSubscription = null;
    _commSubscription = null;

    // 🔥 新增：清理数据变化通知流
    if (_dataChangeController != null && !_dataChangeController!.isClosed) {
      _dataChangeController!.close();
    }
    _dataChangeController = null;

    _channelQueue?.dispose();
    _commService?.disconnect();

    _channelQueue = null;
    _commService = null;
    _sharedPool = null;

    _isEnabled = false;
    _channelId = null;
  }

  /// 集成现有GateCoordinator
  void _setupGateCoordinatorIntegration() {
    try {
      final coordinator = GateCoordinator.instance;

      // 监听闸机事件
      _gateEventSubscription = coordinator.eventStream.listen(
        (event) {
          _handleGateEvent(event);
        },
        onError: (error) {
          debugPrint('[$_channelId] 监听闸机事件出错: $error');
        },
      );

      debugPrint('[$_channelId] 已集成现有GateCoordinator，开始监听事件');
    } catch (e) {
      debugPrint('[$_channelId] 集成GateCoordinator失败: $e');
    }
  }

  /// 处理闸机事件
  void _handleGateEvent(GateEvent event) {
    if (!_isEnabled) return;

    debugPrint('[$_channelId] 收到闸机事件: ${event.type}');

    switch (event.type) {
      case GateEvent.exitStart: // 使用正确的事件类型常量
        _handleExitStart();
        break;
      case 'position_reached': // 自定义事件类型（需要手动触发）
        _handlePositionReached();
        break;
      case GateEvent.exitEnd: // 使用正确的事件类型常量
        _handleExitEnd();
        break;
    }
  }

  /// 🔥 重构：处理出馆开始（请求-响应模式）
  Future<void> _handleExitStart() async {
    // 调用新的请求-响应方法，但不使用返回值（保持兼容性）
    await handleExitStart();
  }

  /// 🔥 优化：公共的出馆开始处理方法（请求-响应模式，防重复调用）
  Future<List<String>> handleExitStart() async {
    // 🔥 防重复调用检查
    if (_isHandlingExitStart) {
      debugPrint('⚠️ [$_channelId] 出馆开始处理中，跳过重复调用');
      return [];
    }

    if (!_isEnabled) {
      debugPrint('⚠️ 主从机扩展未启用');
      return [];
    }

    _isHandlingExitStart = true;
    try {
      debugPrint('[$_channelId] 主从机扩展：处理出馆开始（等待到位信号）');

      // 🔥 修复：出馆开始时主机和从机都只等待到位信号，不做任何数据操作
      if (_isMaster) {
        debugPrint('🖥️ [$_channelId] 主机模式：等待到位信号');
      } else {
        debugPrint('📱 [$_channelId] 从机模式：等待到位信号');
      }

      // 🔥 修复：不清空任何数据，不启动任何数据获取，等到出馆到位时再处理
      debugPrint('⏳ [$_channelId] 出馆开始完成，等待到位信号...');
      return [];
    } finally {
      _isHandlingExitStart = false;
    }
  }



  /// 🔥 新增：启动持续数据获取
  Future<void> _startContinuousDataCollection() async {
    try {
      // 启动持续数据获取定时器
      _startDataCollectionTimer();
      debugPrint('✅ [$_channelId] 持续数据获取已启动');
    } catch (e) {
      debugPrint('❌ [$_channelId] 启动持续数据获取失败: $e');
    }
  }

  /// 🔥 新增：启动数据收集定时器
  void _startDataCollectionTimer() {
    // 停止之前的定时器
    _dataCollectionTimer?.cancel();

    // 启动新的定时器，每500ms获取一次数据
    _dataCollectionTimer = Timer.periodic(const Duration(milliseconds: 500), (timer) async {
      try {
        await _collectDataFromMaster();
      } catch (e) {
        debugPrint('❌ [$_channelId] 定时数据收集失败: $e');
      }
    });

    debugPrint('🔄 [$_channelId] 数据收集定时器已启动（每500ms）');
  }

  /// 🔥 新增：从主机收集数据
  Future<void> _collectDataFromMaster() async {
    try {
      final currentData = await _commService?.requestCurrentData(
        channelId: _channelId,
        timeout: const Duration(seconds: 3),
      );

      if (currentData != null) {
        // 合并新数据，去重
        final newBarcodes = currentData.where((barcode) => !_collectedBarcodes.contains(barcode)).toList();

        if (newBarcodes.isNotEmpty) {
          _collectedBarcodes.addAll(newBarcodes);
          debugPrint('📥 [$_channelId] 收集到新数据: ${newBarcodes.length}个条码 - $newBarcodes');
          debugPrint('📊 [$_channelId] 累计收集: ${_collectedBarcodes.length}个条码 - $_collectedBarcodes');

          // 通知数据变化
          _notifyCollectedBarcodes(_collectedBarcodes.toList());
        }
      }
    } catch (e) {
      // 静默处理错误，避免日志过多
      if (e.toString().contains('timeout') || e.toString().contains('未连接')) {
        // 网络问题，静默处理
      } else {
        debugPrint('⚠️ [$_channelId] 数据收集异常: $e');
      }
    }
  }

  /// 🔥 新增：停止持续数据获取
  void stopContinuousDataCollection() {
    _dataCollectionTimer?.cancel();
    _dataCollectionTimer = null;
    debugPrint('⏹️ [$_channelId] 持续数据获取已停止');
  }

  /// 🔥 新增：获取当前收集的数据
  List<String> getCurrentCollectedData() {
    return _collectedBarcodes.toList();
  }

  /// 🔥 修复：清空收集的数据并通知页面更新
  void clearCollectedData() {
    _collectedBarcodes.clear();
    debugPrint('🧹 [$_channelId] 已清空收集的数据');

    // 🔥 关键修复：通知页面更新显示为0
    _notifyCollectedBarcodes([]);
    debugPrint('📱 [$_channelId] 已通知页面更新显示为0个条码');
  }

  /// 🔥 新增：从机请求主机清空共享池（单独方法）
  Future<bool> requestMasterClearPool() async {
    if (_isMaster) {
      debugPrint('⚠️ 主机不能请求清空');
      return false;
    }

    try {
      final success = await _commService?.requestMasterClearPool(
        channelId: _channelId,
        timeout: const Duration(seconds: 5),
        maxRetries: 2,
      );

      return success == true;
    } catch (e) {
      debugPrint('❌ [$_channelId] 请求主机清空共享池失败: $e');
      return false;
    }
  }

  /// 🔥 新增：启动5秒数据收集（出馆到位时调用）
  Future<void> startDataCollectionFor5Seconds() async {
    if (_isMaster) {
      debugPrint('⚠️ 主机不需要启动数据收集');
      return;
    }

    try {
      debugPrint('🔄 [$_channelId] 启动5秒数据收集...');

      // 启动持续数据获取
      await _startContinuousDataCollection();

      // 5秒后自动停止
      Timer(const Duration(seconds: 5), () {
        debugPrint('⏰ [$_channelId] 5秒数据收集完成，自动停止');
        stopContinuousDataCollection();
      });

    } catch (e) {
      debugPrint('❌ [$_channelId] 启动5秒数据收集失败: $e');
    }
  }

  /// 🔥 移除备用策略：只依赖共享池单一路径

  /// 处理到达指定位置
  void _handlePositionReached() {
    debugPrint('[$_channelId] 主从机扩展：处理到达指定位置');
    
    _channelQueue?.stopCollecting();
    
    // 延迟1秒后获取收集的条码
    Timer(const Duration(seconds: 1), () {
      final collectedBarcodes = _channelQueue?.getProcessQueue() ?? <String>{};
      debugPrint('[$_channelId] 收集到${collectedBarcodes.length}个条码: $collectedBarcodes');
      
      // 这里可以将收集到的条码传递给现有的书籍检查逻辑
      _notifyCollectedBarcodes(collectedBarcodes.toList());
    });
  }

  /// 🔥 增强：处理出馆结束（全面清空本地集合和关键信息）
  void _handleExitEnd() {
    debugPrint('[$_channelId] 主从机扩展：处理出馆结束');

    try {
      // 1. 清空队列
      debugPrint('🧹 [$_channelId] 清空队列...');
      _channelQueue?.clearQueue();

      // 2. 停止持续数据获取
      debugPrint('⏹️ [$_channelId] 停止持续数据获取...');
      stopContinuousDataCollection();

      // 🔥 新增：3. 清空收集的数据并通知页面更新
      debugPrint('🧹 [$_channelId] 清空收集的数据并通知页面更新...');
      clearCollectedData();

      // 🔥 新增：4. 重置数据流状态
      debugPrint('🔄 [$_channelId] 重置数据流状态...');
      _resetDataStreamState();

      debugPrint('✅ [$_channelId] 出馆结束处理完成：已停止数据获取并清空所有本地状态');

    } catch (e) {
      debugPrint('❌ [$_channelId] 出馆结束处理失败: $e');
    }
  }

  /// 🔥 新增：重置数据流状态
  void _resetDataStreamState() {
    try {
      // 重新创建数据变化通知流，确保下次使用时状态干净
      if (_dataChangeController != null && !_dataChangeController!.isClosed) {
        _dataChangeController!.close();
      }
      _createDataChangeController();
      debugPrint('✅ [$_channelId] 数据流状态已重置');
    } catch (e) {
      debugPrint('⚠️ [$_channelId] 重置数据流状态失败: $e');
    }
  }

  /// 通知收集到的条码
  void _notifyCollectedBarcodes(List<String> barcodes) {
    debugPrint('[$_channelId] 通知收集到的条码: $barcodes');

    // 🔥 新增：安全的数据流通知
    try {
      if (_dataChangeController != null && !_dataChangeController!.isClosed) {
        _dataChangeController!.add(barcodes);
        debugPrint('✅ [$_channelId] 数据流通知发送成功: ${barcodes.length}个条码');
      } else {
        debugPrint('⚠️ [$_channelId] 数据流通知跳过: StreamController不可用');
      }
    } catch (e) {
      debugPrint('❌ [$_channelId] 数据流通知失败: $e');
      // 不重新抛出异常，避免中断整个流程
    }
  }

  /// 手动触发出馆开始事件
  void triggerExitStart() {
    if (_isEnabled) {
      _handleExitStart();
    }
  }

  /// 手动触发位置到达事件（用于测试）
  void triggerPositionReached() {
    if (_isEnabled) {
      _handlePositionReached();
    }
  }

  /// 手动添加条码到共享池（用于测试）
  void addTestBarcode(String barcode) {
    if (_isEnabled && _isMaster) {
      _sharedPool?.addBarcode(barcode);
    }
  }

  /// 获取当前状态信息
  Map<String, dynamic> getStatus() {
    return {
      'enabled': _isEnabled,
      'channel_id': _channelId,
      'is_master': _isMaster,
      'shared_pool_size': _sharedPool?.poolSize ?? 0,
      'queue_size': _channelQueue?.queueSize ?? 0,
      'comm_connected': _commService?.isConnected ?? false,
    };
  }

  /// 🔥 修改：获取收集到的条码（从机数据收集使用）
  List<String> getCollectedBarcodes() {
    // 🔥 修复：返回从机收集的条码数据，而不是队列数据
    return _collectedBarcodes.toList();
  }

  /// 获取共享池条码（用于调试）
  List<String> getSharedPoolBarcodes() {
    return _sharedPool?.getCurrentPool().toList() ?? [];
  }

  /// 清理资源
  void dispose() {
    disable();
  }
}

/// 简化的配置类
class MasterSlaveConfig {
  final String channelId;
  final bool isMaster;
  final String? slaveAddress;
  final String? masterAddress;
  final int port;

  MasterSlaveConfig({
    required this.channelId,
    required this.isMaster,
    this.slaveAddress,
    this.masterAddress,
    this.port = 8888,
  });

  /// 从JSON创建
  factory MasterSlaveConfig.fromJson(Map<String, dynamic> json) {
    return MasterSlaveConfig(
      channelId: json['channel_id'] ?? '',
      isMaster: json['is_master'] ?? false,
      slaveAddress: json['slave_address'],
      masterAddress: json['master_address'],
      port: json['port'] ?? 8888,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'channel_id': channelId,
      'is_master': isMaster,
      'slave_address': slaveAddress,
      'master_address': masterAddress,
      'port': port,
    };
  }

  /// 验证配置
  bool isValid() {
    if (channelId.isEmpty) return false;

    if (isMaster) {
      return slaveAddress != null && slaveAddress!.isNotEmpty;
    } else {
      return masterAddress != null && masterAddress!.isNotEmpty;
    }
  }

  @override
  String toString() {
    return 'MasterSlaveConfig(channelId: $channelId, isMaster: $isMaster, '
           'slaveAddress: $slaveAddress, masterAddress: $masterAddress, port: $port)';
  }
}
