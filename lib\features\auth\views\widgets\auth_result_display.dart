import 'package:flutter/material.dart';
import '../../models/auth_result.dart';

class AuthResultDisplay extends StatelessWidget {
  final AuthResult result;
  
  const AuthResultDisplay({
    Key? key, 
    required this.result,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: _getResultColor(result.status),
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            _getResultTitle(result.status),
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 16.0),
          if (result.status == AuthStatus.success) ...[
            Text(
              '姓名: ${result.userName}',
              style: const TextStyle(fontSize: 18, color: Colors.white),
            ),
            const SizedBox(height: 8.0),
            Text(
              '证号: ${result.userId}',
              style: const TextStyle(fontSize: 18, color: Colors.white),
            ),
            const SizedBox(height: 8.0),
            Text(
              '认证时间: ${_formatDateTime(result.timestamp)}',
              style: const TextStyle(fontSize: 16, color: Colors.white70),
            ),
            const SizedBox(height: 16.0),
            const Text(
              '请通行',
              style: TextStyle(
                fontSize: 28, 
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Color _getResultColor(AuthStatus status) {
    switch (status) {
      case AuthStatus.success:
        return Colors.green.shade700;
      case AuthStatus.failureNoMatch:
        return Colors.orange.shade700;
      case AuthStatus.failureError:
        return Colors.red.shade700;
      case AuthStatus.failureTimeout:
        return Colors.deepOrange.shade700;
    }
  }

  String _getResultTitle(AuthStatus status) {
    switch (status) {
      case AuthStatus.success:
        return '认证成功';
      case AuthStatus.failureNoMatch:
        return '未检测到录入人脸';
      case AuthStatus.failureError:
        return '认证失败';
      case AuthStatus.failureTimeout:
        return '认证超时';
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
           '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}:${dateTime.second.toString().padLeft(2, '0')}';
  }
} 