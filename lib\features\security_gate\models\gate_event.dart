import 'book_info.dart';

/// 闸机事件模型
class GateEvent {
  final String type;
  final Map<String, dynamic> data;
  final DateTime timestamp;
  
  GateEvent({
    required this.type,
    required this.data,
  }) : timestamp = DateTime.now();
  
  // 事件类型常量
  static const String enterStart = 'enter_start';
  static const String enterEnd = 'enter_end';
  static const String exitStart = 'exit_start';
  static const String exitEnd = 'exit_end';
  static const String authSuccess = 'auth_success';
  static const String authFailed = 'auth_failed';
  // 🔥 新增：出馆认证事件类型
  static const String exitAuthSuccess = 'exit_auth_success';
  static const String exitAuthFailed = 'exit_auth_failed';
  static const String bookScanned = 'book_scanned';
  static const String showBooks = 'show_books';
  static const String exitAllowed = 'exit_allowed';
  static const String exitBlocked = 'exit_blocked';
  static const String tailgating = 'tailgating';
  static const String doorBlocked = 'door_blocked';
  static const String error = 'error';
  static const String stateChanged = 'state_changed';
  static const String pageClear = 'page_clear';
  
  /// 获取事件的显示名称
  String get displayName {
    switch (type) {
      case enterStart:
        return '进馆开始';
      case enterEnd:
        return '进馆结束';
      case exitStart:
        return '出馆开始';
      case exitEnd:
        return '出馆结束';
      case authSuccess:
        return '认证成功';
      case authFailed:
        return '认证失败';
      case exitAuthSuccess:
        return '出馆认证成功';
      case exitAuthFailed:
        return '出馆认证失败';
      case bookScanned:
        return '书籍扫描';
      case showBooks:
        return '显示书籍';
      case exitAllowed:
        return '允许出馆';
      case exitBlocked:
        return '禁止出馆';
      case tailgating:
        return '尾随检测';
      case doorBlocked:
        return '通道阻挡';
      case error:
        return '系统错误';
      case stateChanged:
        return '状态变更';
      default:
        return '未知事件';
    }
  }
  
  /// 获取事件消息
  String get message {
    return data['message'] as String? ?? displayName;
  }
  
  /// 获取用户名（如果有）
  String? get userName {
    return data['user_name'] as String?;
  }
  
  /// 获取用户ID（如果有）
  String? get userId {
    return data['user_id'] as String?;
  }
  
  /// 获取书籍列表（如果有）
  List<Map<String, dynamic>>? get books {
    final booksData = data['books'];
    if (booksData is List) {
      return List<Map<String, dynamic>>.from(booksData);
    }
    return null;
  }
  
  /// 获取未借书籍列表（如果有）
  List<Map<String, dynamic>>? get unborrowedBooks {
    final booksData = data['unborrowed_books'];
    if (booksData is List) {
      return List<Map<String, dynamic>>.from(booksData);
    }
    return null;
  }
  
  /// 获取书籍条码（如果有）
  String? get bookBarcode {
    return data['barcode'] as String?;
  }
  
  /// 获取扫描总数（如果有）
  int? get totalCount {
    return data['total_count'] as int?;
  }
  
  /// 是否为进馆相关事件
  bool get isEnterEvent {
    return [enterStart, enterEnd, authSuccess, authFailed].contains(type);
  }
  
  /// 是否为出馆相关事件
  bool get isExitEvent {
    return [exitStart, exitEnd, bookScanned, showBooks, exitAllowed, exitBlocked].contains(type);
  }
  
  /// 是否为错误事件
  bool get isErrorEvent {
    return [authFailed, exitBlocked, tailgating, doorBlocked, error].contains(type);
  }
  
  /// 是否为成功事件
  bool get isSuccessEvent {
    return [authSuccess, exitAllowed].contains(type);
  }
  
  /// 将事件转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'data': data,
      'timestamp': timestamp.toIso8601String(),
      'display_name': displayName,
      'message': message,
    };
  }
  
  /// 从JSON创建事件
  factory GateEvent.fromJson(Map<String, dynamic> json) {
    return GateEvent(
      type: json['type'] as String,
      data: Map<String, dynamic>.from(json['data'] as Map),
    );
  }
  
  /// 创建进馆开始事件
  factory GateEvent.createEnterStart({String? message}) {
    return GateEvent(
      type: enterStart,
      data: {'message': message ?? '请进行身份认证'},
    );
  }

  /// 创建认证成功事件
  factory GateEvent.createAuthSuccess({
    required String userName,
    String? userId,
    String? message,
  }) {
    return GateEvent(
      type: authSuccess,
      data: {
        'message': message ?? '认证成功，请通过',
        'user_name': userName,
        if (userId != null) 'user_id': userId,
      },
    );
  }

  /// 创建认证失败事件
  factory GateEvent.createAuthFailed({String? message}) {
    return GateEvent(
      type: authFailed,
      data: {'message': message ?? '认证失败，请重试'},
    );
  }

  /// 🔥 新增：创建出馆认证成功事件
  factory GateEvent.createExitAuthSuccess({
    required String userName,
    String? userId,
    String? message,
  }) {
    return GateEvent(
      type: exitAuthSuccess,
      data: {
        'message': message ?? '出馆认证成功，开始扫描',
        'user_name': userName,
        if (userId != null) 'user_id': userId,
      },
    );
  }

  /// 🔥 新增：创建出馆认证失败事件
  factory GateEvent.createExitAuthFailed({String? message}) {
    return GateEvent(
      type: exitAuthFailed,
      data: {'message': message ?? '出馆认证失败，但继续扫描流程'},
    );
  }

  /// 创建出馆开始事件
  factory GateEvent.createExitStart({String? message}) {
    return GateEvent(
      type: exitStart,
      data: {'message': message ?? '正在扫描随身物品'},
    );
  }

  /// 创建书籍扫描事件
  factory GateEvent.createBookScanned({
    required String barcode,
    required int totalCount,
  }) {
    return GateEvent(
      type: bookScanned,
      data: {
        'barcode': barcode,
        'total_count': totalCount,
        'message': '扫描到书籍: $barcode',
      },
    );
  }

  /// 🔥 新增：创建增强书籍扫描事件（包含书籍信息）
  factory GateEvent.createEnhancedBookScanned({
    required String barcode,
    BookInfo? bookInfo,
    required int totalCount,
    String? status,
  }) {
    return GateEvent(
      type: bookScanned,
      data: {
        'barcode': barcode,
        'book_info': bookInfo?.toJson(),
        'total_count': totalCount,
        'status': status,
        'message': bookInfo != null
            ? '扫描到书籍: ${bookInfo.bookName} (${bookInfo.borrowStatusText})'
            : '扫描到书籍: $barcode (获取信息中...)',
      },
    );
  }

  /// 创建显示书籍事件
  factory GateEvent.createShowBooks({
    required List<Map<String, dynamic>> books,
    String? message,
  }) {
    return GateEvent(
      type: showBooks,
      data: {
        'books': books,
        'message': message ?? '书籍检查完成',
      },
    );
  }

  /// 创建允许出馆事件
  factory GateEvent.createExitAllowed({
    String? message,
    List<Map<String, dynamic>>? books,
  }) {
    return GateEvent(
      type: exitAllowed,
      data: {
        'message': message ?? '检查通过，请通过',
        if (books != null) 'books': books,
      },
    );
  }

  /// 创建禁止出馆事件
  factory GateEvent.createExitBlocked({
    required String message,
    required List<Map<String, dynamic>> books,
    required List<Map<String, dynamic>> unborrowedBooks,
  }) {
    return GateEvent(
      type: exitBlocked,
      data: {
        'message': message,
        'books': books,
        'unborrowed_books': unborrowedBooks,
      },
    );
  }

  /// 🔥 新增：创建页面清空事件
  factory GateEvent.createPageClear() {
    return GateEvent(
      type: pageClear,
      data: {},
    );
  }
  
  @override
  String toString() {
    return 'GateEvent{type: $type, message: $message, timestamp: $timestamp}';
  }
}
