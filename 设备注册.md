# 设备注册

## OpenAPI Specification

```yaml
openapi: 3.0.1
info:
  title: ''
  description: ''
  version: 1.0.0
paths:
  /v1/api/door/register:
    post:
      summary: 设备注册
      deprecated: false
      description: ''
      tags:
        - 清华门径
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                deviceMac:
                  type: string
                  description: 设备MAC
                deviceName:
                  type: 'null'
                  description: 设备名称
                doorCode:
                  type: 'null'
                  description: 门编码
                doorName:
                  type: 'null'
                  description: 门名称
                areaCode:
                  type: 'null'
                  description: 区域编码
                areaName:
                  type: 'null'
                  description: 区域名称
                libId:
                  type: 'null'
                  description: 馆编码
                libName:
                  type: 'null'
                  description: 馆名称
              required:
                - deviceMac
                - deviceName
                - doorCode
                - doorName
                - areaCode
                - areaName
                - libId
                - libName
              x-apifox-orders:
                - deviceMac
                - deviceName
                - doorCode
                - doorName
                - areaCode
                - areaName
                - libId
                - libName
            example:
              deviceMac: FFFFFFFF
              deviceName: null
              doorCode: null
              doorName: null
              areaCode: null
              areaName: null
              libId: null
              libName: null
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  errorCode:
                    type: integer
                    description: 0成功, 其他失败
                  message:
                    type: string
                    description: 提示信息
                  data:
                    type: 'null'
                required:
                  - errorCode
                  - message
                  - data
                x-apifox-orders:
                  - errorCode
                  - message
                  - data
              example:
                errorCode: 0
                message: 注册成功!
                data: null
          headers: {}
          x-apifox-name: 成功
      security: []
      x-apifox-folder: 清华门径
      x-apifox-status: developing
      x-run-in-apifox: https://app.apifox.com/web/project/2805692/apis/api-325292363-run
components:
  schemas: {}
  securitySchemes: {}
servers:
  - url: http://************:9000
    description: 开发环境
security: []

```