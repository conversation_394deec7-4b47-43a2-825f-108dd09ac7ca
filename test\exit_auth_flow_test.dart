import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/foundation.dart';
import '../lib/features/security_gate/models/gate_state.dart';
import '../lib/features/security_gate/models/gate_event.dart';
import '../lib/features/auth/models/auth_result.dart';

void main() {
  group('出馆认证流程测试', () {
    test('GateState扩展方法测试', () {
      // 测试进馆状态判断
      expect(GateState.enterStarted.isEnterState, isTrue);
      expect(GateState.enterScanning.isEnterState, isTrue);
      expect(GateState.enterOpening.isEnterState, isTrue);
      expect(GateState.enterOver.isEnterState, isTrue);
      
      // 测试出馆状态判断
      expect(GateState.exitStarted.isExitState, isTrue);
      expect(GateState.exitScanning.isExitState, isTrue);
      expect(GateState.exitChecking.isExitState, isTrue);
      expect(GateState.exitOver.isExitState, isTrue);
      
      // 测试非相关状态
      expect(GateState.idle.isEnterState, isFalse);
      expect(GateState.idle.isExitState, isFalse);
      expect(GateState.error.isEnterState, isFalse);
      expect(GateState.error.isExitState, isFalse);
    });

    test('出馆认证事件创建测试', () {
      // 测试出馆认证成功事件
      final successEvent = GateEvent.createExitAuthSuccess(
        userName: '测试用户',
        userId: '12345',
        message: '出馆认证成功',
      );
      
      expect(successEvent.type, equals(GateEvent.exitAuthSuccess));
      expect(successEvent.userName, equals('测试用户'));
      expect(successEvent.userId, equals('12345'));
      expect(successEvent.message, equals('出馆认证成功'));
      expect(successEvent.displayName, equals('出馆认证成功'));

      // 测试出馆认证失败事件
      final failedEvent = GateEvent.createExitAuthFailed(
        message: '出馆认证失败，但继续扫描',
      );
      
      expect(failedEvent.type, equals(GateEvent.exitAuthFailed));
      expect(failedEvent.message, equals('出馆认证失败，但继续扫描'));
      expect(failedEvent.displayName, equals('出馆认证失败'));
    });

    test('认证结果状态测试', () {
      // 测试成功认证结果
      final successResult = AuthResult(
        method: AuthMethod.readerCard,
        status: AuthStatus.success,
        userId: '12345',
        userName: '测试用户',
        timestamp: DateTime.now(),
      );
      
      expect(successResult.status, equals(AuthStatus.success));
      expect(successResult.userId, equals('12345'));
      expect(successResult.userName, equals('测试用户'));

      // 测试失败认证结果
      final failedResult = AuthResult(
        method: AuthMethod.readerCard,
        status: AuthStatus.failureNoMatch,
        errorMessage: '用户不存在',
        timestamp: DateTime.now(),
      );
      
      expect(failedResult.status, equals(AuthStatus.failureNoMatch));
      expect(failedResult.errorMessage, equals('用户不存在'));
    });

    test('出馆流程状态转换测试', () {
      // 模拟出馆流程的状态转换
      var currentState = GateState.idle;
      
      // 1. 出馆开始 -> 显示认证界面
      currentState = GateState.exitStarted;
      expect(currentState.isExitState, isTrue);
      expect(currentState, equals(GateState.exitStarted));
      
      // 2. 认证完成 -> 开始RFID扫描
      currentState = GateState.exitScanning;
      expect(currentState.isExitState, isTrue);
      expect(currentState, equals(GateState.exitScanning));
      
      // 3. 到达位置 -> 检查书籍
      currentState = GateState.exitChecking;
      expect(currentState.isExitState, isTrue);
      expect(currentState, equals(GateState.exitChecking));
      
      // 4. 出馆结束 -> 回到空闲
      currentState = GateState.idle;
      expect(currentState.isExitState, isFalse);
      expect(currentState, equals(GateState.idle));
    });

    test('事件类型常量测试', () {
      // 验证新增的事件类型常量
      expect(GateEvent.exitAuthSuccess, equals('exit_auth_success'));
      expect(GateEvent.exitAuthFailed, equals('exit_auth_failed'));
      
      // 验证现有事件类型常量
      expect(GateEvent.enterStart, equals('enter_start'));
      expect(GateEvent.exitStart, equals('exit_start'));
      expect(GateEvent.authSuccess, equals('auth_success'));
      expect(GateEvent.authFailed, equals('auth_failed'));
    });

    test('出馆认证流程完整性测试', () {
      // 这个测试验证出馆认证流程的完整性
      
      // 1. 初始状态
      var state = GateState.idle;
      expect(state.isExitState, isFalse);
      
      // 2. 出馆开始 - 应该显示认证界面
      state = GateState.exitStarted;
      expect(state.isExitState, isTrue);
      
      // 创建出馆开始事件
      final exitStartEvent = GateEvent.createExitStart();
      expect(exitStartEvent.type, equals(GateEvent.exitStart));
      
      // 3. 模拟认证成功
      final authSuccessResult = AuthResult(
        method: AuthMethod.face,
        status: AuthStatus.success,
        userId: 'test123',
        userName: '张三',
        timestamp: DateTime.now(),
      );
      
      // 创建出馆认证成功事件
      final exitAuthSuccessEvent = GateEvent.createExitAuthSuccess(
        userName: authSuccessResult.userName!,
        userId: authSuccessResult.userId,
      );
      
      expect(exitAuthSuccessEvent.type, equals(GateEvent.exitAuthSuccess));
      expect(exitAuthSuccessEvent.userName, equals('张三'));
      
      // 4. 认证成功后应该开始RFID扫描
      state = GateState.exitScanning;
      expect(state.isExitState, isTrue);
      
      // 5. 到达指定位置
      state = GateState.exitChecking;
      expect(state.isExitState, isTrue);
      
      // 6. 出馆结束
      state = GateState.idle;
      expect(state.isExitState, isFalse);
      
      debugPrint('✅ 出馆认证流程完整性测试通过');
    });

    test('认证失败但继续流程测试', () {
      // 测试出馆认证失败但仍然继续RFID扫描的场景
      
      // 1. 出馆开始
      var state = GateState.exitStarted;
      expect(state.isExitState, isTrue);
      
      // 2. 模拟认证失败
      final authFailedResult = AuthResult(
        method: AuthMethod.readerCard,
        status: AuthStatus.failureNoMatch,
        errorMessage: '读者证无效',
        timestamp: DateTime.now(),
      );
      
      // 3. 创建出馆认证失败事件
      final exitAuthFailedEvent = GateEvent.createExitAuthFailed(
        message: authFailedResult.errorMessage,
      );
      
      expect(exitAuthFailedEvent.type, equals(GateEvent.exitAuthFailed));
      expect(exitAuthFailedEvent.message, equals('读者证无效'));
      
      // 4. 即使认证失败，也应该继续RFID扫描流程
      state = GateState.exitScanning;
      expect(state.isExitState, isTrue);
      
      debugPrint('✅ 认证失败但继续流程测试通过');
    });
  });
}
