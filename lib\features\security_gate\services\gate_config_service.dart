import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:seasetting/seasetting.dart' as seasetting;
import 'master_slave_extension.dart';

/// 闸机配置服务
/// 管理主从机配置和初始化
class GateConfigService {
  static final _instance = GateConfigService._internal();
  static GateConfigService get instance => _instance;
  GateConfigService._internal();

  // 当前配置
  MasterSlaveConfig? _currentConfig;

  // 扩展管理器
  MasterSlaveExtension? _extension;

  /// 获取当前配置
  MasterSlaveConfig? get currentConfig => _currentConfig;

  /// 获取扩展管理器
  MasterSlaveExtension? get extension => _extension;

  /// 初始化主从机扩展
  Future<void> initialize() async {
    try {
      debugPrint('开始初始化主从机扩展...');

      // 加载配置
      await _loadConfig();

      if (_currentConfig == null) {
        debugPrint('未找到配置，主从机扩展未启用');
        return;
      }

      // 启用扩展
      _extension = MasterSlaveExtension.instance;
      await _extension!.enable(
        channelId: _currentConfig!.channelId,
        isMaster: _currentConfig!.isMaster,
        slaveAddress: _currentConfig!.slaveAddress,
        masterAddress: _currentConfig!.masterAddress,
        port: _currentConfig!.port,
      );

      debugPrint('主从机扩展初始化完成');
      debugPrint('配置信息: $_currentConfig');

    } catch (e) {
      debugPrint('初始化主从机扩展失败: $e');
      rethrow;
    }
  }

  /// 🔥 优化：配置为主机模式（不需要从机地址）
  Future<void> configureAsMaster({
    required String channelId,
    int port = 8888,
  }) async {
    try {
      debugPrint('配置为主机模式: $channelId，监听端口: $port');

      _currentConfig = MasterSlaveConfig(
        channelId: channelId,
        isMaster: true,
        slaveAddress: null, // 🔥 不再需要从机地址
        port: port,
      );

      await _saveConfig();
      await _reinitialize();

    } catch (e) {
      debugPrint('配置主机模式失败: $e');
      rethrow;
    }
  }

  /// 配置为从机模式
  Future<void> configureAsSlave({
    required String channelId,
    required String masterAddress,
    int port = 8888,
  }) async {
    try {
      debugPrint('配置为从机模式: $channelId');

      _currentConfig = MasterSlaveConfig(
        channelId: channelId,
        isMaster: false,
        masterAddress: masterAddress,
        port: port,
      );

      await _saveConfig();
      await _reinitialize();

    } catch (e) {
      debugPrint('配置从机模式失败: $e');
      rethrow;
    }
  }

  /// 重新初始化系统
  Future<void> _reinitialize() async {
    // 清理旧的扩展
    _extension?.disable();
    _extension = null;

    // 重新初始化
    await initialize();
  }

  /// 加载配置 - 从 seasetting 数据库获取完整的安全闸机配置
  Future<void> _loadConfig() async {
    try {
      // 🔥 从 seasetting 数据库加载主从机配置
      await _loadMasterSlaveConfig();

      // 🔥 从 seasetting 数据库加载串口硬件配置
      await _loadSerialConfig();

    } catch (e) {
      debugPrint('从 seasetting 数据库加载配置失败: $e');
    }
  }

  /// 加载主从机配置
  Future<void> _loadMasterSlaveConfig() async {
    try {
      final settingProvider = Get.context?.read<seasetting.SettingProvider>();

      if (settingProvider != null) {
        final configs = await settingProvider.getMasterSlaveConfigs();

        if (configs.isNotEmpty) {
          // 使用最新的配置（最后一个）
          final seasettingConfig = configs.last;

          // 转换为本地配置格式
          _currentConfig = MasterSlaveConfig(
            channelId: seasettingConfig.channelId,
            isMaster: seasettingConfig.isMaster,
            slaveAddress: seasettingConfig.slaveAddress,
            masterAddress: seasettingConfig.masterAddress,
            port: seasettingConfig.port,
          );

          debugPrint('从 seasetting 数据库加载主从机配置成功: ${_currentConfig!.channelId}');
          debugPrint('配置详情: ${seasettingConfig.isMaster ? "主机" : "从机"}模式');
        } else {
          debugPrint('seasetting 数据库中没有找到主从机配置');
        }
      } else {
        debugPrint('无法获取 SettingProvider');
      }
    } catch (e) {
      debugPrint('加载主从机配置失败: $e');
    }
  }

  /// 加载串口硬件配置（统一通过 SettingProvider，与主从机配置保持一致）
  Future<void> _loadSerialConfig() async {
    try {
      // 🔥 统一通过 SettingProvider 加载串口配置
      final settingProvider = Get.context?.read<seasetting.SettingProvider>();

      if (settingProvider != null) {
        // 尝试通过 SettingProvider 获取串口配置
        final configLoaded = await _tryLoadSerialConfigFromProvider(settingProvider);

        if (configLoaded) {
          debugPrint('✅ 通过 SettingProvider 加载串口配置成功');
        } else {
          debugPrint('⚠️ 未从 SettingProvider 获取到串口配置');
        }
      } else {
        debugPrint('⚠️ SettingProvider 未就绪，无法加载串口配置');
      }
    } catch (e) {
      debugPrint('❌ 加载串口配置失败: $e');
    }
  }

  /// 尝试通过 SettingProvider 加载串口配置
  Future<bool> _tryLoadSerialConfigFromProvider(seasetting.SettingProvider provider) async {
    try {
      // 尝试通过动态调用获取 SecurityGateConfigs（兼容不同版本的 SettingProvider）
      dynamic result;
      try {
        result = await (provider as dynamic).getSettingBy('SecurityGateConfigs');
      } catch (e) {
        // 如果 getSettingBy 不存在，尝试其他方法
        debugPrint('getSettingBy 方法不存在，尝试其他方法: $e');
        return false;
      }

      if (result != null && result.isNotEmpty) {
        final configsJson = result.first;
        final configsData = jsonDecode(configsJson) as Map<String, dynamic>;

        // 查找启用的配置
        for (final entry in configsData.entries) {
          if (entry.value is Map<String, dynamic>) {
            final config = entry.value as Map<String, dynamic>;
            if (config['enabled'] == true) {
              final serialConfig = config['serialConfig'] as Map<String, dynamic>?;
              if (serialConfig != null) {
                final portName = serialConfig['gatePort'];
                final baudRate = serialConfig['baudRate'];

                if (portName != null && baudRate != null) {
                  debugPrint('📡 从 SettingProvider 获取串口配置: $portName @ $baudRate');
                  // 这里可以存储配置或传递给其他服务
                  return true;
                }
              }
            }
          }
        }
      }

      return false;
    } catch (e) {
      debugPrint('通过 SettingProvider 获取串口配置失败: $e');
      return false;
    }
  }

  /// 保存配置到 seasetting 数据库
  Future<void> _saveConfig() async {
    if (_currentConfig == null) return;

    try {
      final settingProvider = Get.context?.read<seasetting.SettingProvider>();

      if (settingProvider != null) {
        // 转换为 seasetting 配置格式
        final seasettingConfig = seasetting.MasterSlaveConfig(
          channelId: _currentConfig!.channelId,
          isMaster: _currentConfig!.isMaster,
          slaveAddress: _currentConfig!.slaveAddress,
          masterAddress: _currentConfig!.masterAddress,
          port: _currentConfig!.port,
        );

        // 保存到数据库
        settingProvider.changeMasterSlaveConfigData([seasettingConfig]);
        debugPrint('配置已保存到 seasetting 数据库');
      } else {
        debugPrint('无法获取 SettingProvider，保存配置失败');
      }
    } catch (e) {
      debugPrint('保存配置到数据库失败: $e');
    }
  }

  /// 获取系统状态
  Map<String, dynamic> getSystemStatus() {
    return {
      'config': _currentConfig?.toJson(),
      'extension_status': _extension?.getStatus(),
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// 清理资源
  void dispose() {
    _extension?.dispose();
    _extension = null;
  }
}
