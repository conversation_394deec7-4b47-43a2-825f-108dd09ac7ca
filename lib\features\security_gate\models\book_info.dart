/// 书籍信息模型
class BookInfo {
  final String barcode;
  final String bookName;
  final String? author;
  final String? isbn;
  final bool isBorrowed;
  final DateTime? borrowDate;
  final DateTime? returnDate;
  final String? borrowerName;
  final String? borrowerId;
  
  BookInfo({
    required this.barcode,
    required this.bookName,
    this.author,
    this.isbn,
    required this.isBorrowed,
    this.borrowDate,
    this.returnDate,
    this.borrowerName,
    this.borrowerId,
  });
  
  /// 从JSON创建书籍信息
  factory BookInfo.fromJson(Map<String, dynamic> json) {
    return BookInfo(
      barcode: json['barcode'] as String? ?? '',
      bookName: json['book_name'] as String? ?? json['bookName'] as String? ?? '未知书名',
      author: json['author'] as String?,
      isbn: json['isbn'] as String?,
      isBorrowed: json['is_borrowed'] as bool? ?? json['borrowFlag'] as bool? ?? false,
      borrowDate: json['borrow_date'] != null && json['borrow_date'] is String
          ? DateTime.tryParse(json['borrow_date'] as String)
          : null,
      returnDate: json['return_date'] != null && json['return_date'] is String
          ? DateTime.tryParse(json['return_date'] as String)
          : null,
      borrowerName: json['borrower_name'] as String?,
      borrowerId: json['borrower_id'] as String?,
    );
  }
  
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'barcode': barcode,
      'book_name': bookName,
      'author': author,
      'isbn': isbn,
      'is_borrowed': isBorrowed,
      'borrow_date': borrowDate?.toIso8601String(),
      'return_date': returnDate?.toIso8601String(),
      'borrower_name': borrowerName,
      'borrower_id': borrowerId,
    };
  }
  
  /// 获取借阅状态显示文本
  String get borrowStatusText {
    return isBorrowed ? '已借' : '未借';
  }
  
  /// 获取借阅状态颜色
  String get borrowStatusColor {
    return isBorrowed ? 'green' : 'red';
  }
  
  /// 获取书籍显示标题
  String get displayTitle {
    if (author != null && author!.isNotEmpty) {
      return '$bookName - $author';
    }
    return bookName;
  }
  
  /// 获取借阅信息
  String get borrowInfo {
    if (!isBorrowed) {
      return '未借阅';
    }
    
    String info = '已借阅';
    if (borrowerName != null && borrowerName!.isNotEmpty) {
      info += ' - $borrowerName';
    }
    if (borrowDate != null) {
      final dateStr = '${borrowDate!.year}-${borrowDate!.month.toString().padLeft(2, '0')}-${borrowDate!.day.toString().padLeft(2, '0')}';
      info += ' ($dateStr)';
    }
    return info;
  }
  
  /// 是否逾期
  bool get isOverdue {
    if (!isBorrowed || returnDate == null) return false;
    return DateTime.now().isAfter(returnDate!);
  }
  
  /// 剩余天数
  int? get remainingDays {
    if (!isBorrowed || returnDate == null) return null;
    final now = DateTime.now();
    final difference = returnDate!.difference(now).inDays;
    return difference;
  }
  
  /// 获取逾期信息
  String? get overdueInfo {
    if (!isBorrowed || returnDate == null) return null;
    
    final remaining = remainingDays;
    if (remaining == null) return null;
    
    if (remaining < 0) {
      return '已逾期 ${-remaining} 天';
    } else if (remaining == 0) {
      return '今日到期';
    } else if (remaining <= 3) {
      return '还有 $remaining 天到期';
    }
    return null;
  }
  
  /// 复制并修改属性
  BookInfo copyWith({
    String? barcode,
    String? bookName,
    String? author,
    String? isbn,
    bool? isBorrowed,
    DateTime? borrowDate,
    DateTime? returnDate,
    String? borrowerName,
    String? borrowerId,
  }) {
    return BookInfo(
      barcode: barcode ?? this.barcode,
      bookName: bookName ?? this.bookName,
      author: author ?? this.author,
      isbn: isbn ?? this.isbn,
      isBorrowed: isBorrowed ?? this.isBorrowed,
      borrowDate: borrowDate ?? this.borrowDate,
      returnDate: returnDate ?? this.returnDate,
      borrowerName: borrowerName ?? this.borrowerName,
      borrowerId: borrowerId ?? this.borrowerId,
    );
  }
  
  @override
  String toString() {
    return 'BookInfo{barcode: $barcode, bookName: $bookName, isBorrowed: $isBorrowed}';
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BookInfo && other.barcode == barcode;
  }
  
  @override
  int get hashCode => barcode.hashCode;
}

/// 书籍检查结果
class BookCheckResult {
  final List<BookInfo> books;
  final List<BookInfo> borrowedBooks;
  final List<BookInfo> unborrowedBooks;
  final bool hasUnborrowedBooks;
  final int totalCount;
  
  BookCheckResult({
    required this.books,
  }) : borrowedBooks = books.where((book) => book.isBorrowed).toList(),
       unborrowedBooks = books.where((book) => !book.isBorrowed).toList(),
       hasUnborrowedBooks = books.any((book) => !book.isBorrowed),
       totalCount = books.length;
  
  /// 获取检查结果摘要
  String get summary {
    if (totalCount == 0) {
      return '未检测到书籍';
    }
    
    if (hasUnborrowedBooks) {
      return '检测到 $totalCount 本书籍，其中 ${unborrowedBooks.length} 本未借阅';
    } else {
      return '检测到 $totalCount 本书籍，均已借阅';
    }
  }
  
  /// 是否允许通过
  bool get allowPass {
    return !hasUnborrowedBooks;
  }
  
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'books': books.map((book) => book.toJson()).toList(),
      'borrowed_books': borrowedBooks.map((book) => book.toJson()).toList(),
      'unborrowed_books': unborrowedBooks.map((book) => book.toJson()).toList(),
      'has_unborrowed_books': hasUnborrowedBooks,
      'total_count': totalCount,
      'summary': summary,
      'allow_pass': allowPass,
    };
  }
}
