import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/utils/window_util.dart';
import '../../../core/widgets/custom_button.dart';
import '../view_models/device_view_model.dart';
import 'auto_save_text_field.dart';

class DeviceRegisterForm extends StatefulWidget {
  const DeviceRegisterForm({Key? key}) : super(key: key);

  @override
  State<DeviceRegisterForm> createState() => _DeviceRegisterFormState();
}

class _DeviceRegisterFormState extends State<DeviceRegisterForm> {
  final _formKey = GlobalKey<FormState>();

  // 控制器 - 用于自动保存输入框
  final _deviceMacController = TextEditingController();
  final _deviceNameController = TextEditingController();
  final _doorCodeController = TextEditingController();
  final _doorNameController = TextEditingController();
  final _areaCodeController = TextEditingController();
  final _areaNameController = TextEditingController();
  final _libIdController = TextEditingController();
  final _libNameController = TextEditingController();
  final _locationController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _ipAddressController = TextEditingController();
  final _portController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // 监听ViewModel变化，同步数据到控制器
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final viewModel = context.read<DeviceViewModel>();
      _syncFromViewModel(viewModel);
    });
  }

  @override
  void dispose() {
    _deviceMacController.dispose();
    _deviceNameController.dispose();
    _doorCodeController.dispose();
    _doorNameController.dispose();
    _areaCodeController.dispose();
    _areaNameController.dispose();
    _libIdController.dispose();
    _libNameController.dispose();
    _locationController.dispose();
    _descriptionController.dispose();
    _ipAddressController.dispose();
    _portController.dispose();
    super.dispose();
  }

  void _syncFromViewModel(DeviceViewModel viewModel) {
    final data = viewModel.registerData;
    _deviceMacController.text = data.deviceMac;
    _deviceNameController.text = data.deviceName ?? '';
    _doorCodeController.text = data.doorCode ?? '';
    _doorNameController.text = data.doorName ?? '';
    _areaCodeController.text = data.areaCode ?? '';
    _areaNameController.text = data.areaName ?? '';
    _libIdController.text = data.libId ?? '';
    _libNameController.text = data.libName ?? '';
  }

  void _syncToViewModel() {
    final viewModel = context.read<DeviceViewModel>();
    viewModel.updateRegisterData(
      deviceMac: _deviceMacController.text.trim(),
      deviceName: _deviceNameController.text.trim().isEmpty ? null : _deviceNameController.text.trim(),
      doorCode: _doorCodeController.text.trim().isEmpty ? null : _doorCodeController.text.trim(),
      doorName: _doorNameController.text.trim().isEmpty ? null : _doorNameController.text.trim(),
      areaCode: _areaCodeController.text.trim().isEmpty ? null : _areaCodeController.text.trim(),
      areaName: _areaNameController.text.trim().isEmpty ? null : _areaNameController.text.trim(),
      libId: _libIdController.text.trim().isEmpty ? null : _libIdController.text.trim(),
      libName: _libNameController.text.trim().isEmpty ? null : _libNameController.text.trim(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<DeviceViewModel>(
      builder: (context, viewModel, child) {
        return Card(
          margin: EdgeInsets.all(16.p),
          child: Padding(
            padding: EdgeInsets.all(24.p),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 标题
                  Text(
                    '设备注册',
                    style: TextStyle(
                      fontSize: 24.p,
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF333333),
                    ),
                  ),
                  SizedBox(height: 24.p),
                  
                  // 表单字段
                  _buildFormFields(),
                  
                  SizedBox(height: 32.p),
                  
                  // 注册按钮
                  SizedBox(
                    width: double.infinity,
                    child: CustomButton.filled(
                      text: viewModel.isRegisterLoading ? '注册中...' : '注册设备',
                      onTap: viewModel.isRegisterLoading ? null : () {
                        if (_formKey.currentState!.validate()) {
                          _syncToViewModel();
                          viewModel.registerDevice();
                        }
                      },
                      disabled: viewModel.isRegisterLoading,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildFormFields() {
    return Column(
      children: [
        // 第一行：设备MAC（必填）和设备名称
        Row(
          children: [
            Expanded(
              child: AutoSaveTextField(
                fieldKey: 'device_mac',
                formType: 'device_register',
                controller: _deviceMacController,
                labelText: '设备MAC地址 *',
                hintText: '请输入设备MAC地址',
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return '设备MAC地址不能为空';
                  }
                  return null;
                },
              ),
            ),
            SizedBox(width: 16.p),
            Expanded(
              child: AutoSaveTextField(
                fieldKey: 'device_name',
                formType: 'device_register',
                controller: _deviceNameController,
                labelText: '设备名称',
                hintText: '请输入设备名称',
              ),
            ),
          ],
        ),
        
        SizedBox(height: 16.p),
        
        // 第二行：门编码和门名称
        Row(
          children: [
            Expanded(
              child: AutoSaveTextField(
                fieldKey: 'door_code',
                formType: 'device_register',
                controller: _doorCodeController,
                labelText: '门编码',
                hintText: '请输入门编码',
              ),
            ),
            SizedBox(width: 16.p),
            Expanded(
              child: AutoSaveTextField(
                fieldKey: 'door_name',
                formType: 'device_register',
                controller: _doorNameController,
                labelText: '门名称',
                hintText: '请输入门名称',
              ),
            ),
          ],
        ),
        
        SizedBox(height: 16.p),
        
        // 第三行：区域编码和区域名称
        Row(
          children: [
            Expanded(
              child: AutoSaveTextField(
                fieldKey: 'area_code',
                formType: 'device_register',
                controller: _areaCodeController,
                labelText: '区域编码',
                hintText: '请输入区域编码',
              ),
            ),
            SizedBox(width: 16.p),
            Expanded(
              child: AutoSaveTextField(
                fieldKey: 'area_name',
                formType: 'device_register',
                controller: _areaNameController,
                labelText: '区域名称',
                hintText: '请输入区域名称',
              ),
            ),
          ],
        ),

        SizedBox(height: 16.p),

        // 第四行：馆编码和馆名称
        Row(
          children: [
            Expanded(
              child: AutoSaveTextField(
                fieldKey: 'lib_id',
                formType: 'device_register',
                controller: _libIdController,
                labelText: '馆编码',
                hintText: '请输入馆编码',
              ),
            ),
            SizedBox(width: 16.p),
            Expanded(
              child: AutoSaveTextField(
                fieldKey: 'lib_name',
                formType: 'device_register',
                controller: _libNameController,
                labelText: '馆名称',
                hintText: '请输入馆名称',
              ),
            ),
          ],
        ),

        SizedBox(height: 16.p),

        // 第五行：位置和描述
        Row(
          children: [
            Expanded(
              child: AutoSaveTextField(
                fieldKey: 'location',
                formType: 'device_register',
                controller: _locationController,
                labelText: '安装位置',
                hintText: '请输入设备安装位置',
              ),
            ),
            SizedBox(width: 16.p),
            Expanded(
              child: AutoSaveTextField(
                fieldKey: 'description',
                formType: 'device_register',
                controller: _descriptionController,
                labelText: '设备描述',
                hintText: '请输入设备描述信息',
                maxLines: 2,
              ),
            ),
          ],
        ),

        SizedBox(height: 16.p),

        // 第六行：IP地址和端口
        Row(
          children: [
            Expanded(
              child: AutoSaveTextField(
                fieldKey: 'ip_address',
                formType: 'device_register',
                controller: _ipAddressController,
                labelText: 'IP地址',
                hintText: '请输入设备IP地址',
                keyboardType: TextInputType.number,
              ),
            ),
            SizedBox(width: 16.p),
            Expanded(
              child: AutoSaveTextField(
                fieldKey: 'port',
                formType: 'device_register',
                controller: _portController,
                labelText: '端口号',
                hintText: '请输入端口号',
                keyboardType: TextInputType.number,
              ),
            ),
          ],
        ),
      ],
    );
  }

}
