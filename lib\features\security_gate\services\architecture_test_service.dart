import 'dart:async';
import 'package:flutter/foundation.dart';
import 'shared_scan_pool_service.dart';
import 'master_collection_a_service.dart';
import 'slave_collection_a_service.dart';
import 'book_info_query_service.dart';
import 'master_slave_extension.dart';

/// 🔥 新架构测试和验证服务
/// 
/// 用于测试和验证新的三层架构是否正常工作
class ArchitectureTestService {
  static ArchitectureTestService? _instance;
  
  static ArchitectureTestService get instance {
    _instance ??= ArchitectureTestService._internal();
    return _instance!;
  }
  
  ArchitectureTestService._internal();

  /// 🔥 完整的架构测试
  Future<Map<String, dynamic>> runFullArchitectureTest() async {
    final results = <String, dynamic>{};
    
    try {
      debugPrint('🧪 开始完整架构测试...');
      
      // 1. 测试共享池基础功能
      results['shared_pool_test'] = await _testSharedPool();
      
      // 2. 测试主机集合A服务
      results['master_collection_test'] = await _testMasterCollectionA();
      
      // 3. 测试从机集合A服务
      results['slave_collection_test'] = await _testSlaveCollectionA();
      
      // 4. 测试书籍查询服务
      results['book_query_test'] = await _testBookInfoQuery();
      
      // 5. 测试完整数据流
      results['full_dataflow_test'] = await _testFullDataFlow();
      
      results['overall_success'] = true;
      debugPrint('✅ 完整架构测试完成');
      
    } catch (e) {
      results['overall_success'] = false;
      results['error'] = e.toString();
      debugPrint('❌ 完整架构测试失败: $e');
    }
    
    return results;
  }

  /// 测试共享池基础功能
  Future<Map<String, dynamic>> _testSharedPool() async {
    final result = <String, dynamic>{};
    
    try {
      debugPrint('🧪 测试共享池基础功能...');
      
      final sharedPool = SharedScanPoolService.instance;
      
      // 清空共享池
      sharedPool.clearPool();
      result['initial_size'] = sharedPool.poolSize;
      
      // 添加测试条码
      final testBarcodes = ['TEST001', 'TEST002', 'TEST003'];
      for (final barcode in testBarcodes) {
        sharedPool.addBarcode(barcode);
      }
      
      result['after_add_size'] = sharedPool.poolSize;
      result['expected_size'] = testBarcodes.length;
      result['add_success'] = result['after_add_size'] == result['expected_size'];
      
      // 获取数据
      final poolData = sharedPool.getCurrentPool();
      result['retrieved_data'] = poolData.toList();
      result['data_match'] = testBarcodes.every((barcode) => poolData.contains(barcode));
      
      result['success'] = result['add_success'] && result['data_match'];
      
      debugPrint('✅ 共享池测试完成: ${result['success']}');
      
    } catch (e) {
      result['success'] = false;
      result['error'] = e.toString();
      debugPrint('❌ 共享池测试失败: $e');
    }
    
    return result;
  }

  /// 测试主机集合A服务
  Future<Map<String, dynamic>> _testMasterCollectionA() async {
    final result = <String, dynamic>{};
    
    try {
      debugPrint('🧪 测试主机集合A服务...');
      
      final masterCollection = MasterCollectionAService.instance;
      final sharedPool = SharedScanPoolService.instance;
      
      // 准备测试数据
      sharedPool.clearPool();
      final testBarcodes = ['MASTER001', 'MASTER002'];
      for (final barcode in testBarcodes) {
        sharedPool.addBarcode(barcode);
      }
      
      // 清空集合A
      masterCollection.clearCollection();
      result['initial_collection_size'] = masterCollection.collectionSize;
      
      // 同步数据
      await masterCollection.performSync();
      result['after_sync_size'] = masterCollection.collectionSize;
      result['expected_size'] = testBarcodes.length;
      result['sync_success'] = result['after_sync_size'] == result['expected_size'];
      
      // 检查数据一致性
      result['data_consistency'] = masterCollection.checkDataConsistency();
      
      result['success'] = result['sync_success'] && result['data_consistency'];
      
      debugPrint('✅ 主机集合A测试完成: ${result['success']}');
      
    } catch (e) {
      result['success'] = false;
      result['error'] = e.toString();
      debugPrint('❌ 主机集合A测试失败: $e');
    }
    
    return result;
  }

  /// 测试从机集合A服务
  Future<Map<String, dynamic>> _testSlaveCollectionA() async {
    final result = <String, dynamic>{};
    
    try {
      debugPrint('🧪 测试从机集合A服务...');
      
      final slaveCollection = SlaveCollectionAService.instance;
      
      // 检查主从机连接状态
      result['master_connection'] = slaveCollection.checkMasterConnection();
      
      // 清空集合A
      slaveCollection.clearCollection();
      result['initial_collection_size'] = slaveCollection.collectionSize;
      
      // 尝试同步数据（可能失败，取决于主从机配置）
      try {
        await slaveCollection.performSync();
        result['sync_attempted'] = true;
        result['after_sync_size'] = slaveCollection.collectionSize;
      } catch (e) {
        result['sync_attempted'] = false;
        result['sync_error'] = e.toString();
      }
      
      result['success'] = result['sync_attempted'] || !result['master_connection'];
      
      debugPrint('✅ 从机集合A测试完成: ${result['success']}');
      
    } catch (e) {
      result['success'] = false;
      result['error'] = e.toString();
      debugPrint('❌ 从机集合A测试失败: $e');
    }
    
    return result;
  }

  /// 测试书籍查询服务
  Future<Map<String, dynamic>> _testBookInfoQuery() async {
    final result = <String, dynamic>{};
    
    try {
      debugPrint('🧪 测试书籍查询服务...');
      
      final bookQueryService = BookInfoQueryService.instance;
      
      // 清空缓存
      bookQueryService.clearCache();
      result['initial_cache_size'] = bookQueryService.bookInfoCache.length;
      
      // 创建测试流
      final testController = StreamController<List<String>>();
      bookQueryService.startMonitoring(testController.stream);
      
      // 发送测试数据
      final testBarcodes = ['BOOK001', 'BOOK002'];
      testController.add(testBarcodes);
      
      // 等待查询完成
      await Future.delayed(const Duration(milliseconds: 1000));
      
      result['querying_count'] = bookQueryService.queryingBarcodes.length;
      result['cache_size'] = bookQueryService.bookInfoCache.length;
      result['query_stats'] = bookQueryService.getQueryStats();
      
      // 停止监听
      bookQueryService.stopMonitoring();
      testController.close();
      
      result['success'] = true;
      
      debugPrint('✅ 书籍查询服务测试完成');
      
    } catch (e) {
      result['success'] = false;
      result['error'] = e.toString();
      debugPrint('❌ 书籍查询服务测试失败: $e');
    }
    
    return result;
  }

  /// 测试完整数据流
  Future<Map<String, dynamic>> _testFullDataFlow() async {
    final result = <String, dynamic>{};
    
    try {
      debugPrint('🧪 测试完整数据流...');
      
      // 1. 模拟RFID扫描到条码 → 共享池
      final sharedPool = SharedScanPoolService.instance;
      sharedPool.clearPool();
      sharedPool.addBarcode('FLOW001');
      
      // 2. 主机从共享池同步到集合A
      final masterCollection = MasterCollectionAService.instance;
      masterCollection.clearCollection();
      await masterCollection.performSync();
      
      // 3. 书籍查询服务监听集合A变化
      final bookQueryService = BookInfoQueryService.instance;
      bookQueryService.clearCache();
      bookQueryService.startMonitoring(masterCollection.collectionChangeStream);
      
      // 4. 触发集合A变化
      masterCollection.addBarcode('FLOW002');
      
      // 等待处理完成
      await Future.delayed(const Duration(milliseconds: 500));
      
      result['shared_pool_size'] = sharedPool.poolSize;
      result['collection_a_size'] = masterCollection.collectionSize;
      result['book_cache_size'] = bookQueryService.bookInfoCache.length;
      
      result['success'] = result['shared_pool_size'] > 0 && 
                         result['collection_a_size'] > 0;
      
      debugPrint('✅ 完整数据流测试完成: ${result['success']}');
      
    } catch (e) {
      result['success'] = false;
      result['error'] = e.toString();
      debugPrint('❌ 完整数据流测试失败: $e');
    }
    
    return result;
  }

  /// 🔥 快速验证新架构是否正常工作
  Future<bool> quickArchitectureCheck() async {
    try {
      debugPrint('🔍 快速架构检查...');

      // 检查服务是否可用
      final sharedPool = SharedScanPoolService.instance;
      final masterCollection = MasterCollectionAService.instance;
      final slaveCollection = SlaveCollectionAService.instance;
      final bookQueryService = BookInfoQueryService.instance;

      // 基础功能测试
      sharedPool.addBarcode('QUICK_TEST');
      await masterCollection.performSync();

      final isWorking = sharedPool.poolSize > 0 &&
                       masterCollection.collectionSize > 0;

      debugPrint('🔍 快速架构检查结果: ${isWorking ? '✅ 正常' : '❌ 异常'}');

      return isWorking;
    } catch (e) {
      debugPrint('❌ 快速架构检查失败: $e');
      return false;
    }
  }

  /// 🔥 测试完整的出馆开始流程
  Future<Map<String, dynamic>> testExitStartFlow() async {
    final result = <String, dynamic>{};

    try {
      debugPrint('🧪 测试完整出馆开始流程...');

      final sharedPool = SharedScanPoolService.instance;
      final masterCollection = MasterCollectionAService.instance;
      final slaveCollection = SlaveCollectionAService.instance;
      final masterSlaveExtension = MasterSlaveExtension.instance;

      // 1. 准备测试数据
      sharedPool.clearPool();
      sharedPool.addBarcode('EXIT_TEST_001');
      sharedPool.addBarcode('EXIT_TEST_002');
      result['initial_pool_size'] = sharedPool.poolSize;

      // 2. 测试主机出馆开始流程
      if (!masterSlaveExtension.isEnabled || masterSlaveExtension.isMaster) {
        debugPrint('🖥️ 测试主机出馆开始流程...');

        // 主机清空并同步
        await masterCollection.syncOnExitStartWithClear();

        result['master_test'] = {
          'pool_size_after_clear': sharedPool.poolSize,
          'collection_size': masterCollection.collectionSize,
          'clear_and_sync_success': true,
        };

      } else {
        debugPrint('📱 测试从机出馆开始流程...');

        // 从机请求清空并同步
        await slaveCollection.syncOnExitStart();

        result['slave_test'] = {
          'collection_size': slaveCollection.collectionSize,
          'sync_success': true,
        };
      }

      // 3. 等待一段时间观察数据变化
      await Future.delayed(const Duration(seconds: 2));

      result['final_pool_size'] = sharedPool.poolSize;
      result['success'] = true;

      debugPrint('✅ 出馆开始流程测试完成');

    } catch (e) {
      result['success'] = false;
      result['error'] = e.toString();
      debugPrint('❌ 出馆开始流程测试失败: $e');
    }

    return result;
  }

  /// 🔥 测试清空时机的正确性
  Future<Map<String, dynamic>> testClearTiming() async {
    final result = <String, dynamic>{};

    try {
      debugPrint('🧪 测试清空时机...');

      final sharedPool = SharedScanPoolService.instance;
      final masterCollection = MasterCollectionAService.instance;

      // 1. 准备初始数据
      sharedPool.clearPool();
      sharedPool.addBarcode('TIMING_TEST_001');
      sharedPool.addBarcode('TIMING_TEST_002');
      masterCollection.clearCollection();
      await masterCollection.performSync();

      result['before_clear'] = {
        'pool_size': sharedPool.poolSize,
        'collection_size': masterCollection.collectionSize,
      };

      // 2. 测试清空操作
      await masterCollection.syncOnExitStartWithClear();

      result['after_clear'] = {
        'pool_size': sharedPool.poolSize,
        'collection_size': masterCollection.collectionSize,
      };

      // 3. 等待RFID重新检测
      await Future.delayed(const Duration(seconds: 1));

      result['after_wait'] = {
        'pool_size': sharedPool.poolSize,
        'collection_size': masterCollection.collectionSize,
      };

      result['clear_timing_correct'] =
          result['after_clear']['pool_size'] == 0 &&
          result['after_clear']['collection_size'] == 0;

      result['success'] = true;

      debugPrint('✅ 清空时机测试完成');

    } catch (e) {
      result['success'] = false;
      result['error'] = e.toString();
      debugPrint('❌ 清空时机测试失败: $e');
    }

    return result;
  }
}
